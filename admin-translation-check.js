#!/usr/bin/env node

/**
 * Admin Panel Translation Check
 * Checks specifically for admin panel translation completeness
 */

const fs = require('fs');

// Load Arabic translation file
function loadArabicTranslations() {
  try {
    const content = fs.readFileSync('./app/translations/ar.js', 'utf8');
    const match = content.match(/const ar = ({[\s\S]*});/);
    if (!match) {
      throw new Error('Could not parse Arabic translation file');
    }
    return eval(`(${match[1]})`);
  } catch (error) {
    console.error('Error loading Arabic translations:', error.message);
    return null;
  }
}

// Check if a nested key exists
function hasKey(obj, keyPath) {
  const keys = keyPath.split('.');
  let current = obj;
  
  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return false;
    }
  }
  
  return true;
}

// Main check function
function checkAdminTranslations() {
  console.log('🔍 فحص ترجمات صفحة الإدارة...\n');
  
  const translations = loadArabicTranslations();
  if (!translations) {
    console.log('❌ فشل في تحميل ملف الترجمة العربي');
    return false;
  }
  
  // Critical admin translation keys
  const adminKeys = [
    // Dashboard
    'admin.dashboard.title',
    'admin.dashboard.performanceOverview',
    'admin.dashboard.mostVisitedProducts',
    'admin.dashboard.latestActivities',
    'admin.dashboard.viewAll',
    'admin.dashboard.totalVisits',
    'admin.dashboard.totalClicks',
    'admin.dashboard.estimatedRevenue',
    'admin.dashboard.comparedToPrevious',
    
    // Users
    'admin.users.title',
    'admin.users.totalUsers',
    'admin.users.activeUsers',
    'admin.users.newUsers',
    'admin.users.name',
    'admin.users.email',
    'admin.users.status',
    'admin.users.actions',
    
    // Analytics
    'admin.analytics.title',
    'admin.analytics.today',
    'admin.analytics.thisWeek',
    'admin.analytics.thisMonth',
    'admin.analytics.visits',
    'admin.analytics.conversionRate',
    
    // Settings
    'admin.settings.title',
    'admin.settings.general',
    'admin.settings.notifications',
    'admin.settings.saveChanges',
    
    // Profile
    'admin.profile.title',
    'admin.profile.personalInfo',
    'admin.profile.changePassword',
    'admin.profile.saveChanges',
    
    // Common elements
    'common.buttons.save',
    'common.buttons.cancel',
    'common.buttons.edit',
    'common.buttons.delete',
    'common.labels.name',
    'common.labels.email',
    'common.labels.password',
    'common.errors.required',
    'common.errors.serverError',
    
    // Time elements
    'time.sunday',
    'time.monday',
    'time.tuesday',
    'time.wednesday',
    'time.thursday',
    'time.friday',
    'time.saturday'
  ];
  
  let missingKeys = [];
  let presentKeys = [];
  
  for (const key of adminKeys) {
    if (hasKey(translations, key)) {
      presentKeys.push(key);
    } else {
      missingKeys.push(key);
    }
  }
  
  console.log(`📊 نتائج الفحص:`);
  console.log(`✅ المفاتيح الموجودة: ${presentKeys.length}`);
  console.log(`❌ المفاتيح المفقودة: ${missingKeys.length}\n`);
  
  if (missingKeys.length > 0) {
    console.log('❌ المفاتيح المفقودة:');
    missingKeys.forEach(key => console.log(`   - ${key}`));
    console.log('');
  }
  
  // Check specific admin sections
  console.log('🔍 فحص أقسام الإدارة:\n');
  
  const sections = [
    { name: 'لوحة التحكم', key: 'admin.dashboard' },
    { name: 'إدارة المستخدمين', key: 'admin.users' },
    { name: 'التحليلات', key: 'admin.analytics' },
    { name: 'الإعدادات', key: 'admin.settings' },
    { name: 'الملف الشخصي', key: 'admin.profile' },
    { name: 'العناصر المشتركة', key: 'common' },
    { name: 'الوقت', key: 'time' }
  ];
  
  for (const section of sections) {
    const sectionExists = hasKey(translations, section.key);
    const status = sectionExists ? '✅' : '❌';
    console.log(`${status} ${section.name} (${section.key})`);
  }
  
  console.log('\n🎯 التوصيات:');
  
  if (missingKeys.length === 0) {
    console.log('✅ جميع الترجمات الأساسية لصفحة الإدارة موجودة!');
    console.log('✅ صفحة الإدارة جاهزة للاستخدام بالعربية');
  } else {
    console.log('⚠️  يوجد بعض الترجمات المفقودة');
    console.log('📝 يُنصح بإضافة الترجمات المفقودة لتحسين تجربة المستخدم');
  }
  
  console.log('\n🚀 الحالة العامة:');
  const completionRate = ((presentKeys.length / adminKeys.length) * 100).toFixed(1);
  console.log(`📈 نسبة اكتمال الترجمات: ${completionRate}%`);
  
  if (completionRate >= 90) {
    console.log('🎉 ممتاز! صفحة الإدارة مترجمة بشكل شبه كامل');
  } else if (completionRate >= 70) {
    console.log('👍 جيد! معظم ترجمات صفحة الإدارة موجودة');
  } else {
    console.log('⚠️  يحتاج إلى المزيد من العمل على الترجمات');
  }
  
  return missingKeys.length === 0;
}

// Run the check
if (require.main === module) {
  const isComplete = checkAdminTranslations();
  process.exit(isComplete ? 0 : 1);
}

module.exports = { checkAdminTranslations };