'use client';

import { useState, useEffect } from 'react';
import './commission-discounts.css';
import { useTranslation } from '../components/TranslationProvider';

export default function CommissionDiscountsPage() {
  const { t, language, direction } = useTranslation();

  // نصوص الترجمة
  const texts = {
    ar: {
      noProducts: 'لم يتم العثور على منتجات. يرجى البحث عن منتج محدد.',
      noSearchResults: 'لم يتم العثور على منتجات مطابقة لـ'
    },
    en: {
      noProducts: 'No products found. Please search for a specific product.',
      noSearchResults: 'No products found matching'
    },
    fr: {
      noProducts: 'Aucun produit trouvé. Veuillez rechercher un produit spécifique.',
      noSearchResults: 'Aucun produit trouvé correspondant à'
    },
    es: {
      noProducts: 'No se encontraron productos. Por favor busque un producto específico.',
      noSearchResults: 'No se encontraron productos que coincidan con'
    },
    uk: {
      noProducts: 'Товари не знайдено. Будь ласка, шукайте конкретний товар.',
      noSearchResults: 'Не знайдено товарів, що відповідають'
    }
  };

  // النصوص الحالية حسب اللغة
  const currentTexts = texts[language] || texts.ar;

  // حالة البحث والتصفية
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [sortBy, setSortBy] = useState(''); // 'price', 'sales', 'rating'
  
  // حالة المنتجات
  const [products, setProducts] = useState([]);
  // حالة التحميل
  const [loading, setLoading] = useState(false);
  // حالة الخطأ
  const [error, setError] = useState(null);
  // حالة التوصية
  const [recommendation, setRecommendation] = useState('');
  // حالة اللغة والعملة
  const [userLanguage, setUserLanguage] = useState('ar');
  const [currency, setCurrency] = useState('USD');
  
  // جلب اللغة والعملة من localStorage عند تحميل الصفحة
  useEffect(() => {
    const storedLanguage = localStorage.getItem('language') || 'ar';
    const storedCurrency = localStorage.getItem('currency') || 'USD';
    
    setUserLanguage(storedLanguage);
    setCurrency(storedCurrency);
    
    // جلب المنتجات تلقائياً عند تحميل الصفحة
    fetchProducts('');
  }, []);
  
  // وظيفة جلب المنتجات من نقطة النهاية الآمنة في الخادم
  const fetchProducts = async (query) => {
    try {
      setLoading(true);
      setError(null);
      
      // استدعاء نقطة النهاية الآمنة في الخادم
      const response = await fetch('/api/commission-products', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query,
          currency,
          language: userLanguage
        })
      });
      
      if (!response.ok) {
        console.error(`خطأ في استجابة الخادم: ${response.status}`);
        throw new Error(`خطأ في الخادم: ${response.status}`);
      }
      
      const result = await response.json();
      console.log("استجابة API:", result);
      
      if (result.success) {
        setProducts(result.products || []);
        setRecommendation(result.recommendation || '');
      } else {
        setError(result.error || 'حدث خطأ غير معروف');
        setProducts([]);
      }
      
      setLoading(false);
    } catch (err) {
      // تسجيل الخطأ داخليًا فقط (للمطورين)
      console.error('خطأ في جلب المنتجات (للمطورين فقط):', err);
      
      // عرض رسالة مناسبة للمستخدم
      setError('نواجه مشكلة فنية مؤقتة في خدمة البحث. يرجى المحاولة لاحقًا.');
      setProducts([]);
      setLoading(false);
    }
  };
  
  // وظيفة البحث
  const handleSearch = async (e) => {
    e.preventDefault();
    fetchProducts(searchQuery);
  };
  
  // تنسيق السعر حسب العملة
  const formatPrice = (price) => {
    return new Intl.NumberFormat('ar-EG', { 
      style: 'currency', 
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2 
    }).format(price);
  };

  return (
    <div style={containerStyle}>
      <h1 style={titleStyle}>{t('commissionDiscounts.title')}</h1>
      
      {/* شريط البحث */}
      <form onSubmit={handleSearch} style={searchBarContainerStyle}>
        <div style={searchInputWrapperStyle}>
          <input 
            type="text" 
            placeholder={t('commissionDiscounts.searchPlaceholder')} 
            style={searchInputStyle}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <button 
          type="submit" 
          style={{
            ...searchButtonStyle,
            opacity: loading ? 0.7 : 1,
            cursor: loading ? 'not-allowed' : 'pointer'
          }}
          disabled={loading}
        >
          <span style={{ marginRight: direction === 'rtl' ? '5px' : 0, marginLeft: direction === 'rtl' ? 0 : '5px' }}>🔍</span>
          {loading ? t('commissionDiscounts.searching') : t('commissionDiscounts.searchButton')}
        </button>
      </form>
      
      {/* عرض التوصية */}
      {recommendation && (
        <div style={recommendationStyle}>
          {recommendation}
        </div>
      )}
      
      {/* عرض رسالة الخطأ */}
      {error && (
        <div style={errorStyle}>
          {error}
        </div>
      )}
      
      {/* عرض رسالة التحميل */}
      {loading && (
        <div style={loadingStyle}>
          {t('commissionDiscounts.loading')}
        </div>
      )}
      
      {/* أزرار التصفية وطريقة العرض */}
      {!loading && products.length > 0 && (
        <div style={filtersContainerStyle}>
          <div style={filterGroupStyle}>
            <button 
              className={`filter-button ${sortBy === 'price' ? 'active' : ''}`}
              onClick={() => setSortBy(sortBy === 'price' ? '' : 'price')}
            >
              {t('commissionDiscounts.filters.bestPrice')}
            </button>
            <button 
              className={`filter-button ${sortBy === 'sales' ? 'active' : ''}`}
              onClick={() => setSortBy(sortBy === 'sales' ? '' : 'sales')}
            >
              {t('commissionDiscounts.filters.bestSelling')}
            </button>
            <button 
              className={`filter-button ${sortBy === 'rating' ? 'active' : ''}`}
              onClick={() => setSortBy(sortBy === 'rating' ? '' : 'rating')}
            >
              {t('commissionDiscounts.filters.bestRated')}
            </button>
            <button 
              className={`filter-button ${sortBy === 'commission' ? 'active' : ''}`}
              onClick={() => setSortBy(sortBy === 'commission' ? '' : 'commission')}
            >
              {t('commissionDiscounts.filters.highestCommission')}
            </button>
          </div>
          <div style={viewToggleStyle}>
            <button 
              className={`view-button ${viewMode !== 'grid' ? 'inactive' : ''}`}
              onClick={() => setViewMode('grid')}
            >
              <span style={viewIconStyle}>▦</span> {t('commissionDiscounts.filters.viewModes.grid')}
            </button>
            <button 
              className={`view-button ${viewMode !== 'list' ? 'inactive' : ''}`}
              onClick={() => setViewMode('list')}
            >
              <span style={viewIconStyle}>☰</span> {t('commissionDiscounts.filters.viewModes.list')}
            </button>
          </div>
        </div>
      )}
      
      {/* عرض المنتجات (شبكة أو قائمة) */}
      {!loading && products.length > 0 && (
        <div style={viewMode === 'grid' ? productsGridStyle : productsListStyle}>
          {products
            .sort((a, b) => {
              if (sortBy === 'price') return a.total_price - b.total_price;
              if (sortBy === 'sales') return b.sold_count - a.sold_count;
              if (sortBy === 'rating') return b.rating - a.rating;
              if (sortBy === 'commission') return b.commission_discount - a.commission_discount;
              // افتراضياً، رتب حسب الأكثر توصية (الأعلى نقاطاً)
              return b.score - a.score;
            })
            .map(product => (
              <div key={product.id} style={viewMode === 'grid' ? productCardStyle : productListItemStyle}>
                <div style={viewMode === 'grid' ? productImageContainerStyle : productListImageStyle}>
                  <img 
                    src={product.image_url || `https://picsum.photos/seed/${product.id}/300/300`} 
                    alt={product.title} 
                    style={productImageStyle} 
                  />
                </div>
                <div style={viewMode === 'grid' ? productInfoStyle : productListInfoStyle}>
                  <h3 style={productTitleStyle}>{product.title}</h3>
                  
                  {/* السعر والخصم */}
                  <div style={productPriceContainerStyle}>
                    <span style={productPriceStyle}>{formatPrice(product.total_price)}</span>
                    {product.commission_discount > 0 && (
                      <span style={productOldPriceStyle}>{formatPrice(product.price)}</span>
                    )}
                  </div>
                  
                  {/* التقييم والمراجعات */}
                  <div style={productRatingContainerStyle}>
                    <span style={productRatingStyle}>⭐ {product.rating}</span>
                    <span style={productReviewsStyle}>{product.reviews_count} تقييم</span>
                  </div>
                  
                  {/* المبيعات والنقاط */}
                  <div style={productMetaStyle}>
                    <span style={productSalesStyle}>تم بيع {product.sold_count}+</span>
                    <span style={pointsRequiredStyle}>
                      {product.points_required} نقطة
                    </span>
                  </div>
                  
                  {/* العمولة */}
                  <div style={commissionStyle}>
                    العمولة: {product.commission_discount}%
                  </div>
                  
                  {/* توفير */}
                  <div style={savingsStyle}>
                    توفير: {formatPrice(product.savings)}
                  </div>
                  
                  {/* نسبة الخصم */}
                  {product.commission_discount > 0 && (
                    <div style={discountBadgeStyle}>
                      -{product.commission_discount}%
                    </div>
                  )}
                  
                  {/* شارة التوصية */}
                  {product.recommended && (
                    <div style={recommendedBadgeStyle}>
                      موصى به
                    </div>
                  )}
                  
                  {/* زر الشراء */}
                  <a 
                    href={product.affiliateUrl || product.productUrl} 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    style={buyButtonStyle}
                  >
                    تسوق الآن
                  </a>
                </div>
              </div>
            ))
          }
        </div>
      )}
      
      {/* عدد النتائج */}
      {!loading && products.length > 0 && (
        <div style={resultsCountStyle}>
          تم العثور على {products.length} منتج
        </div>
      )}
      
      {/* رسالة عدم وجود نتائج */}
      {!loading && !error && products.length === 0 && (
        <div style={noResultsStyle}>
          {searchQuery.trim() !== ''
            ? `${currentTexts.noSearchResults} "${searchQuery}"`
            : currentTexts.noProducts}
        </div>
      )}
    </div>
  );
}

// الألوان
const aliexpressOrange = '#ff4747';
const aliexpressLightOrange = '#ff6e6e';
const aliexpressDarkOrange = '#e53935';

// الأنماط
const containerStyle = {
  maxWidth: '1200px',
  margin: '0 auto',
  padding: '20px',
};

// أنماط شريط البحث
const searchBarContainerStyle = {
  display: 'flex',
  marginBottom: '20px',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
  borderRadius: '4px',
  overflow: 'hidden',
  maxWidth: '600px',
  margin: '0 auto 20px',
};

const searchInputWrapperStyle = {
  display: 'flex',
  alignItems: 'center',
  flex: 1,
  position: 'relative',
};

const searchInputStyle = {
  flex: 1,
  padding: '10px 16px',
  fontSize: '0.95rem',
  border: 'none',
  outline: 'none',
  width: '100%',
};

const searchButtonStyle = {
  backgroundColor: aliexpressOrange,
  color: 'white',
  border: 'none',
  padding: '0 20px',
  fontSize: '0.95rem',
  cursor: 'pointer',
  transition: 'background-color 0.3s',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
};

// أنماط أزرار التصفية وطريقة العرض
const filtersContainerStyle = {
  display: 'flex',
  justifyContent: 'space-between',
  flexWrap: 'wrap',
  gap: '15px',
  maxWidth: '1000px',
  margin: '0 auto 25px',
};

const filterGroupStyle = {
  display: 'flex',
  gap: '8px',
  flexWrap: 'wrap',
};

const filterButtonStyle = {
  backgroundColor: 'white',
  color: '#333',
  border: '1px solid #ddd',
  borderRadius: '20px',
  padding: '6px 14px',
  fontSize: '0.85rem',
  cursor: 'pointer',
  transition: 'all 0.2s',
  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
};

const filterButtonActiveStyle = {
  backgroundColor: aliexpressOrange,
  color: 'white',
  borderColor: aliexpressOrange,
  boxShadow: '0 2px 5px rgba(255, 71, 71, 0.3)',
};

const viewToggleStyle = {
  display: 'flex',
  gap: '5px',
};

const viewButtonStyle = {
  display: 'flex',
  alignItems: 'center',
  gap: '5px',
  backgroundColor: aliexpressOrange,
  color: 'white',
  border: 'none',
  borderRadius: '4px',
  padding: '6px 10px',
  fontSize: '0.85rem',
  cursor: 'pointer',
  transition: 'all 0.2s',
  boxShadow: '0 2px 5px rgba(255, 71, 71, 0.3)',
};

const viewButtonInactiveStyle = {
  backgroundColor: 'white',
  color: '#333',
  border: '1px solid #ddd',
  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
};

const viewIconStyle = {
  fontSize: '1rem',
};

// أنماط عرض المنتجات
const productsGridStyle = {
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))',
  gap: '20px',
  marginBottom: '20px',
};

const productsListStyle = {
  display: 'flex',
  flexDirection: 'column',
  gap: '15px',
  marginBottom: '20px',
};

// أنماط بطاقة المنتج (عرض الشبكة)
const productCardStyle = {
  backgroundColor: 'white',
  borderRadius: '8px',
  overflow: 'hidden',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
  transition: 'transform 0.3s, box-shadow 0.3s',
  position: 'relative',
};

const productImageContainerStyle = {
  height: '200px',
  overflow: 'hidden',
};

const productImageStyle = {
  width: '100%',
  height: '100%',
  objectFit: 'cover',
  transition: 'transform 0.3s',
};

const productInfoStyle = {
  padding: '15px',
};

// أنماط عنصر المنتج (عرض القائمة)
const productListItemStyle = {
  display: 'flex',
  backgroundColor: 'white',
  borderRadius: '8px',
  overflow: 'hidden',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
  transition: 'transform 0.3s, box-shadow 0.3s',
  position: 'relative',
};

const productListImageStyle = {
  width: '150px',
  height: '150px',
  overflow: 'hidden',
  flexShrink: 0,
};

const productListInfoStyle = {
  padding: '15px',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  flex: 1,
};

// أنماط مشتركة
const productTitleStyle = {
  fontSize: '1rem',
  fontWeight: 'normal',
  marginBottom: '8px',
  color: '#333',
  height: '40px',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  display: '-webkit-box',
  WebkitLineClamp: 2,
  WebkitBoxOrient: 'vertical',
};

const productPriceContainerStyle = {
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  marginBottom: '6px',
};

const productPriceStyle = {
  fontSize: '1.1rem',
  fontWeight: 'bold',
  color: aliexpressOrange,
};

const productOldPriceStyle = {
  fontSize: '0.85rem',
  color: '#999',
  textDecoration: 'line-through',
};

const productRatingContainerStyle = {
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  marginBottom: '6px',
};

const productRatingStyle = {
  display: 'flex',
  alignItems: 'center',
  gap: '4px',
  fontSize: '0.8rem',
  color: '#FF9800',
};

const productReviewsStyle = {
  fontSize: '0.8rem',
  color: '#666',
};

const productMetaStyle = {
  display: 'flex',
  justifyContent: 'space-between',
  fontSize: '0.8rem',
  color: '#666',
  marginTop: '4px',
};

const productSalesStyle = {
  fontSize: '0.8rem',
  color: '#666',
};

const freeShippingStyle = {
  fontSize: '0.8rem',
  color: '#4CAF50',
  fontWeight: 'bold',
};

const commissionStyle = {
  fontSize: '0.85rem',
  color: '#8e44ad',
  fontWeight: 'bold',
  marginTop: '6px',
  backgroundColor: '#f3e5f5',
  padding: '3px 8px',
  borderRadius: '4px',
  display: 'inline-block',
};

const discountBadgeStyle = {
  position: 'absolute',
  top: '10px',
  right: '10px',
  backgroundColor: aliexpressOrange,
  color: 'white',
  padding: '3px 6px',
  borderRadius: '4px',
  fontSize: '0.75rem',
  fontWeight: 'bold',
};

// نمط عدد النتائج
const resultsCountStyle = {
  textAlign: 'center',
  color: '#666',
  marginTop: '10px',
  fontSize: '0.9rem',
};

// أنماط إضافية
const titleStyle = {
  fontSize: '1.8rem',
  marginBottom: '20px',
  color: '#333',
  textAlign: 'center',
};

const recommendationStyle = {
  backgroundColor: '#f8f4ff',
  border: '1px solid #e0d1ff',
  borderRadius: '8px',
  padding: '15px',
  margin: '0 auto 20px',
  maxWidth: '800px',
  textAlign: 'center',
  color: '#6200ea',
  fontSize: '1rem',
  boxShadow: '0 2px 5px rgba(98, 0, 234, 0.1)',
};

const errorStyle = {
  backgroundColor: '#fff5f5',
  border: '1px solid #ffcccc',
  borderRadius: '8px',
  padding: '15px',
  margin: '0 auto 20px',
  maxWidth: '800px',
  textAlign: 'center',
  color: '#d32f2f',
  fontSize: '1rem',
};

const loadingStyle = {
  textAlign: 'center',
  padding: '30px',
  color: '#666',
  fontSize: '1.1rem',
};

const noResultsStyle = {
  textAlign: 'center',
  padding: '40px 20px',
  color: '#666',
  fontSize: '1.1rem',
  backgroundColor: '#f9f9f9',
  borderRadius: '8px',
  margin: '20px auto',
  maxWidth: '600px',
};

const pointsRequiredStyle = {
  color: '#4CAF50',
  fontWeight: 'bold',
  fontSize: '0.8rem',
};

const recommendedBadgeStyle = {
  position: 'absolute',
  top: '10px',
  left: '10px',
  backgroundColor: '#6200ea',
  color: 'white',
  padding: '3px 8px',
  borderRadius: '4px',
  fontSize: '0.75rem',
  fontWeight: 'bold',
  zIndex: 1,
};

const savingsStyle = {
  fontSize: '0.85rem',
  color: '#e53935',
  fontWeight: 'bold',
  marginTop: '6px',
  backgroundColor: '#ffebee',
  padding: '3px 8px',
  borderRadius: '4px',
  display: 'inline-block',
  marginRight: '8px',
};

const buyButtonStyle = {
  display: 'block',
  width: '100%',
  backgroundColor: aliexpressOrange,
  color: 'white',
  textAlign: 'center',
  padding: '8px 12px',
  borderRadius: '4px',
  marginTop: '12px',
  textDecoration: 'none',
  fontWeight: 'bold',
  transition: 'background-color 0.3s',
};