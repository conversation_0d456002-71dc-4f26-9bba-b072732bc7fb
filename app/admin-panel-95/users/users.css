/* تنسيقات صفحة إدارة المستخدمين */

.users-management {
    width: 100%;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.page-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-container {
    position: relative;
    width: 300px;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem;
    padding-right: 2.5rem;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    transition: var(--transition);
}

[dir="rtl"] .search-input {
    padding-right: 1rem;
    padding-left: 2.5rem;
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    outline: none;
}

.search-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-lighter);
    pointer-events: none;
}

[dir="rtl"] .search-icon {
    right: auto;
    left: 1rem;
}

.add-user-btn {
    background-color: var(--primary-color);
    color: white;
    padding: 0.75rem 1.25rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.add-user-btn:hover {
    background-color: var(--primary-dark);
}

.users-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.users-stats .stat-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    text-align: center;
    min-height: auto;
}

.stat-title {
    font-size: 0.875rem;
    color: var(--text-light);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.users-table-container {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    margin-bottom: 2rem;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
}

.users-table th,
.users-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #f0f0f0;
}

.users-table th {
    background-color: #f9f9f9;
    font-weight: 600;
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.users-table th:hover {
    background-color: #f0f0f0;
}

.users-table th.sorted-asc,
.users-table th.sorted-desc {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
}

.sort-arrow {
    margin-right: 0.25rem;
    display: inline-block;
}

.users-table tbody tr {
    transition: var(--transition);
}

.users-table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-active {
    background-color: rgba(46, 204, 113, 0.1);
    color: #27ae60;
}

.status-inactive {
    background-color: rgba(189, 195, 199, 0.1);
    color: #7f8c8d;
}

.status-banned {
    background-color: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.actions-container {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    background-color: #f0f0f0;
    cursor: pointer;
    border: none;
}

.action-btn:hover {
    background-color: #e0e0e0;
}

.view-btn:hover {
    background-color: rgba(52, 152, 219, 0.2);
}

.edit-btn:hover {
    background-color: rgba(241, 196, 15, 0.2);
}

.delete-btn {
    color: #e74c3c;
}

.delete-btn:hover {
    background-color: rgba(231, 76, 60, 0.2);
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(52, 152, 219, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* نافذة تفاصيل المستخدم */
.user-details-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 1rem;
}

.modal-content {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #f0f0f0;
}

.modal-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-lighter);
    transition: var(--transition);
}

.close-btn:hover {
    color: var(--text-color);
}

.modal-body {
    padding: 1.5rem;
}

.user-info-section {
    margin-bottom: 2rem;
}

.user-info-section h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #f0f0f0;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-label {
    font-size: 0.875rem;
    color: var(--text-light);
    font-weight: 500;
}

.info-value {
    font-size: 1rem;
    color: var(--text-color);
}

.user-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 2rem;
    justify-content: flex-start;
}

.action-button {
    padding: 0.75rem 1.25rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.edit-button {
    background-color: var(--primary-color);
    color: white;
}

.edit-button:hover {
    background-color: var(--primary-dark);
}

.reset-password-button {
    background-color: #f39c12;
    color: white;
}

.reset-password-button:hover {
    background-color: #e67e22;
}

.block-button {
    background-color: #e74c3c;
    color: white;
}

.block-button:hover {
    background-color: #c0392b;
}

.unblock-button {
    background-color: #2ecc71;
    color: white;
}

.unblock-button:hover {
    background-color: #27ae60;
}

.activate-button {
    background-color: #3498db;
    color: white;
}

.activate-button:hover {
    background-color: #2980b9;
}

.delete-button {
    background-color: #e74c3c;
    color: white;
}

.delete-button:hover {
    background-color: #c0392b;
}

.cancel-button {
    background-color: #7f8c8d;
    color: white;
}

.cancel-button:hover {
    background-color: #6c7a7d;
}

/* نافذة تأكيد الحذف */
.delete-confirm-modal {
    max-width: 500px;
}

.delete-confirm-message {
    margin-bottom: 1.5rem;
    text-align: center;
}

.delete-warning {
    color: #e74c3c;
    margin-top: 1rem;
    font-weight: 500;
}

/* تنسيقات النماذج */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.9rem;
}

.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    transition: var(--transition);
    box-sizing: border-box;
}

.form-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    outline: none;
}

.form-input::placeholder {
    color: var(--text-lighter);
}

/* تحسين تخطيط النماذج في النوافذ المنبثقة */
.modal-body .user-actions {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #f0f0f0;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

html[dir="rtl"] .modal-body .user-actions {
    flex-direction: row-reverse;
}

html[dir="ltr"] .modal-body .user-actions {
    flex-direction: row;
}

.modal-body .action-button {
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    min-width: 100px;
}

/* تحسينات للتجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .page-actions {
        width: 100%;
        flex-direction: column;
    }

    .search-container {
        width: 100%;
    }

    .add-user-btn {
        width: 100%;
        justify-content: center;
    }

    .users-table-container {
        overflow-x: auto;
    }

    .users-table {
        min-width: 800px;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .user-actions {
        flex-direction: column;
    }

    .action-button {
        width: 100%;
        text-align: center;
    }

    .modal-body .user-actions {
        flex-direction: column;
    }

    .modal-body .action-button {
        width: 100%;
    }
}
