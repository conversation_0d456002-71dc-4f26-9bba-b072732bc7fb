"use client";

import { useState, useEffect } from "react";
import { useTranslation } from "../../components/TranslationProvider";
import "./users.css";

export default function UsersManagement() {
  const { t, language, direction } = useTranslation();
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUser, setSelectedUser] = useState(null);
  const [showUserDetails, setShowUserDetails] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [sortBy, setSortBy] = useState("registrationDate");
  const [sortOrder, setSortOrder] = useState("desc");
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // النصوص حسب اللغة
  const texts = {
    ar: {
      title: "إدارة المستخدمين",
      addUser: "إضافة مستخدم جديد",
      editUser: "تعديل المستخدم",
      deleteUser: "حذف المستخدم",
      userDetails: "تفاصيل المستخدم",
      searchPlaceholder: "البحث عن مستخدم...",
      name: "الاسم",
      email: "البريد الإلكتروني",
      phone: "رقم الهاتف",
      status: "الحالة",
      registrationDate: "تاريخ التسجيل",
      lastLogin: "آخر تسجيل دخول",
      actions: "الإجراءات",
      ordersCount: "عدد الطلبات",
      totalSpent: "إجمالي الإنفاق",
      points: "النقاط المكتسبة",
      basicInfo: "المعلومات الأساسية",
      registrationInfo: "معلومات التسجيل",
      userStats: "إحصائيات المستخدم",
      blockUser: "حظر المستخدم",
      unblockUser: "إلغاء حظر المستخدم",
      activateUser: "تنشيط المستخدم",
      resetPassword: "إعادة تعيين كلمة المرور",
      totalUsers: "إجمالي المستخدمين",
      activeUsers: "المستخدمين النشطين",
      newUsers: "المستخدمين الجدد",
      thisMonth: "هذا الشهر",
      confirmDelete: "هل أنت متأكد من أنك تريد حذف هذا المستخدم؟",
      deleteConfirmMessage: "هل أنت متأكد من أنك تريد حذف هذا المستخدم��",
      deleteWarning: "هذا الإجراء لا يمكن التراجع عنه. جميع بيانات المستخدم ستُحذف نهائياً.",
      loading: "جاري التحميل...",
      save: "حفظ",
      cancel: "إلغاء",
      edit: "تعديل",
      delete: "حذف",
      view: "عرض",
      currency: "ر.س",
      statuses: {
        active: "نشط",
        inactive: "غير نشط",
        banned: "محظور"
      }
    },
    en: {
      title: "User Management",
      addUser: "Add New User",
      editUser: "Edit User",
      deleteUser: "Delete User",
      userDetails: "User Details",
      searchPlaceholder: "Search for user...",
      name: "Name",
      email: "Email",
      phone: "Phone",
      status: "Status",
      registrationDate: "Registration Date",
      lastLogin: "Last Login",
      actions: "Actions",
      ordersCount: "Orders Count",
      totalSpent: "Total Spent",
      points: "Earned Points",
      basicInfo: "Basic Information",
      registrationInfo: "Registration Information",
      userStats: "User Statistics",
      blockUser: "Block User",
      unblockUser: "Unblock User",
      activateUser: "Activate User",
      resetPassword: "Reset Password",
      totalUsers: "Total Users",
      activeUsers: "Active Users",
      newUsers: "New Users",
      thisMonth: "This Month",
      confirmDelete: "Are you sure you want to delete this user?",
      deleteConfirmMessage: "Are you sure you want to delete this user?",
      deleteWarning: "This action cannot be undone. All user data will be permanently deleted.",
      loading: "Loading...",
      save: "Save",
      cancel: "Cancel",
      edit: "Edit",
      delete: "Delete",
      view: "View",
      currency: "$",
      statuses: {
        active: "Active",
        inactive: "Inactive",
        banned: "Banned"
      }
    },
    fr: {
      title: "Gestion des Utilisateurs",
      addUser: "Ajouter un Nouvel Utilisateur",
      editUser: "Modifier l'Utilisateur",
      deleteUser: "Supprimer l'Utilisateur",
      userDetails: "Détails de l'Utilisateur",
      searchPlaceholder: "Rechercher un utilisateur...",
      name: "Nom",
      email: "Email",
      phone: "Téléphone",
      status: "Statut",
      registrationDate: "Date d'Inscription",
      lastLogin: "Dernière Connexion",
      actions: "Actions",
      ordersCount: "Nombre de Commandes",
      totalSpent: "Total Dépensé",
      points: "Points Gagnés",
      basicInfo: "Informations de Base",
      registrationInfo: "Informations d'Inscription",
      userStats: "Statistiques Utilisateur",
      blockUser: "Bloquer l'Utilisateur",
      unblockUser: "Débloquer l'Utilisateur",
      activateUser: "Activer l'Utilisateur",
      resetPassword: "Réinitialiser le Mot de Passe",
      totalUsers: "Total des Utilisateurs",
      activeUsers: "Utilisateurs Actifs",
      newUsers: "Nouveaux Utilisateurs",
      thisMonth: "Ce Mois",
      confirmDelete: "Êtes-vous sûr de vouloir supprimer cet utilisateur?",
      deleteConfirmMessage: "Êtes-vous sûr de vouloir supprimer cet utilisateur?",
      deleteWarning: "Cette action ne peut pas être annulée. Toutes les données utilisateur seront supprimées définitivement.",
      loading: "Chargement...",
      save: "Enregistrer",
      cancel: "Annuler",
      edit: "Modifier",
      delete: "Supprimer",
      view: "Voir",
      currency: "€",
      statuses: {
        active: "Actif",
        inactive: "Inactif",
        banned: "Banni"
      }
    },
    es: {
      title: "Gestión de Usuarios",
      addUser: "Agregar Nuevo Usuario",
      editUser: "Editar Usuario",
      deleteUser: "Eliminar Usuario",
      userDetails: "Detalles del Usuario",
      searchPlaceholder: "Buscar usuario...",
      name: "Nombre",
      email: "Email",
      phone: "Teléfono",
      status: "Estado",
      registrationDate: "Fecha de Registro",
      lastLogin: "Último Acceso",
      actions: "Acciones",
      ordersCount: "Número de Pedidos",
      totalSpent: "Total Gastado",
      points: "Puntos Ganados",
      basicInfo: "Información Básica",
      registrationInfo: "Información de Registro",
      userStats: "Estadísticas del Usuario",
      blockUser: "Bloquear Usuario",
      unblockUser: "Desbloquear Usuario",
      activateUser: "Activar Usuario",
      resetPassword: "Restablecer Contraseña",
      totalUsers: "Total de Usuarios",
      activeUsers: "Usuarios Activos",
      newUsers: "Nuevos Usuarios",
      thisMonth: "Este Mes",
      confirmDelete: "¿Estás seguro de que quieres eliminar este usuario?",
      deleteConfirmMessage: "¿Estás seguro de que quieres eliminar este usuario?",
      deleteWarning: "Esta acción no se puede deshacer. Todos los datos del usuario se eliminarán permanentemente.",
      loading: "Cargando...",
      save: "Guardar",
      cancel: "Cancelar",
      edit: "Editar",
      delete: "Eliminar",
      view: "Ver",
      currency: "$",
      statuses: {
        active: "Activo",
        inactive: "Inactivo",
        banned: "Prohibido"
      }
    },
    uk: {
      title: "Управління Користувачами",
      addUser: "Додати Нового Користувача",
      editUser: "Редагувати Користувача",
      deleteUser: "Видалити Користувача",
      userDetails: "Деталі Користувача",
      searchPlaceholder: "Шукати користувача...",
      name: "Ім'я",
      email: "Email",
      phone: "Телефон",
      status: "Статус",
      registrationDate: "Дата Реєстрації",
      lastLogin: "Останній Вхід",
      actions: "Дії",
      ordersCount: "Кількість Замовлень",
      totalSpent: "Всього Витрачено",
      points: "Зароблені Бали",
      basicInfo: "Основна Інформація",
      registrationInfo: "Інформація про Реєстрацію",
      userStats: "Статистика Користувача",
      blockUser: "Заблокувати Користувача",
      unblockUser: "Розблокувати Користувача",
      activateUser: "Активувати Користувача",
      resetPassword: "Скинути Пароль",
      totalUsers: "Всього Користувачів",
      activeUsers: "Активні Користувачі",
      newUsers: "Нові Користувачі",
      thisMonth: "Цей Місяць",
      confirmDelete: "Ви впевнені, що хочете видалити цього користувача?",
      deleteConfirmMessage: "Ви впевнені, що хочете видалити цього користувача?",
      deleteWarning: "Ця дія не може бути скасована. Всі дані користувача будуть видалені назавжди.",
      loading: "Завантаження...",
      save: "Зберегти",
      cancel: "Скасувати",
      edit: "Редагувати",
      delete: "Видалити",
      view: "Переглянути",
      currency: "₴",
      statuses: {
        active: "Активний",
        inactive: "Неактивний",
        banned: "Заборонений"
      }
    }
  };

  const currentTexts = texts[language] || texts.ar;

  // دالة ترجمة حالات المستخدمين
  const getStatusText = (status) => {
    return currentTexts.statuses[status] || status;
  };

  // جلب بيانات المستخدمين من API
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        // في المستقبل، سيتم استبدال هذا بطلب API حقيقي
        // const response = await fetch('/api/admin/users');
        // const data = await response.json();

        // حالياً نعرض قائمة فارغة حتى يتم ربط النظام بقاعدة البيانات
        setUsers([]);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching users:", error);
        setUsers([]);
        setIsLoading(false);
      }
    };

    fetchUsers();
  }, []);

  // وظيفة البحث عن المستخدمين
  const filteredUsers = users.filter((user) => {
    const query = searchQuery.toLowerCase();
    return (
      user.email.toLowerCase().includes(query) ||
      user.name.toLowerCase().includes(query) ||
      user.status.toLowerCase().includes(query)
    );
  });

  // وظيفة فرز المستخدمين
  const sortedUsers = [...filteredUsers].sort((a, b) => {
    let comparison = 0;

    switch (sortBy) {
      case "name":
        comparison = a.name.localeCompare(b.name);
        break;
      case "email":
        comparison = a.email.localeCompare(b.email);
        break;
      case "registrationDate":
        comparison =
          new Date(a.registrationDate) - new Date(b.registrationDate);
        break;
      case "lastLogin":
        comparison = new Date(a.lastLogin) - new Date(b.lastLogin);
        break;
      case "status":
        comparison = a.status.localeCompare(b.status);
        break;
      case "ordersCount":
        comparison = a.ordersCount - b.ordersCount;
        break;
      case "totalSpent":
        comparison = a.totalSpent - b.totalSpent;
        break;
      default:
        comparison = 0;
    }

    return sortOrder === "asc" ? comparison : -comparison;
  });

  // وظيفة تغيير ترتيب الفرز
  const handleSort = (column) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("desc");
    }
  };

  // وظيفة عرض تفاصيل المستخدم
  const handleViewUserDetails = (user) => {
    setSelectedUser(user);
    setShowUserDetails(true);
  };

  // وظيفة إغلاق تفاصيل المستخدم
  const handleCloseUserDetails = () => {
    setShowUserDetails(false);
    setSelectedUser(null);
  };

  // وظيفة فتح نافذة إضافة مستخدم
  const handleAddUser = () => {
    setShowAddModal(true);
  };

  // وظيفة فتح نافذة تعديل المستخدم
  const handleEditUser = (user) => {
    setSelectedUser(user);
    setShowEditModal(true);
  };

  // وظيفة إغلاق نوافذ الإضافة والتعديل
  const handleCloseModals = () => {
    setShowAddModal(false);
    setShowEditModal(false);
    setSelectedUser(null);
  };

  // وظيفة حذف المستخدم
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleDeleteUser = (user) => {
    setSelectedUser(user);
    setShowDeleteConfirm(true);
  };

  const confirmDeleteUser = () => {
    // محاكاة حذف المستخدم من القائمة
    setUsers(users.filter((user) => user.id !== selectedUser.id));
    setShowDeleteConfirm(false);
    setSelectedUser(null);
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    const options = {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    };
    const locale =
      language === "ar"
        ? "ar-EG"
        : language === "fr"
          ? "fr-FR"
          : language === "es"
            ? "es-ES"
            : language === "uk"
              ? "uk-UA"
              : "en-US";
    return new Date(dateString).toLocaleDateString(locale, options);
  };

  // عدم عرض المكون حتى يتم تحميله من جانب العميل
  if (!isClient) {
    return (
      <div className="users-management">
        <div className="page-header">
          <h1 className="page-title">Loading...</h1>
        </div>
        <div className="loading-spinner">جاري التحميل...</div>
      </div>
    );
  }

  return (
    <div className="users-management">
      <div className="page-header">
        <h1 className="page-title">{t("admin.users.title")}</h1>
        <div className="page-actions">
          <div className="search-container">
            <input
              type="text"
              placeholder={t("admin.users.searchPlaceholder")}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
            <span className="search-icon">🔍</span>
          </div>
          <button className="add-user-btn" onClick={handleAddUser}>
            <span>➕</span>
            <span>{t("admin.users.addUser")}</span>
          </button>
        </div>
      </div>

      <div className="users-stats">
        <div className="stat-card">
          <h3 className="stat-title">{t("admin.analytics.totalUsers")}</h3>
          <div className="stat-value">{users.length}</div>
        </div>
        <div className="stat-card">
          <h3 className="stat-title">{t("admin.analytics.activeUsers")}</h3>
          <div className="stat-value">
            {users.filter((user) => user.status === "active").length}
          </div>
        </div>
        <div className="stat-card">
          <h3 className="stat-title">
            {t("admin.analytics.newUsers")} (
            {t("admin.analytics.thisMonth")})
          </h3>
          <div className="stat-value">
            {
              users.filter((user) => {
                const registrationDate = new Date(user.registrationDate);
                const now = new Date();
                return (
                  registrationDate.getMonth() === now.getMonth() &&
                  registrationDate.getFullYear() === now.getFullYear()
                );
              }).length
            }
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>{t("common.messages.loading")}</p>
        </div>
      ) : (
        <div className="users-table-container">
          <table className="users-table">
            <thead>
              <tr>
                <th
                  onClick={() => handleSort("name")}
                  className={sortBy === "name" ? `sorted-${sortOrder}` : ""}
                >
                  {t("admin.users.name")}{" "}
                  {sortBy === "name" && (
                    <span className="sort-arrow">
                      {sortOrder === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </th>
                <th
                  onClick={() => handleSort("email")}
                  className={sortBy === "email" ? `sorted-${sortOrder}` : ""}
                >
                  {t("admin.users.email")}{" "}
                  {sortBy === "email" && (
                    <span className="sort-arrow">
                      {sortOrder === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </th>
                <th
                  onClick={() => handleSort("registrationDate")}
                  className={
                    sortBy === "registrationDate" ? `sorted-${sortOrder}` : ""
                  }
                >
                  {t("admin.users.registrationDate")}{" "}
                  {sortBy === "registrationDate" && (
                    <span className="sort-arrow">
                      {sortOrder === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </th>
                <th
                  onClick={() => handleSort("lastLogin")}
                  className={
                    sortBy === "lastLogin" ? `sorted-${sortOrder}` : ""
                  }
                >
                  {t("admin.users.lastLogin")}{" "}
                  {sortBy === "lastLogin" && (
                    <span className="sort-arrow">
                      {sortOrder === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </th>
                <th
                  onClick={() => handleSort("status")}
                  className={sortBy === "status" ? `sorted-${sortOrder}` : ""}
                >
                  {t("admin.users.status")}{" "}
                  {sortBy === "status" && (
                    <span className="sort-arrow">
                      {sortOrder === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </th>
                <th
                  onClick={() => handleSort("ordersCount")}
                  className={
                    sortBy === "ordersCount" ? `sorted-${sortOrder}` : ""
                  }
                >
                  {t("admin.users.ordersCount")}{" "}
                  {sortBy === "ordersCount" && (
                    <span className="sort-arrow">
                      {sortOrder === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </th>
                <th>{t("admin.users.actions")}</th>
              </tr>
            </thead>
            <tbody>
              {sortedUsers.map((user) => (
                <tr key={user.id}>
                  <td>{user.name}</td>
                  <td>{user.email}</td>
                  <td>{formatDate(user.registrationDate)}</td>
                  <td>{formatDate(user.lastLogin)}</td>
                  <td>
                    <span className={`status-badge status-${user.status}`}>
                      {getStatusText(user.status)}
                    </span>
                  </td>
                  <td>{user.ordersCount}</td>
                  <td>
                    <div className="actions-container">
                      <button
                        className="action-btn view-btn"
                        onClick={() => handleViewUserDetails(user)}
                        title={t("common.buttons.view")}
                      >
                        👁️
                      </button>
                      <button
                        className="action-btn edit-btn"
                        title={t("common.buttons.edit")}
                        onClick={() => handleEditUser(user)}
                      >
                        ✏️
                      </button>
                      <button
                        className="action-btn delete-btn"
                        title={t("common.buttons.delete")}
                        onClick={() => handleDeleteUser(user)}
                      >
                        🗑️
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* نافذة تفاصيل المستخدم */}
      {showUserDetails && selectedUser && (
        <div className="user-details-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h2>{t("admin.users.userDetails")}</h2>
              <button className="close-btn" onClick={handleCloseUserDetails}>
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="user-info-section">
                <h3>{t("admin.users.basicInfo")}</h3>
                <div className="info-grid">
                  <div className="info-item">
                    <span className="info-label">
                      {t("admin.users.name")}:
                    </span>
                    <span className="info-value">{selectedUser.name}</span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">
                      {t("admin.users.email")}:
                    </span>
                    <span className="info-value">{selectedUser.email}</span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">
                      {t("admin.users.status")}:
                    </span>
                    <span
                      className={`status-badge status-${selectedUser.status}`}
                    >
                      {getStatusText(selectedUser.status)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="user-info-section">
                <h3>{t("admin.users.registrationInfo")}</h3>
                <div className="info-grid">
                  <div className="info-item">
                    <span className="info-label">
                      {t("admin.users.registrationDate")}:
                    </span>
                    <span className="info-value">
                      {formatDate(selectedUser.registrationDate)}
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">
                      {t("admin.users.lastLogin")}:
                    </span>
                    <span className="info-value">
                      {formatDate(selectedUser.lastLogin)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="user-info-section">
                <h3>{t("admin.users.userStats")}</h3>
                <div className="info-grid">
                  <div className="info-item">
                    <span className="info-label">
                      {t("admin.users.ordersCount")}:
                    </span>
                    <span className="info-value">
                      {selectedUser.ordersCount}
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">
                      {t("admin.users.totalSpent")}:
                    </span>
                    <span className="info-value">
                      {selectedUser.totalSpent} {t("common.currency")}
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">
                      {t("admin.users.points")}:
                    </span>
                    <span className="info-value">{selectedUser.points}</span>
                  </div>
                </div>
              </div>

              <div className="user-actions">
                <button className="action-button edit-button">
                  {t("admin.users.editUser")}
                </button>
                <button className="action-button reset-password-button">
                  {t("admin.users.resetPassword")}
                </button>
                {selectedUser.status === "active" ? (
                  <button className="action-button block-button">
                    {t("admin.users.blockUser")}
                  </button>
                ) : selectedUser.status === "banned" ? (
                  <button className="action-button unblock-button">
                    {t("admin.users.unblockUser")}
                  </button>
                ) : (
                  <button className="action-button activate-button">
                    {t("admin.users.activateUser")}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نافذة إضافة مستخدم */}
      {showAddModal && (
        <div className="user-details-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h2>{t("admin.users.addUser")}</h2>
              <button className="close-btn" onClick={handleCloseModals}>
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>{t("admin.users.name")}:</label>
                <input
                  type="text"
                  className="form-input"
                  placeholder={t("admin.users.name")}
                />
              </div>
              <div className="form-group">
                <label>{t("admin.users.email")}:</label>
                <input
                  type="email"
                  className="form-input"
                  placeholder={t("admin.users.email")}
                />
              </div>
              <div className="form-group">
                <label>{t("admin.users.phone")}:</label>
                <input
                  type="tel"
                  className="form-input"
                  placeholder={t("admin.users.phone")}
                />
              </div>
              <div className="user-actions">
                <button className="action-button edit-button">
                  {t("common.buttons.save")}
                </button>
                <button
                  className="action-button reset-password-button"
                  onClick={handleCloseModals}
                >
                  {t("common.buttons.cancel")}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نافذة تعديل مستخدم */}
      {showEditModal && selectedUser && (
        <div className="user-details-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h2>{t("admin.users.editUser")}</h2>
              <button className="close-btn" onClick={handleCloseModals}>
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>{t("admin.users.name")}:</label>
                <input
                  type="text"
                  className="form-input"
                  defaultValue={selectedUser.name}
                />
              </div>
              <div className="form-group">
                <label>{t("admin.users.email")}:</label>
                <input
                  type="email"
                  className="form-input"
                  defaultValue={selectedUser.email}
                />
              </div>
              <div className="form-group">
                <label>{t("admin.users.status")}:</label>
                <select
                  className="form-input"
                  defaultValue={selectedUser.status}
                >
                  <option value="active">
                    {t("admin.users.statuses.active")}
                  </option>
                  <option value="inactive">
                    {t("admin.users.statuses.inactive")}
                  </option>
                  <option value="banned">
                    {t("admin.users.statuses.banned")}
                  </option>
                </select>
              </div>
              <div className="user-actions">
                <button className="action-button edit-button">
                  {t("common.buttons.save")}
                </button>
                <button
                  className="action-button reset-password-button"
                  onClick={handleCloseModals}
                >
                  {t("common.buttons.cancel")}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نافذة تأكيد حذف المستخدم */}
      {showDeleteConfirm && selectedUser && (
        <div className="user-details-modal">
          <div className="modal-content delete-confirm-modal">
            <div className="modal-header">
              <h2>{t("admin.users.deleteUser")}</h2>
              <button
                className="close-btn"
                onClick={() => setShowDeleteConfirm(false)}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="delete-confirm-message">
                <p>
                  {t("admin.users.deleteConfirmMessage")}{" "}
                  <strong>{selectedUser.name}</strong> ({selectedUser.email})?
                </p>
                <p className="delete-warning">
                  {t("admin.users.deleteWarning")}
                </p>
              </div>
              <div className="user-actions">
                <button
                  className="action-button delete-button"
                  onClick={confirmDeleteUser}
                >
                  {t("common.buttons.delete")}
                </button>
                <button
                  className="action-button cancel-button"
                  onClick={() => setShowDeleteConfirm(false)}
                >
                  {t("common.buttons.cancel")}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
