"use client";

import { useState, useEffect } from "react";
import { useTranslation } from "../../components/TranslationProvider";
import "./users.css";

export default function UsersManagement() {
  const { t, language } = useTranslation();
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUser, setSelectedUser] = useState(null);
  const [showUserDetails, setShowUserDetails] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [sortBy, setSortBy] = useState("registrationDate");
  const [sortOrder, setSortOrder] = useState("desc");

  // النصوص حسب اللغة
  const texts = {
    ar: {
      title: "إدارة المستخدمين",
      searchPlaceholder: "البحث عن مستخدم...",
      addUser: "إضافة مستخدم جديد",
      name: "الاسم",
      email: "البريد الإلكتروني",
      registrationDate: "تاريخ التسجيل",
      lastLogin: "آخر تسجيل دخول",
      status: "الحالة",
      ordersCount: "عدد الطلبات",
      actions: "الإجراءات",
      userDetails: "تفاصيل المستخدم",
      basicInfo: "المعلومات الأساسية",
      registrationInfo: "معلومات التسجيل",
      userStats: "إحصائيات المستخدم",
      totalSpent: "إجمالي المبلغ المنفق",
      points: "النقاط",
      editUser: "تعديل المستخدم",
      resetPassword: "إعادة تعيين كلمة المرور",
      blockUser: "حظر المستخدم",
      unblockUser: "إلغاء حظر المستخدم",
      activateUser: "تفعيل المستخدم",
      deleteUser: "حذف المستخدم",
      deleteConfirmMessage: "هل أنت متأكد من حذف المستخدم",
      deleteWarning: "هذا الإجراء لا يمكن التراجع عنه!",
      phone: "رقم الهاتف",
      totalUsers: "إجمالي المستخدمين",
      activeUsers: "المستخدمين النشطين",
      newUsers: "المستخدمين الجدد",
      thisMonth: "هذا الشهر",
      loading: "جاري التحميل...",
      save: "حفظ",
      cancel: "إلغاء",
      edit: "تعديل",
      delete: "حذف",
      view: "عرض",
      statuses: {
        active: "نشط",
        inactive: "غير نشط",
        banned: "محظور"
      }
    },
    en: {
      title: "User Management",
      searchPlaceholder: "Search for user...",
      addUser: "Add New User",
      name: "Name",
      email: "Email",
      registrationDate: "Registration Date",
      lastLogin: "Last Login",
      status: "Status",
      ordersCount: "Orders Count",
      actions: "Actions",
      userDetails: "User Details",
      basicInfo: "Basic Information",
      registrationInfo: "Registration Information",
      userStats: "User Statistics",
      totalSpent: "Total Spent",
      points: "Points",
      editUser: "Edit User",
      resetPassword: "Reset Password",
      blockUser: "Block User",
      unblockUser: "Unblock User",
      activateUser: "Activate User",
      deleteUser: "Delete User",
      deleteConfirmMessage: "Are you sure you want to delete user",
      deleteWarning: "This action cannot be undone!",
      phone: "Phone Number",
      totalUsers: "Total Users",
      activeUsers: "Active Users",
      newUsers: "New Users",
      thisMonth: "This Month",
      loading: "Loading...",
      save: "Save",
      cancel: "Cancel",
      edit: "Edit",
      delete: "Delete",
      view: "View",
      statuses: {
        active: "Active",
        inactive: "Inactive",
        banned: "Banned"
      }
    },
    fr: {
      title: "Gestion des Utilisateurs",
      searchPlaceholder: "Rechercher un utilisateur...",
      addUser: "Ajouter un Nouvel Utilisateur",
      name: "Nom",
      email: "Email",
      registrationDate: "Date d'Inscription",
      lastLogin: "Dernière Connexion",
      status: "Statut",
      ordersCount: "Nombre de Commandes",
      actions: "Actions",
      userDetails: "Détails de l'Utilisateur",
      basicInfo: "Informations de Base",
      registrationInfo: "Informations d'Inscription",
      userStats: "Statistiques de l'Utilisateur",
      totalSpent: "Total Dépensé",
      points: "Points",
      editUser: "Modifier l'Utilisateur",
      resetPassword: "Réinitialiser le Mot de Passe",
      blockUser: "Bloquer l'Utilisateur",
      unblockUser: "Débloquer l'Utilisateur",
      activateUser: "Activer l'Utilisateur",
      deleteUser: "Supprimer l'Utilisateur",
      deleteConfirmMessage: "Êtes-vous sûr de vouloir supprimer l'utilisateur",
      deleteWarning: "Cette action ne peut pas être annulée!",
      phone: "Numéro de Téléphone",
      totalUsers: "Total des Utilisateurs",
      activeUsers: "Utilisateurs Actifs",
      newUsers: "Nouveaux Utilisateurs",
      thisMonth: "Ce Mois",
      loading: "Chargement...",
      save: "Enregistrer",
      cancel: "Annuler",
      edit: "Modifier",
      delete: "Supprimer",
      view: "Voir",
      statuses: {
        active: "Actif",
        inactive: "Inactif",
        banned: "Banni"
      }
    },
    es: {
      title: "Gestión de Usuarios",
      searchPlaceholder: "Buscar usuario...",
      addUser: "Agregar Nuevo Usuario",
      name: "Nombre",
      email: "Email",
      registrationDate: "Fecha de Registro",
      lastLogin: "Último Acceso",
      status: "Estado",
      ordersCount: "Número de Pedidos",
      actions: "Acciones",
      userDetails: "Detalles del Usuario",
      basicInfo: "Información Básica",
      registrationInfo: "Información de Registro",
      userStats: "Estadísticas del Usuario",
      totalSpent: "Total Gastado",
      points: "Puntos",
      editUser: "Editar Usuario",
      resetPassword: "Restablecer Contraseña",
      blockUser: "Bloquear Usuario",
      unblockUser: "Desbloquear Usuario",
      activateUser: "Activar Usuario",
      deleteUser: "Eliminar Usuario",
      deleteConfirmMessage: "¿Estás seguro de que quieres eliminar el usuario",
      deleteWarning: "¡Esta acción no se puede deshacer!",
      phone: "Número de Teléfono",
      totalUsers: "Total de Usuarios",
      activeUsers: "Usuarios Activos",
      newUsers: "Nuevos Usuarios",
      thisMonth: "Este Mes",
      loading: "Cargando...",
      save: "Guardar",
      cancel: "Cancelar",
      edit: "Editar",
      delete: "Eliminar",
      view: "Ver",
      statuses: {
        active: "Activo",
        inactive: "Inactivo",
        banned: "Bloqueado"
      }
    },
    uk: {
      title: "Управління Користувачами",
      searchPlaceholder: "Шукати користувача...",
      addUser: "Додати Нового Користувача",
      name: "Ім'я",
      email: "Email",
      registrationDate: "Дата Реєстрації",
      lastLogin: "Останній Вхід",
      status: "Статус",
      ordersCount: "Кількість Замовлень",
      actions: "Дії",
      userDetails: "Деталі Користувача",
      basicInfo: "Основна Інформація",
      registrationInfo: "Інформація про Реєстрацію",
      userStats: "Статистика Користувача",
      totalSpent: "Загальна Витрата",
      points: "Бали",
      editUser: "Редагувати Користувача",
      resetPassword: "Скинути Пароль",
      blockUser: "Заблокувати Користувача",
      unblockUser: "Розблокувати Користувача",
      activateUser: "Активувати Користувача",
      deleteUser: "Видалити Користувача",
      deleteConfirmMessage: "Ви впевнені, що хочете видалити користувача",
      deleteWarning: "Цю дію неможливо скасувати!",
      phone: "Номер Телефону",
      totalUsers: "Всього Користувачів",
      activeUsers: "Активні Користувачі",
      newUsers: "Нові Користувачі",
      thisMonth: "Цей Місяць",
      loading: "Завантаження...",
      save: "Зберегти",
      cancel: "Скасувати",
      edit: "Редагувати",
      delete: "Видалити",
      view: "Переглянути",
      statuses: {
        active: "Активний",
        inactive: "Неактивний",
        banned: "Заблокований"
      }
    }
  };

  const currentTexts = texts[language] || texts.ar;

  // دالة ترجمة حالات المستخدمين
  const getStatusText = (status) => {
    return currentTexts.statuses[status] || status;
  };

  // جلب بيانات المستخدمين من API
  useEffect(() => {
    // التحقق من أن نظام الترجمة جاهز
    if (!t || typeof t !== "function") {
      console.warn("Translation function not ready yet");
      return;
    }

    // اختبار بسيط للتأكد من عمل الترجمة
    try {
      const testTranslation = t("admin.users.title");
      if (!testTranslation || typeof testTranslation === "object") {
        console.warn("Translation system not fully loaded");
        return;
      }
    } catch (error) {
      console.error("Translation system error:", error);
      return;
    }

    const fetchUsers = async () => {
      try {
        // في المستقبل، سيتم استبدال هذا بطلب API حقيقي
        // const response = await fetch('/api/admin/users');
        // const data = await response.json();

        // حالياً نعرض قائمة فارغة حتى يتم ربط النظام بقاعدة البيانات
        setUsers([]);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching users:", error);
        setUsers([]);
        setIsLoading(false);
      }
    };

    fetchUsers();
  }, [t, language]);

  // وظيفة البحث عن المستخدمين
  const filteredUsers = users.filter((user) => {
    const query = searchQuery.toLowerCase();
    return (
      user.email.toLowerCase().includes(query) ||
      user.name.toLowerCase().includes(query) ||
      user.status.toLowerCase().includes(query)
    );
  });

  // وظيفة فرز المستخدمين
  const sortedUsers = [...filteredUsers].sort((a, b) => {
    let comparison = 0;

    switch (sortBy) {
      case "name":
        comparison = a.name.localeCompare(b.name);
        break;
      case "email":
        comparison = a.email.localeCompare(b.email);
        break;
      case "registrationDate":
        comparison =
          new Date(a.registrationDate) - new Date(b.registrationDate);
        break;
      case "lastLogin":
        comparison = new Date(a.lastLogin) - new Date(b.lastLogin);
        break;
      case "status":
        comparison = a.status.localeCompare(b.status);
        break;
      case "ordersCount":
        comparison = a.ordersCount - b.ordersCount;
        break;
      case "totalSpent":
        comparison = a.totalSpent - b.totalSpent;
        break;
      default:
        comparison = 0;
    }

    return sortOrder === "asc" ? comparison : -comparison;
  });

  // وظيفة تغيير ترتيب الفرز
  const handleSort = (column) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("desc");
    }
  };

  // وظيفة عرض تفاصيل المستخدم
  const handleViewUserDetails = (user) => {
    setSelectedUser(user);
    setShowUserDetails(true);
  };

  // وظيفة إغلاق تفاصيل المستخدم
  const handleCloseUserDetails = () => {
    setShowUserDetails(false);
    setSelectedUser(null);
  };

  // وظيفة فتح نافذة إضافة مستخدم
  const handleAddUser = () => {
    setShowAddModal(true);
  };

  // وظيفة فتح نافذة تعديل المستخدم
  const handleEditUser = (user) => {
    setSelectedUser(user);
    setShowEditModal(true);
  };

  // وظيفة إغلاق نوافذ الإضافة والتعديل
  const handleCloseModals = () => {
    setShowAddModal(false);
    setShowEditModal(false);
    setSelectedUser(null);
  };

  // وظيفة حذف المستخدم
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleDeleteUser = (user) => {
    setSelectedUser(user);
    setShowDeleteConfirm(true);
  };

  const confirmDeleteUser = () => {
    // محاكاة حذف المستخدم من القائمة
    setUsers(users.filter((user) => user.id !== selectedUser.id));
    setShowDeleteConfirm(false);
    setSelectedUser(null);
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    const options = {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    };
    const locale =
      language === "ar"
        ? "ar-EG"
        : language === "fr"
          ? "fr-FR"
          : language === "es"
            ? "es-ES"
            : language === "uk"
              ? "uk-UA"
              : "en-US";
    return new Date(dateString).toLocaleDateString(locale, options);
  };

  return (
    <div className="users-management">
      <div className="page-header">
        <h1 className="page-title">{currentTexts.title}</h1>
        <div className="page-actions">
          <div className="search-container">
            <input
              type="text"
              placeholder={currentTexts.searchPlaceholder}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
            <span className="search-icon">🔍</span>
          </div>
          <button className="add-user-btn" onClick={handleAddUser}>
            <span>➕</span>
            <span>{currentTexts.addUser}</span>
          </button>
        </div>
      </div>

      <div className="users-stats">
        <div className="stat-card">
          <h3 className="stat-title">{currentTexts.totalUsers}</h3>
          <div className="stat-value">{users.length}</div>
        </div>
        <div className="stat-card">
          <h3 className="stat-title">{currentTexts.activeUsers}</h3>
          <div className="stat-value">
            {users.filter((user) => user.status === "active").length}
          </div>
        </div>
        <div className="stat-card">
          <h3 className="stat-title">
            {currentTexts.newUsers} (
            {currentTexts.thisMonth})
          </h3>
          <div className="stat-value">
            {
              users.filter((user) => {
                const registrationDate = new Date(user.registrationDate);
                const now = new Date();
                return (
                  registrationDate.getMonth() === now.getMonth() &&
                  registrationDate.getFullYear() === now.getFullYear()
                );
              }).length
            }
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>{currentTexts.loading}</p>
        </div>
      ) : (
        <div className="users-table-container">
          <table className="users-table">
            <thead>
              <tr>
                <th
                  onClick={() => handleSort("name")}
                  className={sortBy === "name" ? `sorted-${sortOrder}` : ""}
                >
                  {currentTexts.name}{" "}
                  {sortBy === "name" && (
                    <span className="sort-arrow">
                      {sortOrder === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </th>
                <th
                  onClick={() => handleSort("email")}
                  className={sortBy === "email" ? `sorted-${sortOrder}` : ""}
                >
                  {currentTexts.email}{" "}
                  {sortBy === "email" && (
                    <span className="sort-arrow">
                      {sortOrder === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </th>
                <th
                  onClick={() => handleSort("registrationDate")}
                  className={
                    sortBy === "registrationDate" ? `sorted-${sortOrder}` : ""
                  }
                >
                  {currentTexts.registrationDate}{" "}
                  {sortBy === "registrationDate" && (
                    <span className="sort-arrow">
                      {sortOrder === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </th>
                <th
                  onClick={() => handleSort("lastLogin")}
                  className={
                    sortBy === "lastLogin" ? `sorted-${sortOrder}` : ""
                  }
                >
                  {currentTexts.lastLogin}{" "}
                  {sortBy === "lastLogin" && (
                    <span className="sort-arrow">
                      {sortOrder === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </th>
                <th
                  onClick={() => handleSort("status")}
                  className={sortBy === "status" ? `sorted-${sortOrder}` : ""}
                >
                  {currentTexts.status}{" "}
                  {sortBy === "status" && (
                    <span className="sort-arrow">
                      {sortOrder === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </th>
                <th
                  onClick={() => handleSort("ordersCount")}
                  className={
                    sortBy === "ordersCount" ? `sorted-${sortOrder}` : ""
                  }
                >
                  {currentTexts.ordersCount}{" "}
                  {sortBy === "ordersCount" && (
                    <span className="sort-arrow">
                      {sortOrder === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </th>
                <th>{currentTexts.actions}</th>
              </tr>
            </thead>
            <tbody>
              {sortedUsers.map((user) => (
                <tr key={user.id}>
                  <td>{user.name}</td>
                  <td>{user.email}</td>
                  <td>{formatDate(user.registrationDate)}</td>
                  <td>{formatDate(user.lastLogin)}</td>
                  <td>
                    <span className={`status-badge status-${user.status}`}>
                      {getStatusText(user.status)}
                    </span>
                  </td>
                  <td>{user.ordersCount}</td>
                  <td>
                    <div className="actions-container">
                      <button
                        className="action-btn view-btn"
                        onClick={() => handleViewUserDetails(user)}
                        title={currentTexts.view}
                      >
                        👁️
                      </button>
                      <button
                        className="action-btn edit-btn"
                        title={currentTexts.edit}
                        onClick={() => handleEditUser(user)}
                      >
                        ✏️
                      </button>
                      <button
                        className="action-btn delete-btn"
                        title={currentTexts.delete}
                        onClick={() => handleDeleteUser(user)}
                      >
                        🗑️
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* نافذة تفاصيل المستخدم */}
      {showUserDetails && selectedUser && (
        <div className="user-details-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h2>{currentTexts.userDetails}</h2>
              <button className="close-btn" onClick={handleCloseUserDetails}>
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="user-info-section">
                <h3>{currentTexts.basicInfo}</h3>
                <div className="info-grid">
                  <div className="info-item">
                    <span className="info-label">
                      {currentTexts.name}:
                    </span>
                    <span className="info-value">{selectedUser.name}</span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">
                      {currentTexts.email}:
                    </span>
                    <span className="info-value">{selectedUser.email}</span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">
                      {currentTexts.status}:
                    </span>
                    <span
                      className={`status-badge status-${selectedUser.status}`}
                    >
                      {getStatusText(selectedUser.status)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="user-info-section">
                <h3>{currentTexts.registrationInfo}</h3>
                <div className="info-grid">
                  <div className="info-item">
                    <span className="info-label">
                      {currentTexts.registrationDate}:
                    </span>
                    <span className="info-value">
                      {formatDate(selectedUser.registrationDate)}
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">
                      {currentTexts.lastLogin}:
                    </span>
                    <span className="info-value">
                      {formatDate(selectedUser.lastLogin)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="user-info-section">
                <h3>{currentTexts.userStats}</h3>
                <div className="info-grid">
                  <div className="info-item">
                    <span className="info-label">
                      {currentTexts.ordersCount}:
                    </span>
                    <span className="info-value">
                      {selectedUser.ordersCount}
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">
                      {currentTexts.totalSpent}:
                    </span>
                    <span className="info-value">
                      {selectedUser.totalSpent} ريال
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">
                      {currentTexts.points}:
                    </span>
                    <span className="info-value">{selectedUser.points}</span>
                  </div>
                </div>
              </div>

              <div className="user-actions">
                <button className="action-button edit-button">
                  {currentTexts.editUser}
                </button>
                <button className="action-button reset-password-button">
                  {currentTexts.resetPassword}
                </button>
                {selectedUser.status === "active" ? (
                  <button className="action-button block-button">
                    {currentTexts.blockUser}
                  </button>
                ) : selectedUser.status === "banned" ? (
                  <button className="action-button unblock-button">
                    {currentTexts.unblockUser}
                  </button>
                ) : (
                  <button className="action-button activate-button">
                    {currentTexts.activateUser}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نافذة إضافة مستخدم */}
      {showAddModal && (
        <div className="user-details-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h2>{currentTexts.addUser}</h2>
              <button className="close-btn" onClick={handleCloseModals}>
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>{currentTexts.name}:</label>
                <input
                  type="text"
                  className="form-input"
                  placeholder={currentTexts.name}
                />
              </div>
              <div className="form-group">
                <label>{currentTexts.email}:</label>
                <input
                  type="email"
                  className="form-input"
                  placeholder={currentTexts.email}
                />
              </div>
              <div className="form-group">
                <label>{currentTexts.phone}:</label>
                <input
                  type="tel"
                  className="form-input"
                  placeholder={currentTexts.phone}
                />
              </div>
              <div className="user-actions">
                <button className="action-button edit-button">
                  {currentTexts.save}
                </button>
                <button
                  className="action-button reset-password-button"
                  onClick={handleCloseModals}
                >
                  {currentTexts.cancel}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نافذة تعديل مستخدم */}
      {showEditModal && selectedUser && (
        <div className="user-details-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h2>{currentTexts.editUser}</h2>
              <button className="close-btn" onClick={handleCloseModals}>
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>{currentTexts.name}:</label>
                <input
                  type="text"
                  className="form-input"
                  defaultValue={selectedUser.name}
                />
              </div>
              <div className="form-group">
                <label>{currentTexts.email}:</label>
                <input
                  type="email"
                  className="form-input"
                  defaultValue={selectedUser.email}
                />
              </div>
              <div className="form-group">
                <label>{currentTexts.status}:</label>
                <select
                  className="form-input"
                  defaultValue={selectedUser.status}
                >
                  <option value="active">
                    {currentTexts.statuses.active}
                  </option>
                  <option value="inactive">
                    {currentTexts.statuses.inactive}
                  </option>
                  <option value="banned">
                    {currentTexts.statuses.banned}
                  </option>
                </select>
              </div>
              <div className="user-actions">
                <button className="action-button edit-button">
                  {currentTexts.save}
                </button>
                <button
                  className="action-button reset-password-button"
                  onClick={handleCloseModals}
                >
                  {currentTexts.cancel}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نافذة تأكيد حذف المستخدم */}
      {showDeleteConfirm && selectedUser && (
        <div className="user-details-modal">
          <div className="modal-content delete-confirm-modal">
            <div className="modal-header">
              <h2>{currentTexts.deleteUser}</h2>
              <button
                className="close-btn"
                onClick={() => setShowDeleteConfirm(false)}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="delete-confirm-message">
                <p>
                  {currentTexts.deleteConfirmMessage}{" "}
                  <strong>{selectedUser.name}</strong> ({selectedUser.email})?
                </p>
                <p className="delete-warning">
                  {currentTexts.deleteWarning}
                </p>
              </div>
              <div className="user-actions">
                <button
                  className="action-button delete-button"
                  onClick={confirmDeleteUser}
                >
                  {currentTexts.delete}
                </button>
                <button
                  className="action-button cancel-button"
                  onClick={() => setShowDeleteConfirm(false)}
                >
                  {currentTexts.cancel}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
