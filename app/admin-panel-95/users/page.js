"use client";

import { useState, useEffect } from "react";
import { useTranslation } from "../../components/TranslationProvider";
import "./users.css";

export default function UsersManagement() {
  const { t, language } = useTranslation();
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUser, setSelectedUser] = useState(null);
  const [showUserDetails, setShowUserDetails] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [sortBy, setSortBy] = useState("registrationDate");
  const [sortOrder, setSortOrder] = useState("desc");

  // خريطة ثابتة للحالات كاحتياطي
  const statusTranslations = {
    ar: {
      active: "نشط",
      inactive: "غير نشط",
      banned: "محظور",
    },
    en: {
      active: "Active",
      inactive: "Inactive",
      banned: "Banned",
    },
    fr: {
      active: "Actif",
      inactive: "Inactif",
      banned: "Banni",
    },
    es: {
      active: "Activo",
      inactive: "Inactivo",
      banned: "Prohibido",
    },
    uk: {
      active: "Активний",
      inactive: "Неактивний",
      banned: "Заборонений",
    },
  };

  // دالة آمنة شاملة للترجمة تضمن إرجاع نص وليس كائن
  const safeT = (key, fallback = key) => {
    try {
      const result = t(key);

      // التحقق من أن النتيجة صالحة للعرض في React
      if (result === null || result === undefined) {
        return fallback;
      }

      if (typeof result === "string" && result.trim() !== "") {
        return result;
      }

      if (typeof result === "number" || typeof result === "boolean") {
        return String(result);
      }

      // إذا كان الكائن، حاول استخراج القيمة المطلوبة
      if (typeof result === "object" && !Array.isArray(result)) {
        const parts = key.split(".");
        const lastPart = parts[parts.length - 1];
        if (result[lastPart] && typeof result[lastPart] === "string") {
          return result[lastPart];
        }
      }

      return fallback;
    } catch (error) {
      return fallback;
    }
  };

  // دالة خاصة لترجمة حالات المستخدمين
  const getStatusText = (status) => {
    // أولاً، حاول الحصول على الترجمة من النظام
    const translatedStatus = safeT(`admin.users.statuses.${status}`, null);
    if (
      translatedStatus &&
      translatedStatus !== `admin.users.statuses.${status}`
    ) {
      return translatedStatus;
    }

    // إذا فشل، استخدم الترجمات الثابتة
    const staticTranslations =
      statusTranslations[language] || statusTranslations.en;
    return staticTranslations[status] || status;
  };

  // جلب بيانات المستخدمين من API
  useEffect(() => {
    // التحقق من أن نظام الترجمة جاهز
    if (!t || typeof t !== "function") {
      console.warn("Translation function not ready yet");
      return;
    }

    // اختبار بسيط للتأكد من عمل الترجمة
    try {
      const testTranslation = t("admin.users.title");
      if (!testTranslation || typeof testTranslation === "object") {
        console.warn("Translation system not fully loaded");
        return;
      }
    } catch (error) {
      console.error("Translation system error:", error);
      return;
    }

    const fetchUsers = async () => {
      try {
        // في المستقبل، سيتم استبدال هذا بطلب API حقيقي
        // const response = await fetch('/api/admin/users');
        // const data = await response.json();

        // حالياً نعرض قائمة فارغة حتى يتم ربط النظام بقاعدة البيانات
        setUsers([]);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching users:", error);
        setUsers([]);
        setIsLoading(false);
      }
    };

    fetchUsers();
  }, [t, language]);

  // وظيفة البحث عن المستخدمين
  const filteredUsers = users.filter((user) => {
    const query = searchQuery.toLowerCase();
    return (
      user.email.toLowerCase().includes(query) ||
      user.name.toLowerCase().includes(query) ||
      user.status.toLowerCase().includes(query)
    );
  });

  // وظيفة فرز المستخدمين
  const sortedUsers = [...filteredUsers].sort((a, b) => {
    let comparison = 0;

    switch (sortBy) {
      case "name":
        comparison = a.name.localeCompare(b.name);
        break;
      case "email":
        comparison = a.email.localeCompare(b.email);
        break;
      case "registrationDate":
        comparison =
          new Date(a.registrationDate) - new Date(b.registrationDate);
        break;
      case "lastLogin":
        comparison = new Date(a.lastLogin) - new Date(b.lastLogin);
        break;
      case "status":
        comparison = a.status.localeCompare(b.status);
        break;
      case "ordersCount":
        comparison = a.ordersCount - b.ordersCount;
        break;
      case "totalSpent":
        comparison = a.totalSpent - b.totalSpent;
        break;
      default:
        comparison = 0;
    }

    return sortOrder === "asc" ? comparison : -comparison;
  });

  // وظيفة تغيير ترتيب الفرز
  const handleSort = (column) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("desc");
    }
  };

  // وظيفة عرض تفاصيل المستخدم
  const handleViewUserDetails = (user) => {
    setSelectedUser(user);
    setShowUserDetails(true);
  };

  // وظيفة إغلاق تفاصيل المستخدم
  const handleCloseUserDetails = () => {
    setShowUserDetails(false);
    setSelectedUser(null);
  };

  // وظيفة فتح نافذة إضافة مستخدم
  const handleAddUser = () => {
    setShowAddModal(true);
  };

  // وظيفة فتح نافذة تعديل المستخدم
  const handleEditUser = (user) => {
    setSelectedUser(user);
    setShowEditModal(true);
  };

  // وظيفة إغلاق نوافذ الإضافة والتعديل
  const handleCloseModals = () => {
    setShowAddModal(false);
    setShowEditModal(false);
    setSelectedUser(null);
  };

  // وظيفة حذف المستخدم
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleDeleteUser = (user) => {
    setSelectedUser(user);
    setShowDeleteConfirm(true);
  };

  const confirmDeleteUser = () => {
    // محاكاة حذف المستخدم من القائمة
    setUsers(users.filter((user) => user.id !== selectedUser.id));
    setShowDeleteConfirm(false);
    setSelectedUser(null);
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    const options = {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    };
    const locale =
      language === "ar"
        ? "ar-EG"
        : language === "fr"
          ? "fr-FR"
          : language === "es"
            ? "es-ES"
            : language === "uk"
              ? "uk-UA"
              : "en-US";
    return new Date(dateString).toLocaleDateString(locale, options);
  };

  return (
    <div className="users-management">
      <div className="page-header">
        <h1 className="page-title">{safeT("admin.users.title")}</h1>
        <div className="page-actions">
          <div className="search-container">
            <input
              type="text"
              placeholder={safeT("admin.users.searchPlaceholder")}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
            <span className="search-icon">🔍</span>
          </div>
          <button className="add-user-btn" onClick={handleAddUser}>
            <span>➕</span>
            <span>{safeT("admin.users.addUser")}</span>
          </button>
        </div>
      </div>

      <div className="users-stats">
        <div className="stat-card">
          <h3 className="stat-title">{safeT("admin.analytics.totalUsers")}</h3>
          <div className="stat-value">{users.length}</div>
        </div>
        <div className="stat-card">
          <h3 className="stat-title">{safeT("admin.analytics.activeUsers")}</h3>
          <div className="stat-value">
            {users.filter((user) => user.status === "active").length}
          </div>
        </div>
        <div className="stat-card">
          <h3 className="stat-title">
            {safeT("admin.analytics.newUsers")} (
            {safeT("admin.analytics.thisMonth")})
          </h3>
          <div className="stat-value">
            {
              users.filter((user) => {
                const registrationDate = new Date(user.registrationDate);
                const now = new Date();
                return (
                  registrationDate.getMonth() === now.getMonth() &&
                  registrationDate.getFullYear() === now.getFullYear()
                );
              }).length
            }
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>{safeT("common.messages.loading")}</p>
        </div>
      ) : (
        <div className="users-table-container">
          <table className="users-table">
            <thead>
              <tr>
                <th
                  onClick={() => handleSort("name")}
                  className={sortBy === "name" ? `sorted-${sortOrder}` : ""}
                >
                  {safeT("admin.users.name")}{" "}
                  {sortBy === "name" && (
                    <span className="sort-arrow">
                      {sortOrder === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </th>
                <th
                  onClick={() => handleSort("email")}
                  className={sortBy === "email" ? `sorted-${sortOrder}` : ""}
                >
                  {safeT("admin.users.email")}{" "}
                  {sortBy === "email" && (
                    <span className="sort-arrow">
                      {sortOrder === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </th>
                <th
                  onClick={() => handleSort("registrationDate")}
                  className={
                    sortBy === "registrationDate" ? `sorted-${sortOrder}` : ""
                  }
                >
                  {safeT("admin.users.registrationDate")}{" "}
                  {sortBy === "registrationDate" && (
                    <span className="sort-arrow">
                      {sortOrder === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </th>
                <th
                  onClick={() => handleSort("lastLogin")}
                  className={
                    sortBy === "lastLogin" ? `sorted-${sortOrder}` : ""
                  }
                >
                  {safeT("admin.users.lastLogin")}{" "}
                  {sortBy === "lastLogin" && (
                    <span className="sort-arrow">
                      {sortOrder === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </th>
                <th
                  onClick={() => handleSort("status")}
                  className={sortBy === "status" ? `sorted-${sortOrder}` : ""}
                >
                  {safeT("admin.users.status")}{" "}
                  {sortBy === "status" && (
                    <span className="sort-arrow">
                      {sortOrder === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </th>
                <th
                  onClick={() => handleSort("ordersCount")}
                  className={
                    sortBy === "ordersCount" ? `sorted-${sortOrder}` : ""
                  }
                >
                  {safeT("admin.users.ordersCount")}{" "}
                  {sortBy === "ordersCount" && (
                    <span className="sort-arrow">
                      {sortOrder === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </th>
                <th>{safeT("admin.users.actions")}</th>
              </tr>
            </thead>
            <tbody>
              {sortedUsers.map((user) => (
                <tr key={user.id}>
                  <td>{user.name}</td>
                  <td>{user.email}</td>
                  <td>{formatDate(user.registrationDate)}</td>
                  <td>{formatDate(user.lastLogin)}</td>
                  <td>
                    <span className={`status-badge status-${user.status}`}>
                      {getStatusText(user.status)}
                    </span>
                  </td>
                  <td>{user.ordersCount}</td>
                  <td>
                    <div className="actions-container">
                      <button
                        className="action-btn view-btn"
                        onClick={() => handleViewUserDetails(user)}
                        title={safeT("common.buttons.view")}
                      >
                        👁️
                      </button>
                      <button
                        className="action-btn edit-btn"
                        title={safeT("common.buttons.edit")}
                        onClick={() => handleEditUser(user)}
                      >
                        ✏️
                      </button>
                      <button
                        className="action-btn delete-btn"
                        title={safeT("common.buttons.delete")}
                        onClick={() => handleDeleteUser(user)}
                      >
                        🗑️
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* نافذة تفاصيل المستخدم */}
      {showUserDetails && selectedUser && (
        <div className="user-details-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h2>{safeT("admin.users.userDetails")}</h2>
              <button className="close-btn" onClick={handleCloseUserDetails}>
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="user-info-section">
                <h3>{safeT("admin.users.basicInfo")}</h3>
                <div className="info-grid">
                  <div className="info-item">
                    <span className="info-label">
                      {safeT("admin.users.name")}:
                    </span>
                    <span className="info-value">{selectedUser.name}</span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">
                      {safeT("admin.users.email")}:
                    </span>
                    <span className="info-value">{selectedUser.email}</span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">
                      {safeT("admin.users.status")}:
                    </span>
                    <span
                      className={`status-badge status-${selectedUser.status}`}
                    >
                      {getStatusText(selectedUser.status)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="user-info-section">
                <h3>{safeT("admin.users.registrationInfo")}</h3>
                <div className="info-grid">
                  <div className="info-item">
                    <span className="info-label">
                      {safeT("admin.users.registrationDate")}:
                    </span>
                    <span className="info-value">
                      {formatDate(selectedUser.registrationDate)}
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">
                      {safeT("admin.users.lastLogin")}:
                    </span>
                    <span className="info-value">
                      {formatDate(selectedUser.lastLogin)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="user-info-section">
                <h3>{safeT("admin.users.userStats")}</h3>
                <div className="info-grid">
                  <div className="info-item">
                    <span className="info-label">
                      {safeT("admin.users.ordersCount")}:
                    </span>
                    <span className="info-value">
                      {selectedUser.ordersCount}
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">
                      {safeT("admin.users.totalSpent")}:
                    </span>
                    <span className="info-value">
                      {selectedUser.totalSpent} {safeT("common.currency")}
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">
                      {safeT("admin.users.points")}:
                    </span>
                    <span className="info-value">{selectedUser.points}</span>
                  </div>
                </div>
              </div>

              <div className="user-actions">
                <button className="action-button edit-button">
                  {safeT("admin.users.editUser")}
                </button>
                <button className="action-button reset-password-button">
                  {safeT("admin.users.resetPassword")}
                </button>
                {selectedUser.status === "active" ? (
                  <button className="action-button block-button">
                    {safeT("admin.users.blockUser")}
                  </button>
                ) : selectedUser.status === "banned" ? (
                  <button className="action-button unblock-button">
                    {safeT("admin.users.unblockUser")}
                  </button>
                ) : (
                  <button className="action-button activate-button">
                    {safeT("admin.users.activateUser")}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نافذة إضافة مستخدم */}
      {showAddModal && (
        <div className="user-details-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h2>{safeT("admin.users.addUser")}</h2>
              <button className="close-btn" onClick={handleCloseModals}>
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>{safeT("admin.users.name")}:</label>
                <input
                  type="text"
                  className="form-input"
                  placeholder={safeT("admin.users.name")}
                />
              </div>
              <div className="form-group">
                <label>{safeT("admin.users.email")}:</label>
                <input
                  type="email"
                  className="form-input"
                  placeholder={safeT("admin.users.email")}
                />
              </div>
              <div className="form-group">
                <label>{safeT("admin.users.phone")}:</label>
                <input
                  type="tel"
                  className="form-input"
                  placeholder={safeT("admin.users.phone")}
                />
              </div>
              <div className="user-actions">
                <button className="action-button edit-button">
                  {safeT("common.buttons.save")}
                </button>
                <button
                  className="action-button reset-password-button"
                  onClick={handleCloseModals}
                >
                  {safeT("common.buttons.cancel")}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نافذة تعديل مستخدم */}
      {showEditModal && selectedUser && (
        <div className="user-details-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h2>{safeT("admin.users.editUser")}</h2>
              <button className="close-btn" onClick={handleCloseModals}>
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>{safeT("admin.users.name")}:</label>
                <input
                  type="text"
                  className="form-input"
                  defaultValue={selectedUser.name}
                />
              </div>
              <div className="form-group">
                <label>{safeT("admin.users.email")}:</label>
                <input
                  type="email"
                  className="form-input"
                  defaultValue={selectedUser.email}
                />
              </div>
              <div className="form-group">
                <label>{safeT("admin.users.status")}:</label>
                <select
                  className="form-input"
                  defaultValue={selectedUser.status}
                >
                  <option value="active">
                    {safeT("admin.users.statuses.active")}
                  </option>
                  <option value="inactive">
                    {safeT("admin.users.statuses.inactive")}
                  </option>
                  <option value="banned">
                    {safeT("admin.users.statuses.banned")}
                  </option>
                </select>
              </div>
              <div className="user-actions">
                <button className="action-button edit-button">
                  {safeT("common.buttons.save")}
                </button>
                <button
                  className="action-button reset-password-button"
                  onClick={handleCloseModals}
                >
                  {safeT("common.buttons.cancel")}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نافذة تأكيد حذف المستخدم */}
      {showDeleteConfirm && selectedUser && (
        <div className="user-details-modal">
          <div className="modal-content delete-confirm-modal">
            <div className="modal-header">
              <h2>{safeT("admin.users.deleteUser")}</h2>
              <button
                className="close-btn"
                onClick={() => setShowDeleteConfirm(false)}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="delete-confirm-message">
                <p>
                  {safeT("admin.users.deleteConfirmMessage")}{" "}
                  <strong>{selectedUser.name}</strong> ({selectedUser.email})?
                </p>
                <p className="delete-warning">
                  {safeT("admin.users.deleteWarning")}
                </p>
              </div>
              <div className="user-actions">
                <button
                  className="action-button delete-button"
                  onClick={confirmDeleteUser}
                >
                  {safeT("common.buttons.delete")}
                </button>
                <button
                  className="action-button cancel-button"
                  onClick={() => setShowDeleteConfirm(false)}
                >
                  {safeT("common.buttons.cancel")}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
