'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { updateOffersSystemSettings } from '../utils/offersSystem'
import { useTranslation } from '../../components/TranslationProvider'

export default function Settings() {
  const { t } = useTranslation();
  const router = useRouter();
  const [apiKeys, setApiKeys] = useState({
    appKey: '',
    secretKey: '',
    tagId: ''
  })

  const [isSaving, setIsSaving] = useState(false)
  const [saveMessage, setSaveMessage] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('api')


  // التحقق من حالة تسجيل الدخول كمسؤول
  useEffect(() => {
    const isAdminAuthenticated = localStorage.getItem('admin_authenticated');
    if (!isAdminAuthenticated || isAdminAuthenticated !== 'true') {
      // إذا لم يكن المستخدم مسؤولاً، قم بتوجيهه إلى صفحة تسجيل دخول المسؤول
      router.push('/admin-panel-95/admin-login');
      return;
    }
  }, [router]);

  // جلب البيانات من localStorage
  useEffect(() => {
    try {
      const savedApiKeys = localStorage.getItem('aliexpressApiKeys')
      if (savedApiKeys) {
        setApiKeys(JSON.parse(savedApiKeys))
      }
    } catch (error) {
      console.error('Error loading API keys from localStorage:', error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  const handleChange = (e) => {
    const { name, value } = e.target
    setApiKeys(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSaving(true)
    
    try {
      // حفظ البيانات في localStorage
      localStorage.setItem('aliexpressApiKeys', JSON.stringify(apiKeys))
      
      // تحديث إعدادات نظام العروض
      const updated = await updateOffersSystemSettings(apiKeys)
      
      setIsSaving(false)
      if (updated) {
        setSaveMessage(t('admin.settings.settingsSavedAndSystemUpdated'))
      } else {
        setSaveMessage(t('admin.settings.settingsSavedButSystemUpdateFailed'))
      }
      
      // إخفاء رسالة النجاح بعد 3 ثوان
      setTimeout(() => {
        setSaveMessage('')
      }, 3000)
    } catch (error) {
      console.error('Error saving API keys to localStorage:', error)
      setIsSaving(false)
      setSaveMessage(t('admin.settings.errorSavingSettings'))
    }
  }



  return (
    <div>
      <div className="dashboard-header">
        <h1 className="page-title">{t('admin.settings.title')}</h1>
      </div>
      
      <div className="settings-tabs">
        <button 
          className={`settings-tab ${activeTab === 'api' ? 'active' : ''}`}
          onClick={() => setActiveTab('api')}
        >
          <span className="tab-icon">🔑</span>
          <span className="tab-text">{t('admin.settings.apiSettings')}</span>
        </button>
        <button 
          className={`settings-tab ${activeTab === 'general' ? 'active' : ''}`}
          onClick={() => setActiveTab('general')}
        >
          <span className="tab-icon">⚙️</span>
          <span className="tab-text">{t('admin.settings.general')}</span>
        </button>
        <button 
          className={`settings-tab ${activeTab === 'notifications' ? 'active' : ''}`}
          onClick={() => setActiveTab('notifications')}
        >
          <span className="tab-icon">🔔</span>
          <span className="tab-text">{t('admin.settings.notifications')}</span>
        </button>
      </div>
      
      {activeTab === 'api' && (
        <>
          <div className="card">
            <div className="card-header">
              <h2 className="card-title">{t('admin.settings.aliexpressApiKeys')}</h2>
              <a href="https://portals.aliexpress.com/help.htm" target="_blank" rel="noopener noreferrer" className="help-link">
                <span className="help-icon">❓</span>
                <span>{t('common.buttons.help')}</span>
              </a>
            </div>
            
            {isLoading ? (
              <div className="form-loading"></div>
            ) : (
              <form onSubmit={handleSubmit}>
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="appKey">App Key</label>
                    <div className="input-wrapper">
                      <input
                        type="text"
                        id="appKey"
                        name="appKey"
                        value={apiKeys.appKey}
                        onChange={handleChange}
                        placeholder={t('admin.settings.enterYourAppKey')}
                        required
                      />
                      <div className="input-icon">🔑</div>
                    </div>
                    <div className="form-hint">{t('admin.settings.appKeyHint')}</div>
                  </div>
                </div>
                
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="secretKey">Secret Key</label>
                    <div className="input-wrapper">
                      <input
                        type="password"
                        id="secretKey"
                        name="secretKey"
                        value={apiKeys.secretKey}
                        onChange={handleChange}
                        placeholder={t('admin.settings.enterYourSecretKey')}
                        required
                      />
                      <div className="input-icon">🔒</div>
                    </div>
                    <div className="form-hint">{t('admin.settings.secretKeyHint')}</div>
                  </div>
                </div>
                
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="tagId">Tag ID</label>
                    <div className="input-wrapper">
                      <input
                        type="text"
                        id="tagId"
                        name="tagId"
                        value={apiKeys.tagId}
                        onChange={handleChange}
                        placeholder={t('admin.settings.enterYourTagId')}
                        required
                      />
                      <div className="input-icon">🏷️</div>
                    </div>
                    <div className="form-hint">{t('admin.settings.tagIdHint')}</div>
                  </div>
                </div>
                
                <div className="form-actions">
                  <button 
                    type="submit" 
                    className="btn btn-primary" 
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <>
                        <span className="spinner"></span>
                        <span>{t('admin.settings.saving')}</span>
                      </>
                    ) : (
                      t('admin.settings.saveSettings')
                    )}
                  </button>
                  
                  <button 
                    type="button" 
                    className="btn btn-secondary"
                    onClick={() => {
                      // إعادة تعيين الحقول إلى القيم المحفوظة
                      try {
                        const savedApiKeys = localStorage.getItem('aliexpressApiKeys')
                        if (savedApiKeys) {
                          setApiKeys(JSON.parse(savedApiKeys))
                        } else {
                          setApiKeys({
                            appKey: '',
                            secretKey: '',
                            tagId: ''
                          })
                        }
                      } catch (error) {
                        console.error('Error resetting fields:', error)
                      }
                    }}
                  >
                    {t('common.buttons.reset')}
                  </button>
                </div>
                
                {saveMessage && (
                  <div className="save-message success">
                    <span className="message-icon">✓</span>
                    <span>{saveMessage}</span>
                  </div>
                )}
              </form>
            )}
          </div>
          
          <div className="card">
            <div className="card-header">
              <h2 className="card-title">{t('admin.settings.connectionTest')}</h2>
            </div>
            
            <div className="api-test">
              <p>{t('admin.settings.testConnectionDescription')}</p>
              <button 
                className="btn btn-secondary"
                onClick={() => {
                  // محاكاة اختبار الاتصال
                  setSaveMessage(t('admin.settings.testingConnection'))
                  
                  setTimeout(() => {
                    if (apiKeys.appKey && apiKeys.secretKey && apiKeys.tagId) {
                      setSaveMessage(t('admin.settings.connectionSuccessful'))
                    } else {
                      setSaveMessage(t('admin.settings.connectionFailed'))
                    }
                    
                    // إخفاء الرسالة بعد 3 ثوان
                    setTimeout(() => {
                      setSaveMessage('')
                    }, 3000)
                  }, 1500)
                }}
              >
                {t('admin.settings.testConnection')}
              </button>
            </div>
          </div>
        </>
      )}
      
      {activeTab === 'general' && (
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">{t('admin.settings.general')}</h2>
          </div>
          
          <div className="settings-placeholder">
            <p>{t('admin.settings.generalSettingsFuture')}</p>
          </div>
        </div>
      )}
      
      {activeTab === 'notifications' && (
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">{t('admin.settings.notifications')}</h2>
          </div>
          
          <div className="settings-placeholder">
            <p>{t('admin.settings.notificationSettingsFuture')}</p>
          </div>
        </div>
      )}
    </div>
  )
}