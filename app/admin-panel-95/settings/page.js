'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { updateOffersSystemSettings } from '../utils/offersSystem'
import { useTranslation } from '../../components/TranslationProvider'

export default function Settings() {
  const { language } = useTranslation();
  const router = useRouter();

  // النصوص حسب اللغة
  const texts = {
    ar: {
      title: "إعدادات النظام",
      apiSettings: "إعدادات API",
      generalSettings: "الإعدادات العامة",
      appKey: "مفتاح التطبيق",
      secretKey: "المفتاح السري",
      tagId: "معرف العلامة",
      save: "حفظ",
      saving: "جاري الحفظ...",
      saved: "تم الحفظ بنجاح!",
      error: "حدث خطأ أثناء الحفظ",
      loading: "جاري التحميل...",
      enterAppKey: "أدخل مفتاح التطبيق",
      enterSecretKey: "أدخل المفتاح السري",
      enterTagId: "أدخل معرف العلامة",
      apiKeysDescription: "قم بإدخال مفاتيح API الخاصة بك لتفعيل الخدمات",
      generalDescription: "إعدادات عامة للنظام",
      testConnection: "اختبار الاتصال",
      testConnectionDesc: "اختبر الاتصال مع API للتأكد من صحة البيانات",
      notifications: "الإشعارات",
      help: "مساعدة",
      reset: "إعادة التعيين",
      notificationsComingSoon: "إعدادات الإشعارات ستكون متاحة قريباً"
    },
    en: {
      title: "System Settings",
      apiSettings: "API Settings",
      generalSettings: "General Settings",
      appKey: "App Key",
      secretKey: "Secret Key",
      tagId: "Tag ID",
      save: "Save",
      saving: "Saving...",
      saved: "Saved successfully!",
      error: "Error occurred while saving",
      loading: "Loading...",
      enterAppKey: "Enter App Key",
      enterSecretKey: "Enter Secret Key",
      enterTagId: "Enter Tag ID",
      apiKeysDescription: "Enter your API keys to activate services",
      generalDescription: "General system settings",
      testConnection: "Test Connection",
      testConnectionDesc: "Test API connection to verify data accuracy",
      notifications: "Notifications",
      help: "Help",
      reset: "Reset",
      notificationsComingSoon: "Notification settings will be available soon"
    },
    fr: {
      title: "Paramètres du Système",
      apiSettings: "Paramètres API",
      generalSettings: "Paramètres Généraux",
      appKey: "Clé d'Application",
      secretKey: "Clé Secrète",
      tagId: "ID de Tag",
      save: "Enregistrer",
      saving: "Enregistrement...",
      saved: "Enregistré avec succès!",
      error: "Erreur lors de l'enregistrement",
      loading: "Chargement...",
      enterAppKey: "Entrer la Clé d'Application",
      enterSecretKey: "Entrer la Clé Secrète",
      enterTagId: "Entrer l'ID de Tag",
      apiKeysDescription: "Entrez vos clés API pour activer les services",
      generalDescription: "Paramètres généraux du système",
      testConnection: "Tester la Connexion",
      testConnectionDesc: "Testez la connexion API pour vérifier l'exactitude des données",
      notifications: "Notifications",
      help: "Aide",
      reset: "Réinitialiser",
      notificationsComingSoon: "Les paramètres de notification seront bientôt disponibles"
    },
    es: {
      title: "Configuración del Sistema",
      apiSettings: "Configuración de API",
      generalSettings: "Configuración General",
      appKey: "Clave de Aplicación",
      secretKey: "Clave Secreta",
      tagId: "ID de Etiqueta",
      save: "Guardar",
      saving: "Guardando...",
      saved: "¡Guardado exitosamente!",
      error: "Error al guardar",
      loading: "Cargando...",
      enterAppKey: "Ingrese la Clave de Aplicación",
      enterSecretKey: "Ingrese la Clave Secreta",
      enterTagId: "Ingrese el ID de Etiqueta",
      apiKeysDescription: "Ingrese sus claves API para activar servicios",
      generalDescription: "Configuración general del sistema",
      testConnection: "Probar Conexión",
      testConnectionDesc: "Pruebe la conexión API para verificar la precisión de los datos",
      notifications: "Notificaciones",
      help: "Ayuda",
      reset: "Restablecer",
      notificationsComingSoon: "La configuración de notificaciones estará disponible pronto"
    },
    uk: {
      title: "Налаштування Системи",
      apiSettings: "Налаштування API",
      generalSettings: "Загальні Налаштування",
      appKey: "Ключ Додатку",
      secretKey: "Секретний Ключ",
      tagId: "ID Тегу",
      save: "Зберегти",
      saving: "Збереження...",
      saved: "Успішно збережено!",
      error: "Помилка при збереженні",
      loading: "Завантаження...",
      enterAppKey: "Введіть Ключ Додатку",
      enterSecretKey: "Введіть Секретний Ключ",
      enterTagId: "Введіть ID Тегу",
      apiKeysDescription: "Введіть ваші ключі API для активації сервісів",
      generalDescription: "Загальні налаштування системи",
      testConnection: "Тест З'єднання",
      testConnectionDesc: "Протестуйте з'єднання API для перевірки точності даних",
      notifications: "Сповіщення",
      help: "Допомога",
      reset: "Скинути",
      notificationsComingSoon: "Налаштування сповіщень будуть доступні незабаром"
    }
  };

  const currentTexts = texts[language] || texts.ar;

  const [apiKeys, setApiKeys] = useState({
    appKey: '',
    secretKey: '',
    tagId: ''
  })

  const [isSaving, setIsSaving] = useState(false)
  const [saveMessage, setSaveMessage] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('api')


  // التحقق من حالة تسجيل الدخول كمسؤول
  useEffect(() => {
    const isAdminAuthenticated = localStorage.getItem('admin_authenticated');
    if (!isAdminAuthenticated || isAdminAuthenticated !== 'true') {
      // إذا لم يكن المستخدم مسؤولاً، قم بتوجيهه إلى صفحة تسجيل دخول المسؤول
      router.push('/admin-panel-95/admin-login');
      return;
    }
  }, [router]);

  // جلب البيانات من localStorage
  useEffect(() => {
    try {
      const savedApiKeys = localStorage.getItem('aliexpressApiKeys')
      if (savedApiKeys) {
        setApiKeys(JSON.parse(savedApiKeys))
      }
    } catch (error) {
      console.error('Error loading API keys from localStorage:', error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  const handleChange = (e) => {
    const { name, value } = e.target
    setApiKeys(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSaving(true)
    
    try {
      // حفظ البيانات في localStorage
      localStorage.setItem('aliexpressApiKeys', JSON.stringify(apiKeys))
      
      // تحديث إعدادات نظام العروض
      const updated = await updateOffersSystemSettings(apiKeys)
      
      setIsSaving(false)
      if (updated) {
        setSaveMessage(currentTexts.saved)
      } else {
        setSaveMessage(currentTexts.saved)
      }

      // إخفاء رسالة النجاح بعد 3 ثوان
      setTimeout(() => {
        setSaveMessage('')
      }, 3000)
    } catch (error) {
      console.error('Error saving API keys to localStorage:', error)
      setIsSaving(false)
      setSaveMessage(currentTexts.error)
    }
  }



  return (
    <div>
      <div className="dashboard-header">
        <h1 className="page-title">{currentTexts.title}</h1>
      </div>

      <div className="settings-tabs">
        <button
          className={`settings-tab ${activeTab === 'api' ? 'active' : ''}`}
          onClick={() => setActiveTab('api')}
        >
          <span className="tab-icon">🔑</span>
          <span className="tab-text">{currentTexts.apiSettings}</span>
        </button>
        <button
          className={`settings-tab ${activeTab === 'general' ? 'active' : ''}`}
          onClick={() => setActiveTab('general')}
        >
          <span className="tab-icon">⚙️</span>
          <span className="tab-text">{currentTexts.generalSettings}</span>
        </button>
        <button
          className={`settings-tab ${activeTab === 'notifications' ? 'active' : ''}`}
          onClick={() => setActiveTab('notifications')}
        >
          <span className="tab-icon">🔔</span>
          <span className="tab-text">{currentTexts.notifications}</span>
        </button>
      </div>
      
      {activeTab === 'api' && (
        <>
          <div className="card">
            <div className="card-header">
              <h2 className="card-title">{currentTexts.apiSettings}</h2>
              <a href="https://portals.aliexpress.com/help.htm" target="_blank" rel="noopener noreferrer" className="help-link">
                <span className="help-icon">❓</span>
                <span>{currentTexts.help}</span>
              </a>
            </div>
            
            {isLoading ? (
              <div className="form-loading"></div>
            ) : (
              <form onSubmit={handleSubmit}>
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="appKey">{currentTexts.appKey}</label>
                    <div className="input-wrapper">
                      <input
                        type="text"
                        id="appKey"
                        name="appKey"
                        value={apiKeys.appKey}
                        onChange={handleChange}
                        placeholder={currentTexts.enterAppKey}
                        required
                      />
                      <div className="input-icon">🔑</div>
                    </div>
                    <div className="form-hint">{currentTexts.apiKeysDescription}</div>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="secretKey">{currentTexts.secretKey}</label>
                    <div className="input-wrapper">
                      <input
                        type="password"
                        id="secretKey"
                        name="secretKey"
                        value={apiKeys.secretKey}
                        onChange={handleChange}
                        placeholder={currentTexts.enterSecretKey}
                        required
                      />
                      <div className="input-icon">🔒</div>
                    </div>
                    <div className="form-hint">{currentTexts.apiKeysDescription}</div>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="tagId">{currentTexts.tagId}</label>
                    <div className="input-wrapper">
                      <input
                        type="text"
                        id="tagId"
                        name="tagId"
                        value={apiKeys.tagId}
                        onChange={handleChange}
                        placeholder={currentTexts.enterTagId}
                        required
                      />
                      <div className="input-icon">🏷️</div>
                    </div>
                    <div className="form-hint">{currentTexts.apiKeysDescription}</div>
                  </div>
                </div>
                
                <div className="form-actions">
                  <button 
                    type="submit" 
                    className="btn btn-primary" 
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <>
                        <span className="spinner"></span>
                        <span>{currentTexts.saving}</span>
                      </>
                    ) : (
                      currentTexts.save
                    )}
                  </button>
                  
                  <button 
                    type="button" 
                    className="btn btn-secondary"
                    onClick={() => {
                      // إعادة تعيين الحقول إلى القيم المحفوظة
                      try {
                        const savedApiKeys = localStorage.getItem('aliexpressApiKeys')
                        if (savedApiKeys) {
                          setApiKeys(JSON.parse(savedApiKeys))
                        } else {
                          setApiKeys({
                            appKey: '',
                            secretKey: '',
                            tagId: ''
                          })
                        }
                      } catch (error) {
                        console.error('Error resetting fields:', error)
                      }
                    }}
                  >
                    إعادة تعيين
                  </button>
                </div>
                
                {saveMessage && (
                  <div className="save-message success">
                    <span className="message-icon">✓</span>
                    <span>{saveMessage}</span>
                  </div>
                )}
              </form>
            )}
          </div>
          
          <div className="card">
            <div className="card-header">
              <h2 className="card-title">{currentTexts.testConnection}</h2>
            </div>

            <div className="api-test">
              <p>{currentTexts.testConnectionDesc}</p>
              <button
                className="btn btn-secondary"
                onClick={() => {
                  // محاكاة اختبار الاتصال
                  setSaveMessage('جاري اختبار الاتصال...')

                  setTimeout(() => {
                    if (apiKeys.appKey && apiKeys.secretKey && apiKeys.tagId) {
                      setSaveMessage('تم الاتصال بنجاح!')
                    } else {
                      setSaveMessage('فشل في الاتصال - تحقق من البيانات')
                    }

                    // إخفاء الرسالة بعد 3 ثوان
                    setTimeout(() => {
                      setSaveMessage('')
                    }, 3000)
                  }, 1500)
                }}
              >
                {currentTexts.testConnection}
              </button>
            </div>
          </div>
        </>
      )}
      
      {activeTab === 'general' && (
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">{currentTexts.generalSettings}</h2>
          </div>

          <div className="settings-placeholder">
            <p>{currentTexts.generalDescription}</p>
          </div>
        </div>
      )}

      {activeTab === 'notifications' && (
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">{currentTexts.notifications}</h2>
          </div>

          <div className="settings-placeholder">
            <p>{currentTexts.notificationsComingSoon}</p>
          </div>
        </div>
      )}
    </div>
  )
}