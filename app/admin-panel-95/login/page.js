'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function AdminLoginRedirect() {
  const router = useRouter();
  
  useEffect(() => {
    // إعادة توجيه إلى الرابط الصحيح
    router.replace('/admin-panel-95/admin-login');
  }, [router]);

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '100vh',
      backgroundColor: '#f5f7fa',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{
        textAlign: 'center',
        padding: '2rem',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)'
      }}>
        <h2 style={{ color: '#333', marginBottom: '1rem' }}>جاري إعادة التوجيه...</h2>
        <p style={{ color: '#666' }}>سيتم توجيهك إلى صفحة تسجيل الدخول الصحيحة</p>
        <div style={{
          marginTop: '1rem',
          padding: '0.5rem',
          backgroundColor: '#e3f2fd',
          borderRadius: '4px',
          fontSize: '0.9rem',
          color: '#1976d2'
        }}>
          الرابط الصحيح: /admin-panel-95/admin-login
        </div>
      </div>
    </div>
  );
}