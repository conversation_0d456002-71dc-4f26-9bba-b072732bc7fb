/* Google Analytics Settings Page Styles */

.analytics-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 30px;
  text-align: center;
  border-bottom: 1px solid #e1e5e9;
  padding-bottom: 20px;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 1rem;
  color: #6c757d;
  margin: 0;
  line-height: 1.5;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Alert Messages */
.alert {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  font-size: 0.95rem;
}

.alert-success {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.alert-error {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.alert-icon {
  margin-right: 8px;
  font-size: 1.1rem;
}

[dir="rtl"] .alert-icon {
  margin-right: 0;
  margin-left: 8px;
}

/* Settings Container */
.settings-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.settings-section {
  padding: 24px;
  border-bottom: 1px solid #e9ecef;
}

.settings-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 20px;
  background: #007bff;
  margin-right: 12px;
  border-radius: 2px;
}

[dir="rtl"] .section-title::before {
  margin-right: 0;
  margin-left: 12px;
}

/* Form Elements */
.form-group {
  margin-bottom: 20px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-weight: 500;
  color: #495057;
  margin-bottom: 6px;
  font-size: 0.95rem;
}

.label-optional {
  font-weight: 400;
  color: #6c757d;
  font-size: 0.85rem;
  margin-left: 4px;
}

[dir="rtl"] .label-optional {
  margin-left: 0;
  margin-right: 4px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 0.95rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  background-color: #fff;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.form-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-input:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

.form-help {
  font-size: 0.85rem;
  color: #6c757d;
  margin-top: 6px;
  margin-bottom: 0;
  line-height: 1.4;
}

/* Checkbox Styles */
.checkbox-label {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  font-size: 0.95rem;
  line-height: 1.4;
}

.checkbox-input {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #ced4da;
  border-radius: 4px;
  margin-right: 12px;
  margin-top: 2px;
  position: relative;
  transition: all 0.15s ease-in-out;
  flex-shrink: 0;
}

[dir="rtl"] .checkbox-custom {
  margin-right: 0;
  margin-left: 12px;
}

.checkbox-input:checked + .checkbox-custom {
  background-color: #007bff;
  border-color: #007bff;
}

.checkbox-input:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.checkbox-input:disabled + .checkbox-custom {
  background-color: #f8f9fa;
  border-color: #dee2e6;
  cursor: not-allowed;
}

.checkbox-text {
  color: #495057;
  font-weight: 500;
}

/* Info Box */
.info-box {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 20px;
  margin-top: 16px;
}

.info-box h3 {
  color: #495057;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.info-box ol {
  margin: 0 0 16px 0;
  padding-left: 20px;
  color: #6c757d;
}

[dir="rtl"] .info-box ol {
  padding-left: 0;
  padding-right: 20px;
}

.info-box li {
  margin-bottom: 6px;
  line-height: 1.4;
}

.info-box a {
  color: #007bff;
  text-decoration: none;
}

.info-box a:hover {
  text-decoration: underline;
}

.info-box p {
  margin: 0;
  color: #495057;
  font-size: 0.9rem;
}

/* Action Buttons */
.settings-actions {
  padding: 20px 24px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

[dir="rtl"] .settings-actions {
  justify-content: flex-start;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  min-width: 140px;
  justify-content: center;
}

.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #545b62;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

.btn-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .analytics-page {
    padding: 15px;
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .settings-section {
    padding: 20px 16px;
  }
  
  .settings-actions {
    padding: 16px;
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
  
  .dashboard-header {
    text-align: right;
  }
  
  [dir="ltr"] .dashboard-header {
    text-align: left;
  }
}

/* RTL Support */
[dir="rtl"] .form-input {
  text-align: right;
}

/* Focus and Hover Effects */
.form-input:hover:not(:disabled) {
  border-color: #80bdff;
}

.checkbox-label:hover .checkbox-custom:not(.checkbox-input:disabled + .checkbox-custom) {
  border-color: #007bff;
}

/* Animation for success/error messages */
.alert {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}