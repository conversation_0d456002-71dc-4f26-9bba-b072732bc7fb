'use client'

import { useState, useEffect } from 'react'
import './analytics.css'
import { useTranslation } from '../../components/TranslationProvider'

export default function GoogleAnalyticsSettings() {
  const { t, language, direction } = useTranslation();
  const [isClient, setIsClient] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [formData, setFormData] = useState({
    enabled: true,
    ga4MeasurementId: '',
    universalTrackingId: '',
    respectDoNotTrack: true,
    anonymizeIp: true
  });

  // تعيين أن المكون يُعرض من جانب العميل
  useEffect(() => {
    setIsClient(true);
  }, []);

  // تحميل الإعدادات المحفوظة
  useEffect(() => {
    if (isClient) {
      loadSettings();
    }
  }, [isClient]);

  const loadSettings = async () => {
    setIsLoading(true);
    try {
      // محاولة تحميل الإعدادات من API
      const response = await fetch('/api/admin/analytics-settings');
      if (response.ok) {
        const data = await response.json();
        setFormData(prev => ({ ...prev, ...data }));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      // تحميل من localStorage كبديل
      const savedSettings = localStorage.getItem('google_analytics_settings');
      if (savedSettings) {
        setFormData(prev => ({ ...prev, ...JSON.parse(savedSettings) }));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // مسح الرسائل عند التغيير
    if (successMessage) setSuccessMessage('');
    if (errorMessage) setErrorMessage('');
  };

  const validateForm = () => {
    if (formData.enabled) {
      if (!formData.ga4MeasurementId && !formData.universalTrackingId) {
        setErrorMessage(t('analytics.settings.errors.noTrackingId'));
        return false;
      }

      if (formData.ga4MeasurementId && !formData.ga4MeasurementId.match(/^G-[A-Z0-9]+$/)) {
        setErrorMessage(t('analytics.settings.errors.invalidGA4Id'));
        return false;
      }

      if (formData.universalTrackingId && !formData.universalTrackingId.match(/^UA-\d+-\d+$/)) {
        setErrorMessage(t('analytics.settings.errors.invalidUniversalId'));
        return false;
      }
    }
    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;
    
    setIsSaving(true);
    setErrorMessage('');
    setSuccessMessage('');
    
    try {
      // محاولة حفظ في API
      const response = await fetch('/api/admin/analytics-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (response.ok) {
        setSuccessMessage(t('analytics.settings.messages.saveSuccess'));
      } else {
        throw new Error('API save failed');
      }
    } catch (error) {
      console.error('Error saving to API:', error);
      // حفظ في localStorage كبديل
      localStorage.setItem('google_analytics_settings', JSON.stringify(formData));
      setSuccessMessage(t('analytics.settings.messages.saveLocalSuccess'));
    } finally {
      setIsSaving(false);
    }
  };

  const testConnection = async () => {
    if (!formData.ga4MeasurementId && !formData.universalTrackingId) {
      setErrorMessage('يجب إدخال معرف تتبع لاختبار الاتصال');
      return;
    }
    
    setIsTestingConnection(true);
    setErrorMessage('');
    setSuccessMessage('');
    
    try {
      // محاكاة اختبار الاتصال
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // في التطبيق الحقيقي، ستقوم بإرسال طلب لاختبار صحة المعرف
      const isValid = Math.random() > 0.2; // محاكاة نتيجة عشوائية
      
      if (isValid) {
        setSuccessMessage(t('analytics.settings.messages.connectionSuccess'));
      } else {
        setErrorMessage(t('analytics.settings.messages.connectionFailed'));
      }
    } catch (error) {
      setErrorMessage(t('analytics.settings.messages.connectionError'));
    } finally {
      setIsTestingConnection(false);
    }
  };

  // عدم عرض المكون حتى يتم تحميله من جانب العميل
  if (!isClient) {
    return (
      <div className="analytics-page">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="analytics-page">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="analytics-page" dir={direction}>
      <div className="dashboard-header">
        <h1 className="page-title">{t('analytics.settings.title')}</h1>
        <p className="page-subtitle">{t('analytics.settings.subtitle')}</p>
      </div>

      {/* رسائل النجاح والخطأ */}
      {successMessage && (
        <div className="alert alert-success">
          <span className="alert-icon">✅</span>
          {successMessage}
        </div>
      )}
      
      {errorMessage && (
        <div className="alert alert-error">
          <span className="alert-icon">❌</span>
          {errorMessage}
        </div>
      )}

      <div className="settings-container">
        {/* تفعيل Google Analytics */}
        <div className="settings-section">
          <h2 className="section-title">{t('analytics.settings.basicSettings')}</h2>

          <div className="form-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                name="enabled"
                checked={formData.enabled}
                onChange={handleInputChange}
                className="checkbox-input"
              />
              <span className="checkbox-custom"></span>
              <span className="checkbox-text">{t('analytics.settings.enableAnalytics')}</span>
            </label>
            <p className="form-help">{t('analytics.settings.enableAnalyticsHelp')}</p>
          </div>
        </div>

        {/* معرفات التتبع */}
        <div className="settings-section">
          <h2 className="section-title">{t('analytics.settings.trackingIds')}</h2>
          
          <div className="form-group">
            <label className="form-label">
              {t('analytics.settings.ga4MeasurementId')}
              <span className="label-optional">({t('common.optional')})</span>
            </label>
            <input
              type="text"
              name="ga4MeasurementId"
              value={formData.ga4MeasurementId}
              onChange={handleInputChange}
              placeholder="G-XXXXXXXXXX"
              className="form-input"
              disabled={!formData.enabled}
            />
            <p className="form-help">{t('analytics.settings.ga4Help')}</p>
          </div>

          <div className="form-group">
            <label className="form-label">
              {t('analytics.settings.universalTrackingId')}
              <span className="label-optional">({t('common.optional')})</span>
            </label>
            <input
              type="text"
              name="universalTrackingId"
              value={formData.universalTrackingId}
              onChange={handleInputChange}
              placeholder="UA-XXXXXXXXX-X"
              className="form-input"
              disabled={!formData.enabled}
            />
            <p className="form-help">{t('analytics.settings.universalHelp')}</p>
          </div>
        </div>

        {/* إعدادات الخصوصية */}
        <div className="settings-section">
          <h2 className="section-title">{t('analytics.settings.privacySettings')}</h2>
          
          <div className="form-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                name="respectDoNotTrack"
                checked={formData.respectDoNotTrack}
                onChange={handleInputChange}
                className="checkbox-input"
                disabled={!formData.enabled}
              />
              <span className="checkbox-custom"></span>
              <span className="checkbox-text">{t('analytics.settings.respectDoNotTrack')}</span>
            </label>
            <p className="form-help">{t('analytics.settings.respectDoNotTrackHelp')}</p>
          </div>

          <div className="form-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                name="anonymizeIp"
                checked={formData.anonymizeIp}
                onChange={handleInputChange}
                className="checkbox-input"
                disabled={!formData.enabled}
              />
              <span className="checkbox-custom"></span>
              <span className="checkbox-text">{t('analytics.settings.anonymizeIp')}</span>
            </label>
            <p className="form-help">{t('analytics.settings.anonymizeIpHelp')}</p>
          </div>
        </div>

        {/* معلومات إضافية */}
        <div className="settings-section">
          <h2 className="section-title">{t('analytics.settings.importantInfo')}</h2>
          <div className="info-box">
            <h3>{t('analytics.settings.howToGetId')}</h3>
            <ol>
              <li>{t('analytics.settings.step1')} <a href="https://analytics.google.com" target="_blank" rel="noopener noreferrer">Google Analytics</a></li>
              <li>{t('analytics.settings.step2')}</li>
              <li>{t('analytics.settings.step3')}</li>
              <li>{t('analytics.settings.step4')}</li>
              <li>{t('analytics.settings.step5')}</li>
            </ol>
            <p><strong>{t('common.note')}:</strong> {t('analytics.settings.finalNote')}</p>
          </div>
        </div>

        {/* أزرار الإجراءات */}
        <div className="settings-actions">
          <button
            onClick={testConnection}
            disabled={!formData.enabled || isTestingConnection || (!formData.ga4MeasurementId && !formData.universalTrackingId)}
            className="btn btn-secondary"
          >
            {isTestingConnection ? (
              <>
                <span className="btn-spinner"></span>
                {t('analytics.settings.testing')}
              </>
            ) : (
              t('analytics.settings.testConnection')
            )}
          </button>
          
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="btn btn-primary"
          >
            {isSaving ? (
              <>
                <span className="btn-spinner"></span>
                {t('common.saving')}
              </>
            ) : (
              t('analytics.settings.saveSettings')
            )}
          </button>
        </div>
      </div>
    </div>
  );
}