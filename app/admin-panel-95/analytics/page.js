'use client'

import { useState, useEffect } from 'react'
import './analytics.css'
import { useTranslation } from '../../components/TranslationProvider'

export default function GoogleAnalyticsSettings() {
  const { t, language, direction } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [formData, setFormData] = useState({
    enabled: false,
    ga4MeasurementId: '',
    universalTrackingId: '',
    respectDoNotTrack: true,
    anonymizeIp: true
  });

  // تحميل الإعدادات المحفوظة
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setIsLoading(true);
    try {
      // محاولة تحميل الإعدادات من API
      const response = await fetch('/api/admin/analytics-settings');
      if (response.ok) {
        const data = await response.json();
        setFormData(prev => ({ ...prev, ...data }));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      // تحميل من localStorage كبديل
      const savedSettings = localStorage.getItem('google_analytics_settings');
      if (savedSettings) {
        setFormData(prev => ({ ...prev, ...JSON.parse(savedSettings) }));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // مسح الرسائل عند التغيير
    if (successMessage) setSuccessMessage('');
    if (errorMessage) setErrorMessage('');
  };

  const validateForm = () => {
    if (formData.enabled) {
      if (!formData.ga4MeasurementId && !formData.universalTrackingId) {
        setErrorMessage('يجب إدخال معرف GA4 أو Universal Analytics على الأقل');
        return false;
      }
      
      if (formData.ga4MeasurementId && !formData.ga4MeasurementId.match(/^G-[A-Z0-9]+$/)) {
        setErrorMessage('معرف GA4 غير صحيح. يجب أن يكون بالشكل: G-XXXXXXXXXX');
        return false;
      }
      
      if (formData.universalTrackingId && !formData.universalTrackingId.match(/^UA-\d+-\d+$/)) {
        setErrorMessage('معرف Universal Analytics غير صحيح. يجب أن يكون بالشكل: UA-XXXXXXXXX-X');
        return false;
      }
    }
    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;
    
    setIsSaving(true);
    setErrorMessage('');
    setSuccessMessage('');
    
    try {
      // محاولة حفظ في API
      const response = await fetch('/api/admin/analytics-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (response.ok) {
        setSuccessMessage('تم حفظ إعدادات Google Analytics بنجاح');
      } else {
        throw new Error('API save failed');
      }
    } catch (error) {
      console.error('Error saving to API:', error);
      // حفظ في localStorage كبديل
      localStorage.setItem('google_analytics_settings', JSON.stringify(formData));
      setSuccessMessage('تم حفظ الإعدادات محلياً');
    } finally {
      setIsSaving(false);
    }
  };

  const testConnection = async () => {
    if (!formData.ga4MeasurementId && !formData.universalTrackingId) {
      setErrorMessage('يجب إدخال معرف تتبع لاختبار الاتصال');
      return;
    }
    
    setIsTestingConnection(true);
    setErrorMessage('');
    setSuccessMessage('');
    
    try {
      // محاكاة اختبار الاتصال
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // في التطبيق الحقيقي، ستقوم بإرسال طلب لاختبار صحة المعرف
      const isValid = Math.random() > 0.2; // محاكاة نتيجة عشوائية
      
      if (isValid) {
        setSuccessMessage('تم التحقق من صحة معرف التتبع بنجاح');
      } else {
        setErrorMessage('فشل في التحقق من معرف التتبع');
      }
    } catch (error) {
      setErrorMessage('حدث خطأ أثناء اختبار الاتصال');
    } finally {
      setIsTestingConnection(false);
    }
  };

  if (isLoading) {
    return (
      <div className="analytics-page">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>جاري التحميل...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="analytics-page" dir={direction}>
      <div className="dashboard-header">
        <h1 className="page-title">ربط Google Analytics</h1>
        <p className="page-subtitle">قم بربط موقعك مع Google Analytics لتتبع الزيارات والإحصائيات</p>
      </div>

      {/* رسائل النجاح والخطأ */}
      {successMessage && (
        <div className="alert alert-success">
          <span className="alert-icon">✅</span>
          {successMessage}
        </div>
      )}
      
      {errorMessage && (
        <div className="alert alert-error">
          <span className="alert-icon">❌</span>
          {errorMessage}
        </div>
      )}

      <div className="settings-container">
        {/* تفعيل Google Analytics */}
        <div className="settings-section">
          <h2 className="section-title">تفعيل التتبع</h2>
          
          <div className="form-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                name="enabled"
                checked={formData.enabled}
                onChange={handleInputChange}
                className="checkbox-input"
              />
              <span className="checkbox-custom"></span>
              <span className="checkbox-text">تفعيل Google Analytics</span>
            </label>
            <p className="form-help">تفعيل تتبع الزيارات والإحصائيات باستخدام Google Analytics</p>
          </div>
        </div>

        {/* معرفات التتبع */}
        <div className="settings-section">
          <h2 className="section-title">معرفات التتبع</h2>
          
          <div className="form-group">
            <label className="form-label">
              معرف Google Analytics 4 (GA4)
              <span className="label-optional">(اختياري)</span>
            </label>
            <input
              type="text"
              name="ga4MeasurementId"
              value={formData.ga4MeasurementId}
              onChange={handleInputChange}
              placeholder="G-XXXXXXXXXX"
              className="form-input"
              disabled={!formData.enabled}
            />
            <p className="form-help">معرف القياس الخاص بـ Google Analytics 4 (الإصدار الجديد المُوصى به)</p>
          </div>

          <div className="form-group">
            <label className="form-label">
              معرف Universal Analytics
              <span className="label-optional">(اختياري)</span>
            </label>
            <input
              type="text"
              name="universalTrackingId"
              value={formData.universalTrackingId}
              onChange={handleInputChange}
              placeholder="UA-XXXXXXXXX-X"
              className="form-input"
              disabled={!formData.enabled}
            />
            <p className="form-help">معرف التتبع الخاص بـ Universal Analytics (الإصدار القديم)</p>
          </div>
        </div>

        {/* إعدادات الخصوصية */}
        <div className="settings-section">
          <h2 className="section-title">إعدادات الخصوصية</h2>
          
          <div className="form-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                name="respectDoNotTrack"
                checked={formData.respectDoNotTrack}
                onChange={handleInputChange}
                className="checkbox-input"
                disabled={!formData.enabled}
              />
              <span className="checkbox-custom"></span>
              <span className="checkbox-text">احترام إعداد "عدم التتبع"</span>
            </label>
            <p className="form-help">عدم تتبع المستخدمين الذين فعلوا خيار "عدم التتبع" في متصفحهم</p>
          </div>

          <div className="form-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                name="anonymizeIp"
                checked={formData.anonymizeIp}
                onChange={handleInputChange}
                className="checkbox-input"
                disabled={!formData.enabled}
              />
              <span className="checkbox-custom"></span>
              <span className="checkbox-text">إخفاء عناوين IP</span>
            </label>
            <p className="form-help">إخفاء آخر جزء من عنوان IP للحفاظ على خصوصية المستخدمين</p>
          </div>
        </div>

        {/* معلومات إضافية */}
        <div className="settings-section">
          <h2 className="section-title">معلومات مهمة</h2>
          <div className="info-box">
            <h3>كيفية الحصول على معرف Google Analytics:</h3>
            <ol>
              <li>انتقل إلى <a href="https://analytics.google.com" target="_blank" rel="noopener noreferrer">Google Analytics</a></li>
              <li>قم بإنشاء حساب جديد أو سجل دخولك</li>
              <li>أضف موقعك الإلكتروني كخاصية جديدة</li>
              <li>انسخ معرف القياس (GA4) أو معرف التتبع (Universal Analytics)</li>
              <li>الصق المعرف في الحقل المناسب أعلاه</li>
            </ol>
            <p><strong>ملاحظة:</strong> بعد حفظ الإعدادات، ستتمكن من مشاهدة إحصائيات موقعك في لوحة تحكم Google Analytics.</p>
          </div>
        </div>

        {/* أزرار الإجراءات */}
        <div className="settings-actions">
          <button
            onClick={testConnection}
            disabled={!formData.enabled || isTestingConnection || (!formData.ga4MeasurementId && !formData.universalTrackingId)}
            className="btn btn-secondary"
          >
            {isTestingConnection ? (
              <>
                <span className="btn-spinner"></span>
                جاري الاختبار...
              </>
            ) : (
              'اختبار الاتصال'
            )}
          </button>
          
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="btn btn-primary"
          >
            {isSaving ? (
              <>
                <span className="btn-spinner"></span>
                جاري الحفظ...
              </>
            ) : (
              'حفظ الإعدادات'
            )}
          </button>
        </div>
      </div>
    </div>
  );
}