'use client'

import { useState, useEffect } from 'react'
import './analytics.css'
import { useTranslation } from '../../components/TranslationProvider'

export default function GoogleAnalyticsSettings() {
  const { language, direction } = useTranslation();

  // النصوص حسب اللغة
  const texts = {
    ar: {
      title: "إعدادات Google Analytics",
      description: "قم بتكوين Google Analytics لتتبع زوار موقعك",
      enabled: "تفعيل Google Analytics",
      ga4MeasurementId: "معرف القياس GA4",
      universalTrackingId: "معرف التتبع العام",
      respectDoNotTrack: "احترام إعداد عدم التتبع",
      anonymizeIp: "إخفاء عناوين IP",
      optional: "اختياري",
      save: "حفظ الإعدادات",
      saving: "جاري الحفظ...",
      saved: "تم حفظ الإعدادات بنجاح!",
      error: "حدث خطأ أثناء الحفظ",
      testConnection: "اختبار الاتصال",
      testing: "جاري الاختبار...",
      connectionSuccess: "تم اختبار الاتصال بنجاح!",
      connectionError: "فشل في اختبار الاتصال",
      ga4Placeholder: "G-XXXXXXXXXX",
      universalPlaceholder: "UA-XXXXXXXX-X",
      enabledDescription: "تفعيل أو إلغاء تفعيل تتبع Google Analytics",
      ga4Description: "معرف القياس الخاص بـ Google Analytics 4",
      universalDescription: "معرف التتبع للإصدار العام من Google Analytics",
      respectDoNotTrackDescription: "احترام إعداد المتصفح لعدم التتبع",
      anonymizeIpDescription: "إخفاء آخر أوكتيت من عنوان IP للمستخدمين"
    },
    en: {
      title: "Google Analytics Settings",
      description: "Configure Google Analytics to track your website visitors",
      enabled: "Enable Google Analytics",
      ga4MeasurementId: "GA4 Measurement ID",
      universalTrackingId: "Universal Tracking ID",
      respectDoNotTrack: "Respect Do Not Track",
      anonymizeIp: "Anonymize IP Addresses",
      optional: "Optional",
      save: "Save Settings",
      saving: "Saving...",
      saved: "Settings saved successfully!",
      error: "Error occurred while saving",
      testConnection: "Test Connection",
      testing: "Testing...",
      connectionSuccess: "Connection tested successfully!",
      connectionError: "Connection test failed",
      ga4Placeholder: "G-XXXXXXXXXX",
      universalPlaceholder: "UA-XXXXXXXX-X",
      enabledDescription: "Enable or disable Google Analytics tracking",
      ga4Description: "Google Analytics 4 Measurement ID",
      universalDescription: "Universal Analytics Tracking ID",
      respectDoNotTrackDescription: "Respect browser's Do Not Track setting",
      anonymizeIpDescription: "Anonymize the last octet of user IP addresses"
    },
    fr: {
      title: "Paramètres Google Analytics",
      description: "Configurez Google Analytics pour suivre les visiteurs de votre site",
      enabled: "Activer Google Analytics",
      ga4MeasurementId: "ID de Mesure GA4",
      universalTrackingId: "ID de Suivi Universel",
      respectDoNotTrack: "Respecter Ne Pas Suivre",
      anonymizeIp: "Anonymiser les Adresses IP",
      optional: "Optionnel",
      save: "Enregistrer les Paramètres",
      saving: "Enregistrement...",
      saved: "Paramètres enregistrés avec succès!",
      error: "Erreur lors de l'enregistrement",
      testConnection: "Tester la Connexion",
      testing: "Test en cours...",
      connectionSuccess: "Connexion testée avec succès!",
      connectionError: "Échec du test de connexion",
      ga4Placeholder: "G-XXXXXXXXXX",
      universalPlaceholder: "UA-XXXXXXXX-X",
      enabledDescription: "Activer ou désactiver le suivi Google Analytics",
      ga4Description: "ID de mesure Google Analytics 4",
      universalDescription: "ID de suivi Universal Analytics",
      respectDoNotTrackDescription: "Respecter le paramètre Ne Pas Suivre du navigateur",
      anonymizeIpDescription: "Anonymiser le dernier octet des adresses IP des utilisateurs"
    },
    es: {
      title: "Configuración de Google Analytics",
      description: "Configure Google Analytics para rastrear los visitantes de su sitio web",
      enabled: "Habilitar Google Analytics",
      ga4MeasurementId: "ID de Medición GA4",
      universalTrackingId: "ID de Seguimiento Universal",
      respectDoNotTrack: "Respetar No Rastrear",
      anonymizeIp: "Anonimizar Direcciones IP",
      optional: "Opcional",
      save: "Guardar Configuración",
      saving: "Guardando...",
      saved: "¡Configuración guardada exitosamente!",
      error: "Error al guardar",
      testConnection: "Probar Conexión",
      testing: "Probando...",
      connectionSuccess: "¡Conexión probada exitosamente!",
      connectionError: "Falló la prueba de conexión",
      ga4Placeholder: "G-XXXXXXXXXX",
      universalPlaceholder: "UA-XXXXXXXX-X",
      enabledDescription: "Habilitar o deshabilitar el seguimiento de Google Analytics",
      ga4Description: "ID de medición de Google Analytics 4",
      universalDescription: "ID de seguimiento de Universal Analytics",
      respectDoNotTrackDescription: "Respetar la configuración No Rastrear del navegador",
      anonymizeIpDescription: "Anonimizar el último octeto de las direcciones IP de los usuarios"
    },
    uk: {
      title: "Налаштування Google Analytics",
      description: "Налаштуйте Google Analytics для відстеження відвідувачів вашого сайту",
      enabled: "Увімкнути Google Analytics",
      ga4MeasurementId: "ID Вимірювання GA4",
      universalTrackingId: "Універсальний ID Відстеження",
      respectDoNotTrack: "Поважати Не Відстежувати",
      anonymizeIp: "Анонімізувати IP-адреси",
      optional: "Необов'язково",
      save: "Зберегти Налаштування",
      saving: "Збереження...",
      saved: "Налаштування успішно збережено!",
      error: "Помилка при збереженні",
      testConnection: "Тест З'єднання",
      testing: "Тестування...",
      connectionSuccess: "З'єднання успішно протестовано!",
      connectionError: "Тест з'єднання не вдався",
      ga4Placeholder: "G-XXXXXXXXXX",
      universalPlaceholder: "UA-XXXXXXXX-X",
      enabledDescription: "Увімкнути або вимкнути відстеження Google Analytics",
      ga4Description: "ID вимірювання Google Analytics 4",
      universalDescription: "ID відстеження Universal Analytics",
      respectDoNotTrackDescription: "Поважати налаштування браузера Не Відстежувати",
      anonymizeIpDescription: "Анонімізувати останній октет IP-адрес користувачів"
    }
  };

  const currentTexts = texts[language] || texts.ar;
  const [isClient, setIsClient] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [formData, setFormData] = useState({
    enabled: true,
    ga4MeasurementId: '',
    universalTrackingId: '',
    respectDoNotTrack: true,
    anonymizeIp: true
  });

  // تعيين أن المكون يُعرض من جانب العميل
  useEffect(() => {
    setIsClient(true);
  }, []);

  // تحميل الإعدادات المحفوظة
  useEffect(() => {
    if (isClient) {
      loadSettings();
    }
  }, [isClient]);

  const loadSettings = async () => {
    setIsLoading(true);
    try {
      // محاولة تحميل الإعدادات من API
      const response = await fetch('/api/admin/analytics-settings');
      if (response.ok) {
        const data = await response.json();
        setFormData(prev => ({ ...prev, ...data }));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      // تحميل من localStorage كبديل
      const savedSettings = localStorage.getItem('google_analytics_settings');
      if (savedSettings) {
        setFormData(prev => ({ ...prev, ...JSON.parse(savedSettings) }));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // مسح الرسائل عند التغيير
    if (successMessage) setSuccessMessage('');
    if (errorMessage) setErrorMessage('');
  };

  const validateForm = () => {
    if (formData.enabled) {
      if (!formData.ga4MeasurementId && !formData.universalTrackingId) {
        setErrorMessage(currentTexts.error);
        return false;
      }

      if (formData.ga4MeasurementId && !formData.ga4MeasurementId.match(/^G-[A-Z0-9]+$/)) {
        setErrorMessage(currentTexts.error);
        return false;
      }

      if (formData.universalTrackingId && !formData.universalTrackingId.match(/^UA-\d+-\d+$/)) {
        setErrorMessage(currentTexts.error);
        return false;
      }
    }
    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;
    
    setIsSaving(true);
    setErrorMessage('');
    setSuccessMessage('');
    
    try {
      // محاولة حفظ في API
      const response = await fetch('/api/admin/analytics-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (response.ok) {
        setSuccessMessage(currentTexts.saved);
      } else {
        throw new Error('API save failed');
      }
    } catch (error) {
      console.error('Error saving to API:', error);
      // حفظ في localStorage كبديل
      localStorage.setItem('google_analytics_settings', JSON.stringify(formData));
      setSuccessMessage(currentTexts.saved);
    } finally {
      setIsSaving(false);
    }
  };

  const testConnection = async () => {
    if (!formData.ga4MeasurementId && !formData.universalTrackingId) {
      setErrorMessage('يجب إدخال معرف تتبع لاختبار الاتصال');
      return;
    }
    
    setIsTestingConnection(true);
    setErrorMessage('');
    setSuccessMessage('');
    
    try {
      // محاكاة اختبار الاتصال
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // في التطبيق الحقيقي، ستقوم بإرسال طلب لاختبار صحة المعرف
      const isValid = Math.random() > 0.2; // محاكاة نتيجة عشوائية
      
      if (isValid) {
        setSuccessMessage(currentTexts.connectionSuccess);
      } else {
        setErrorMessage(currentTexts.connectionError);
      }
    } catch (error) {
      setErrorMessage(currentTexts.connectionError);
    } finally {
      setIsTestingConnection(false);
    }
  };

  // عدم عرض المكون حتى يتم تحميله من جانب العميل
  if (!isClient) {
    return (
      <div className="analytics-page">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="analytics-page">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>جاري التحميل...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="analytics-page" dir={direction}>
      <div className="dashboard-header">
        <h1 className="page-title">{currentTexts.title}</h1>
        <p className="page-subtitle">{currentTexts.description}</p>
      </div>

      {/* رسائل النجاح والخطأ */}
      {successMessage && (
        <div className="alert alert-success">
          <span className="alert-icon">✅</span>
          {successMessage}
        </div>
      )}
      
      {errorMessage && (
        <div className="alert alert-error">
          <span className="alert-icon">❌</span>
          {errorMessage}
        </div>
      )}

      <div className="settings-container">
        {/* تفعيل Google Analytics */}
        <div className="settings-section">
          <h2 className="section-title">الإعدادات الأساسية</h2>

          <div className="form-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                name="enabled"
                checked={formData.enabled}
                onChange={handleInputChange}
                className="checkbox-input"
              />
              <span className="checkbox-custom"></span>
              <span className="checkbox-text">{currentTexts.enabled}</span>
            </label>
            <p className="form-help">{currentTexts.enabledDescription}</p>
          </div>
        </div>

        {/* معرفات التتبع */}
        <div className="settings-section">
          <h2 className="section-title">معرفات التتبع</h2>
          
          <div className="form-group">
            <label className="form-label">
              {currentTexts.ga4MeasurementId}
              <span className="label-optional">({currentTexts.optional})</span>
            </label>
            <input
              type="text"
              name="ga4MeasurementId"
              value={formData.ga4MeasurementId}
              onChange={handleInputChange}
              placeholder="G-XXXXXXXXXX"
              className="form-input"
              disabled={!formData.enabled}
            />
            <p className="form-help">{currentTexts.ga4Description}</p>
          </div>

          <div className="form-group">
            <label className="form-label">
              {currentTexts.universalTrackingId}
              <span className="label-optional">({currentTexts.optional})</span>
            </label>
            <input
              type="text"
              name="universalTrackingId"
              value={formData.universalTrackingId}
              onChange={handleInputChange}
              placeholder="UA-XXXXXXXXX-X"
              className="form-input"
              disabled={!formData.enabled}
            />
            <p className="form-help">{currentTexts.universalDescription}</p>
          </div>
        </div>

        {/* إعدادات الخصوصية */}
        <div className="settings-section">
          <h2 className="section-title">إعدادات الخصوصية</h2>
          
          <div className="form-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                name="respectDoNotTrack"
                checked={formData.respectDoNotTrack}
                onChange={handleInputChange}
                className="checkbox-input"
                disabled={!formData.enabled}
              />
              <span className="checkbox-custom"></span>
              <span className="checkbox-text">{currentTexts.respectDoNotTrack}</span>
            </label>
            <p className="form-help">{currentTexts.respectDoNotTrackDescription}</p>
          </div>

          <div className="form-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                name="anonymizeIp"
                checked={formData.anonymizeIp}
                onChange={handleInputChange}
                className="checkbox-input"
                disabled={!formData.enabled}
              />
              <span className="checkbox-custom"></span>
              <span className="checkbox-text">{currentTexts.anonymizeIp}</span>
            </label>
            <p className="form-help">{currentTexts.anonymizeIpDescription}</p>
          </div>
        </div>

        {/* معلومات إضافية */}
        <div className="settings-section">
          <h2 className="section-title">{t('analytics.settings.importantInfo')}</h2>
          <div className="info-box">
            <h3>{t('analytics.settings.howToGetId')}</h3>
            <ol>
              <li>{t('analytics.settings.step1')} <a href="https://analytics.google.com" target="_blank" rel="noopener noreferrer">Google Analytics</a></li>
              <li>{t('analytics.settings.step2')}</li>
              <li>{t('analytics.settings.step3')}</li>
              <li>{t('analytics.settings.step4')}</li>
              <li>{t('analytics.settings.step5')}</li>
            </ol>
            <p><strong>{t('common.note')}:</strong> {t('analytics.settings.finalNote')}</p>
          </div>
        </div>

        {/* أزرار الإجراءات */}
        <div className="settings-actions">
          <button
            onClick={testConnection}
            disabled={!formData.enabled || isTestingConnection || (!formData.ga4MeasurementId && !formData.universalTrackingId)}
            className="btn btn-secondary"
          >
            {isTestingConnection ? (
              <>
                <span className="btn-spinner"></span>
                {currentTexts.testing}
              </>
            ) : (
              currentTexts.testConnection
            )}
          </button>
          
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="btn btn-primary"
          >
            {isSaving ? (
              <>
                <span className="btn-spinner"></span>
                {currentTexts.saving}
              </>
            ) : (
              currentTexts.save
            )}
          </button>
        </div>
      </div>
    </div>
  );
}