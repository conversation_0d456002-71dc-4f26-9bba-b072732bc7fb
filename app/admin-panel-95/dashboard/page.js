"use client";

import { useState, useEffect } from "react";
import { useTranslation } from "../../components/TranslationProvider";
import { useRouter } from "next/navigation";

export default function Dashboard() {
  const router = useRouter();
  const { t } = useTranslation();

  // التحقق من حالة تسجيل الدخول كمسؤول
  useEffect(() => {
    const isAdminAuthenticated = localStorage.getItem("admin_authenticated");
    if (!isAdminAuthenticated || isAdminAuthenticated !== "true") {
      // إذا لم يكن المستخدم مسجل دخول كمسؤول، قم بتوجيهه إلى صفحة تسجيل الدخول
      router.push("/admin-panel-95/admin-login");
    }
  }, [router]);
  const [stats, setStats] = useState({
    visits: 0,
    clicks: 0,
    conversionRate: 0,
    revenue: 0,
  });

  const [isLoading, setIsLoading] = useState(true);

  // جلب البيانات الفعلية من API
  useEffect(() => {
    const fetchStats = async () => {
      try {
        // في المستقبل، سيتم جلب البيانات من API حقيقي
        // const response = await fetch('/api/admin/stats');
        // const data = await response.json();

        // حالياً نعرض بيانات فارغة حتى يتم ربط النظام بقاعدة البيانات
        setStats({
          visits: 0,
          clicks: 0,
          conversionRate: 0,
          revenue: 0,
        });
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching stats:", error);
        setStats({
          visits: 0,
          clicks: 0,
          conversionRate: 0,
          revenue: 0,
        });
        setIsLoading(false);
      }
    };

    fetchStats();
  }, []);

  return (
    <div>
      <div className="dashboard-header">
        <h1 className="page-title">{t("admin.dashboard.title")}</h1>
        <div className="date-filter">
          <select defaultValue="today" className="date-select">
            <option value="today">{t("admin.analytics.today")}</option>
            <option value="week">{t("admin.analytics.thisWeek")}</option>
            <option value="month">{t("admin.analytics.thisMonth")}</option>
            <option value="year">{t("admin.analytics.thisYear")}</option>
          </select>
        </div>
      </div>

      <div className="stats-grid">
        <div className="stat-card">
          {isLoading ? (
            <div className="stat-loading"></div>
          ) : (
            <>
              <h3 className="stat-label">{t("admin.analytics.visits")}</h3>
              <div className="stat-value">{stats.visits.toLocaleString()}</div>
              <p className="stat-description">
                {t("admin.dashboard.totalVisits")}
              </p>
              <div className="stat-trend">
                <span className="trend-icon">-</span>
                <span className="trend-value">0%</span>
                <span className="trend-period">
                  {t("admin.dashboard.comparedToPrevious")}
                </span>
              </div>
            </>
          )}
        </div>

        <div className="stat-card">
          {isLoading ? (
            <div className="stat-loading"></div>
          ) : (
            <>
              <h3 className="stat-label">{t("admin.dashboard.clicks")}</h3>
              <div className="stat-value">{stats.clicks.toLocaleString()}</div>
              <p className="stat-description">
                {t("admin.dashboard.totalClicks")}
              </p>
              <div className="stat-trend">
                <span className="trend-icon">-</span>
                <span className="trend-value">0%</span>
                <span className="trend-period">
                  {t("admin.dashboard.comparedToPrevious")}
                </span>
              </div>
            </>
          )}
        </div>

        <div className="stat-card">
          {isLoading ? (
            <div className="stat-loading"></div>
          ) : (
            <>
              <h3 className="stat-label">
                {t("admin.analytics.conversionRate")}
              </h3>
              <div className="stat-value">{stats.conversionRate}%</div>
              <p className="stat-description">
                {t("admin.dashboard.clickToVisitRatio")}
              </p>
              <div className="stat-trend">
                <span className="trend-icon">-</span>
                <span className="trend-value">0%</span>
                <span className="trend-period">
                  {t("admin.dashboard.comparedToPrevious")}
                </span>
              </div>
            </>
          )}
        </div>

        <div className="stat-card">
          {isLoading ? (
            <div className="stat-loading"></div>
          ) : (
            <>
              <h3 className="stat-label">{t("admin.analytics.revenue")}</h3>
              <div className="stat-value">
                ${stats.revenue.toLocaleString()}
              </div>
              <p className="stat-description">
                {t("admin.dashboard.totalRevenue")}
              </p>
              <div className="stat-trend">
                <span className="trend-icon">-</span>
                <span className="trend-value">0%</span>
                <span className="trend-period">
                  {t("admin.dashboard.comparedToPrevious")}
                </span>
              </div>
            </>
          )}
        </div>
      </div>

      <div className="dashboard-row">
        <div className="card chart-card">
          <div className="card-header">
            <h2 className="card-title">
              {t("admin.dashboard.performanceOverview")}
            </h2>
            <div className="card-actions">
              <button
                className="card-action-btn active"
                onClick={(event) => {
                  const buttons = document.querySelectorAll(".card-action-btn");
                  buttons.forEach((btn) => btn.classList.remove("active"));
                  event.currentTarget.classList.add("active");

                  // Cambiar la visualización del gráfico a visitas
                  const chartBars = document.querySelectorAll(".chart-bar");
                  if (chartBars.length > 0) {
                    // Animación simple para mostrar el cambio
                    chartBars.forEach((bar, index) => {
                      setTimeout(() => {
                        bar.style.height = `${60 + Math.floor(Math.random() * 30)}%`;
                      }, index * 50);
                    });
                  }
                }}
              >
                {t("admin.analytics.visits")}
              </button>
              <button
                className="card-action-btn"
                onClick={(event) => {
                  const buttons = document.querySelectorAll(".card-action-btn");
                  buttons.forEach((btn) => btn.classList.remove("active"));
                  event.currentTarget.classList.add("active");

                  // Cambiar la visualización del gráfico a clics
                  const chartBars = document.querySelectorAll(".chart-bar");
                  if (chartBars.length > 0) {
                    // Animación simple para mostrar el cambio
                    chartBars.forEach((bar, index) => {
                      setTimeout(() => {
                        bar.style.height = `${40 + Math.floor(Math.random() * 40)}%`;
                      }, index * 50);
                    });
                  }
                }}
              >
                {t("admin.dashboard.clicks")}
              </button>
              <button
                className="card-action-btn"
                onClick={(event) => {
                  const buttons = document.querySelectorAll(".card-action-btn");
                  buttons.forEach((btn) => btn.classList.remove("active"));
                  event.currentTarget.classList.add("active");

                  // Cambiar la visualización del gráfico a ingresos
                  const chartBars = document.querySelectorAll(".chart-bar");
                  if (chartBars.length > 0) {
                    // Animación simple para mostrar el cambio
                    chartBars.forEach((bar, index) => {
                      setTimeout(() => {
                        bar.style.height = `${30 + Math.floor(Math.random() * 60)}%`;
                      }, index * 50);
                    });
                  }
                }}
              >
                {t("admin.dashboard.estimatedRevenue")}
              </button>
            </div>
          </div>
          <div className="card-content">
            {isLoading ? (
              <div className="chart-loading"></div>
            ) : (
              <div className="chart-placeholder">
                <div className="chart-bars">
                  <div
                    className="chart-bar"
                    style={{ height: "60%", transition: "height 0.3s ease" }}
                  ></div>
                  <div
                    className="chart-bar"
                    style={{ height: "80%", transition: "height 0.3s ease" }}
                  ></div>
                  <div
                    className="chart-bar"
                    style={{ height: "40%", transition: "height 0.3s ease" }}
                  ></div>
                  <div
                    className="chart-bar"
                    style={{ height: "70%", transition: "height 0.3s ease" }}
                  ></div>
                  <div
                    className="chart-bar"
                    style={{ height: "90%", transition: "height 0.3s ease" }}
                  ></div>
                  <div
                    className="chart-bar"
                    style={{ height: "50%", transition: "height 0.3s ease" }}
                  ></div>
                  <div
                    className="chart-bar"
                    style={{ height: "75%", transition: "height 0.3s ease" }}
                  ></div>
                </div>
                <div className="chart-labels">
                  <span>{t("time.sunday")}</span>
                  <span>{t("time.monday")}</span>
                  <span>{t("time.tuesday")}</span>
                  <span>{t("time.wednesday")}</span>
                  <span>{t("time.thursday")}</span>
                  <span>{t("time.friday")}</span>
                  <span>{t("time.saturday")}</span>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h2 className="card-title">
              {t("admin.dashboard.mostVisitedProducts")}
            </h2>
            <button className="view-all-btn">
              {t("admin.dashboard.viewAll")}
            </button>
          </div>
          <div className="card-content">
            {isLoading ? (
              <div className="list-loading"></div>
            ) : (
              <div className="product-list">
                <div className="product-item">
                  <div className="product-rank">1</div>
                  <div className="product-info">
                    <div className="product-name">سماعات بلوتوث لاسلكية</div>
                    <div className="product-category">
                      {t("offers.electronics")}
                    </div>
                  </div>
                  <div className="product-stats">
                    <div className="product-visits">
                      245 {t("admin.analytics.visits")}
                    </div>
                  </div>
                </div>
                <div className="product-item">
                  <div className="product-rank">2</div>
                  <div className="product-info">
                    <div className="product-name">ساعة ذكية متعددة الوظائف</div>
                    <div className="product-category">
                      {t("offers.electronics")}
                    </div>
                  </div>
                  <div className="product-stats">
                    <div className="product-visits">
                      198 {t("admin.analytics.visits")}
                    </div>
                  </div>
                </div>
                <div className="product-item">
                  <div className="product-rank">3</div>
                  <div className="product-info">
                    <div className="product-name">
                      حقيبة ظهر للكمبيوتر المحمول
                    </div>
                    <div className="product-category">
                      {t("admin.dashboard.bags")}
                    </div>
                  </div>
                  <div className="product-stats">
                    <div className="product-visits">
                      156 {t("admin.analytics.visits")}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h2 className="card-title">
            {t("admin.dashboard.latestActivities")}
          </h2>
          <button className="view-all-btn">
            {t("admin.dashboard.viewAll")}
          </button>
        </div>
        <div className="card-content">
          {isLoading ? (
            <div className="list-loading"></div>
          ) : (
            <div className="activity-list">
              <div className="activity-item">
                <div className="activity-icon visit-icon">👁️</div>
                <div className="activity-info">
                  <div className="activity-title">زيارة جديدة</div>
                  <div className="activity-details">
                    تمت زيارة "سماعات بلوتوث لاسلكية"
                  </div>
                </div>
                <div className="activity-time">منذ 5 دقائق</div>
              </div>
              <div className="activity-item">
                <div className="activity-icon click-icon">👆</div>
                <div className="activity-info">
                  <div className="activity-title">نقرة على رابط</div>
                  <div className="activity-details">
                    تم النقر على رابط "ساعة ذكية متعددة الوظائف"
                  </div>
                </div>
                <div className="activity-time">منذ 12 دقيقة</div>
              </div>
              <div className="activity-item">
                <div className="activity-icon visit-icon">👁️</div>
                <div className="activity-info">
                  <div className="activity-title">زيارة جديدة</div>
                  <div className="activity-details">
                    تمت زيارة "حقيبة ظهر للكمبيوتر المحمول"
                  </div>
                </div>
                <div className="activity-time">منذ 25 دقيقة</div>
              </div>
              <div className="activity-item">
                <div className="activity-icon click-icon">👆</div>
                <div className="activity-info">
                  <div className="activity-title">نقرة على رابط</div>
                  <div className="activity-details">
                    تم النقر على رابط "سماعات بلوتوث لاسلكية"
                  </div>
                </div>
                <div className="activity-time">منذ 38 دقيقة</div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
