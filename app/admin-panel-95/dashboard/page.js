"use client";

import { useState, useEffect } from "react";
import { useTranslation } from "../../components/TranslationProvider";
import { useRouter } from "next/navigation";

export default function Dashboard() {
  const router = useRouter();
  const { t, language, direction } = useTranslation();
  const [isClient, setIsClient] = useState(false);

  // النصوص حسب اللغة
  const texts = {
    ar: {
      title: "لوحة التحكم",
      welcome: "مرحباً بك في لوحة الإدارة",
      today: "اليوم",
      thisWeek: "هذا الأسبوع",
      thisMonth: "هذا الشهر",
      thisYear: "هذا العام",
      visits: "الزيارات",
      clicks: "النقرات",
      totalVisits: "إجمالي زيارات الموقع",
      totalClicks: "إجمالي النقرات على الروابط التابعة",
      clickToVisitRatio: "نسبة النقرات إلى الزيارات",
      totalRevenue: "إجمالي الإيرادات",
      comparedToPrevious: "مقارنة بالفترة السابقة",
      performanceOverview: "نظرة عامة على الأداء",
      mostVisitedProducts: "المنتجات الأكثر زيارة",
      latestActivities: "الأنشطة الأخيرة",
      viewAll: "عرض الكل",
      estimatedRevenue: "الإيرادات التقديرية",
      conversionRate: "معدل التحويل",
      revenue: "الإيرادات",
      bags: "الحقائب",
      electronics: "الإلكترونيات",
      sunday: "الأحد",
      monday: "الإثنين",
      tuesday: "الثلاثاء",
      wednesday: "الأربعاء",
      thursday: "الخميس",
      friday: "الجمعة",
      saturday: "السبت"
    },
    en: {
      title: "Dashboard",
      welcome: "Welcome to Admin Panel",
      today: "Today",
      thisWeek: "This Week",
      thisMonth: "This Month",
      thisYear: "This Year",
      visits: "Visits",
      clicks: "Clicks",
      totalVisits: "Total website visits",
      totalClicks: "Total clicks on affiliate links",
      clickToVisitRatio: "Click to visit ratio",
      totalRevenue: "Total revenue",
      comparedToPrevious: "Compared to previous period",
      performanceOverview: "Performance Overview",
      mostVisitedProducts: "Most Visited Products",
      latestActivities: "Latest Activities",
      viewAll: "View All",
      estimatedRevenue: "Estimated Revenue",
      conversionRate: "Conversion Rate",
      revenue: "Revenue",
      bags: "Bags",
      electronics: "Electronics",
      sunday: "Sunday",
      monday: "Monday",
      tuesday: "Tuesday",
      wednesday: "Wednesday",
      thursday: "Thursday",
      friday: "Friday",
      saturday: "Saturday"
    },
    fr: {
      title: "Tableau de bord",
      welcome: "Bienvenue dans le panneau d'administration",
      today: "Aujourd'hui",
      thisWeek: "Cette semaine",
      thisMonth: "Ce mois",
      thisYear: "Cette année",
      visits: "Visites",
      clicks: "Clics",
      totalVisits: "Total des visites du site",
      totalClicks: "Total des clics sur les liens d'affiliation",
      clickToVisitRatio: "Ratio clics/visites",
      totalRevenue: "Revenus totaux",
      comparedToPrevious: "Par rapport à la période précédente",
      performanceOverview: "Aperçu des performances",
      mostVisitedProducts: "Produits les plus visités",
      latestActivities: "Dernières activités",
      viewAll: "Voir tout",
      estimatedRevenue: "Revenus estimés",
      conversionRate: "Taux de conversion",
      revenue: "Revenus",
      bags: "Sacs",
      electronics: "Électronique",
      sunday: "Dimanche",
      monday: "Lundi",
      tuesday: "Mardi",
      wednesday: "Mercredi",
      thursday: "Jeudi",
      friday: "Vendredi",
      saturday: "Samedi"
    },
    es: {
      title: "Panel de Control",
      welcome: "Bienvenido al Panel de Administración",
      today: "Hoy",
      thisWeek: "Esta Semana",
      thisMonth: "Este Mes",
      thisYear: "Este Año",
      visits: "Visitas",
      clicks: "Clics",
      totalVisits: "Total de visitas al sitio",
      totalClicks: "Total de clics en enlaces de afiliados",
      clickToVisitRatio: "Ratio de clics a visitas",
      totalRevenue: "Ingresos totales",
      comparedToPrevious: "Comparado con el período anterior",
      performanceOverview: "Resumen de Rendimiento",
      mostVisitedProducts: "Productos Más Visitados",
      latestActivities: "Últimas Actividades",
      viewAll: "Ver Todo",
      estimatedRevenue: "Ingresos Estimados",
      conversionRate: "Tasa de Conversión",
      revenue: "Ingresos",
      bags: "Bolsos",
      electronics: "Electrónicos",
      sunday: "Domingo",
      monday: "Lunes",
      tuesday: "Martes",
      wednesday: "Miércoles",
      thursday: "Jueves",
      friday: "Viernes",
      saturday: "Sábado"
    },
    uk: {
      title: "Панель Управління",
      welcome: "Ласкаво просимо до панелі адміністратора",
      today: "Сьогодні",
      thisWeek: "Цей тиждень",
      thisMonth: "Цей місяць",
      thisYear: "Цей рік",
      visits: "Відвідування",
      clicks: "Кліки",
      totalVisits: "Загальна кількість відвідувань сайту",
      totalClicks: "Загальна кількість кліків по партнерських посилання��",
      clickToVisitRatio: "Співвідношення кліків до відвідувань",
      totalRevenue: "Загальний дохід",
      comparedToPrevious: "Порівняно з попереднім періодом",
      performanceOverview: "Огляд продуктивності",
      mostVisitedProducts: "Найбільш відвідувані продукти",
      latestActivities: "Останні активності",
      viewAll: "Переглянути все",
      estimatedRevenue: "Очікуваний дохід",
      conversionRate: "Коефіцієнт конверсії",
      revenue: "Дохід",
      bags: "Сумки",
      electronics: "Електроніка",
      sunday: "Неділя",
      monday: "Понеділок",
      tuesday: "Вівторок",
      wednesday: "Середа",
      thursday: "Четвер",
      friday: "П'ятниця",
      saturday: "Субота"
    }
  };

  const currentTexts = texts[language] || texts.ar;

  // تحديد ما إذا كنا في بيئة المتصفح
  useEffect(() => {
    setIsClient(true);
  }, []);

  // التحقق من حالة تسجيل الدخول كمسؤول
  useEffect(() => {
    if (isClient) {
      const isAdminAuthenticated = localStorage.getItem("admin_authenticated");
      if (!isAdminAuthenticated || isAdminAuthenticated !== "true") {
        // إذا لم يكن المستخدم مسجل دخول كمسؤول، قم بتوجيهه إلى صفحة تسجيل الدخول
        router.push("/admin-panel-95/admin-login");
      }
    }
  }, [router, isClient]);
  const [stats, setStats] = useState({
    visits: 0,
    clicks: 0,
    conversionRate: 0,
    revenue: 0,
  });

  const [isLoading, setIsLoading] = useState(true);

  // جلب البيانات الفعلية من API
  useEffect(() => {
    const fetchStats = async () => {
      try {
        // في المستقبل، سيتم جلب البيانات من API حقيقي
        // const response = await fetch('/api/admin/stats');
        // const data = await response.json();

        // حالياً نعرض بيانات فارغة حتى يتم ربط النظام بقاعدة البيانات
        setStats({
          visits: 0,
          clicks: 0,
          conversionRate: 0,
          revenue: 0,
        });
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching stats:", error);
        setStats({
          visits: 0,
          clicks: 0,
          conversionRate: 0,
          revenue: 0,
        });
        setIsLoading(false);
      }
    };

    fetchStats();
  }, []);



  return (
    <div dir={direction}>
      <div className="dashboard-header">
        <h1 className="page-title">{currentTexts.title}</h1>
        <div className="date-filter">
          <select defaultValue="today" className="date-select">
            <option value="today">{currentTexts.today}</option>
            <option value="week">{currentTexts.thisWeek}</option>
            <option value="month">{currentTexts.thisMonth}</option>
            <option value="year">{currentTexts.thisYear}</option>
          </select>
        </div>
      </div>

      <div className="stats-grid">
        <div className="stat-card">
          {isLoading ? (
            <div className="stat-loading"></div>
          ) : (
            <>
              <h3 className="stat-label">{currentTexts.visits}</h3>
              <div className="stat-value">{stats.visits.toLocaleString()}</div>
              <p className="stat-description">
                {currentTexts.totalVisits}
              </p>
              <div className="stat-trend">
                <span className="trend-icon">-</span>
                <span className="trend-value">0%</span>
                <span className="trend-period">
                  {currentTexts.comparedToPrevious}
                </span>
              </div>
            </>
          )}
        </div>

        <div className="stat-card">
          {isLoading ? (
            <div className="stat-loading"></div>
          ) : (
            <>
              <h3 className="stat-label">{currentTexts.clicks}</h3>
              <div className="stat-value">{stats.clicks.toLocaleString()}</div>
              <p className="stat-description">
                {currentTexts.totalClicks}
              </p>
              <div className="stat-trend">
                <span className="trend-icon">-</span>
                <span className="trend-value">0%</span>
                <span className="trend-period">
                  {currentTexts.comparedToPrevious}
                </span>
              </div>
            </>
          )}
        </div>

        <div className="stat-card">
          {isLoading ? (
            <div className="stat-loading"></div>
          ) : (
            <>
              <h3 className="stat-label">
                {currentTexts.conversionRate}
              </h3>
              <div className="stat-value">{stats.conversionRate}%</div>
              <p className="stat-description">
                {currentTexts.clickToVisitRatio}
              </p>
              <div className="stat-trend">
                <span className="trend-icon">-</span>
                <span className="trend-value">0%</span>
                <span className="trend-period">
                  {currentTexts.comparedToPrevious}
                </span>
              </div>
            </>
          )}
        </div>

        <div className="stat-card">
          {isLoading ? (
            <div className="stat-loading"></div>
          ) : (
            <>
              <h3 className="stat-label">{currentTexts.revenue}</h3>
              <div className="stat-value">
                ${stats.revenue.toLocaleString()}
              </div>
              <p className="stat-description">
                {currentTexts.totalRevenue}
              </p>
              <div className="stat-trend">
                <span className="trend-icon">-</span>
                <span className="trend-value">0%</span>
                <span className="trend-period">
                  {currentTexts.comparedToPrevious}
                </span>
              </div>
            </>
          )}
        </div>
      </div>

      <div className="dashboard-row">
        <div className="card chart-card">
          <div className="card-header">
            <h2 className="card-title">
              {currentTexts.performanceOverview}
            </h2>
            <div className="card-actions">
              <button
                className="card-action-btn active"
                onClick={(event) => {
                  const buttons = document.querySelectorAll(".card-action-btn");
                  buttons.forEach((btn) => btn.classList.remove("active"));
                  event.currentTarget.classList.add("active");

                  // Cambiar la visualización del gráfico a visitas
                  const chartBars = document.querySelectorAll(".chart-bar");
                  if (chartBars.length > 0) {
                    // Animación simple para mostrar el cambio
                    chartBars.forEach((bar, index) => {
                      setTimeout(() => {
                        bar.style.height = `${60 + Math.floor(Math.random() * 30)}%`;
                      }, index * 50);
                    });
                  }
                }}
              >
                {currentTexts.visits}
              </button>
              <button
                className="card-action-btn"
                onClick={(event) => {
                  const buttons = document.querySelectorAll(".card-action-btn");
                  buttons.forEach((btn) => btn.classList.remove("active"));
                  event.currentTarget.classList.add("active");

                  // Cambiar la visualización del gráfico a clics
                  const chartBars = document.querySelectorAll(".chart-bar");
                  if (chartBars.length > 0) {
                    // Animación simple para mostrar el cambio
                    chartBars.forEach((bar, index) => {
                      setTimeout(() => {
                        bar.style.height = `${40 + Math.floor(Math.random() * 40)}%`;
                      }, index * 50);
                    });
                  }
                }}
              >
                {currentTexts.clicks}
              </button>
              <button
                className="card-action-btn"
                onClick={(event) => {
                  const buttons = document.querySelectorAll(".card-action-btn");
                  buttons.forEach((btn) => btn.classList.remove("active"));
                  event.currentTarget.classList.add("active");

                  // Cambiar la visualización del gráfico a ingresos
                  const chartBars = document.querySelectorAll(".chart-bar");
                  if (chartBars.length > 0) {
                    // Animación simple para mostrar el cambio
                    chartBars.forEach((bar, index) => {
                      setTimeout(() => {
                        bar.style.height = `${30 + Math.floor(Math.random() * 60)}%`;
                      }, index * 50);
                    });
                  }
                }}
              >
                {currentTexts.estimatedRevenue}
              </button>
            </div>
          </div>
          <div className="card-content">
            {isLoading ? (
              <div className="chart-loading"></div>
            ) : (
              <div className="chart-placeholder">
                <div className="chart-bars">
                  <div
                    className="chart-bar"
                    style={{ height: "60%", transition: "height 0.3s ease" }}
                  ></div>
                  <div
                    className="chart-bar"
                    style={{ height: "80%", transition: "height 0.3s ease" }}
                  ></div>
                  <div
                    className="chart-bar"
                    style={{ height: "40%", transition: "height 0.3s ease" }}
                  ></div>
                  <div
                    className="chart-bar"
                    style={{ height: "70%", transition: "height 0.3s ease" }}
                  ></div>
                  <div
                    className="chart-bar"
                    style={{ height: "90%", transition: "height 0.3s ease" }}
                  ></div>
                  <div
                    className="chart-bar"
                    style={{ height: "50%", transition: "height 0.3s ease" }}
                  ></div>
                  <div
                    className="chart-bar"
                    style={{ height: "75%", transition: "height 0.3s ease" }}
                  ></div>
                </div>
                <div className="chart-labels">
                  <span>{currentTexts.sunday}</span>
                  <span>{currentTexts.monday}</span>
                  <span>{currentTexts.tuesday}</span>
                  <span>{currentTexts.wednesday}</span>
                  <span>{currentTexts.thursday}</span>
                  <span>{currentTexts.friday}</span>
                  <span>{currentTexts.saturday}</span>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h2 className="card-title">
              {currentTexts.mostVisitedProducts}
            </h2>
            <button className="view-all-btn">
              {currentTexts.viewAll}
            </button>
          </div>
          <div className="card-content">
            {isLoading ? (
              <div className="list-loading"></div>
            ) : (
              <div className="product-list">
                <div className="product-item">
                  <div className="product-rank">1</div>
                  <div className="product-info">
                    <div className="product-name">سماعات بلوتوث لاسلكية</div>
                    <div className="product-category">
                      {currentTexts.electronics}
                    </div>
                  </div>
                  <div className="product-stats">
                    <div className="product-visits">
                      245 {currentTexts.visits}
                    </div>
                  </div>
                </div>
                <div className="product-item">
                  <div className="product-rank">2</div>
                  <div className="product-info">
                    <div className="product-name">ساعة ذكية متعددة الوظائف</div>
                    <div className="product-category">
                      {currentTexts.electronics}
                    </div>
                  </div>
                  <div className="product-stats">
                    <div className="product-visits">
                      198 {currentTexts.visits}
                    </div>
                  </div>
                </div>
                <div className="product-item">
                  <div className="product-rank">3</div>
                  <div className="product-info">
                    <div className="product-name">
                      حقيبة ظهر للكمبيوتر المحمول
                    </div>
                    <div className="product-category">
                      {currentTexts.bags}
                    </div>
                  </div>
                  <div className="product-stats">
                    <div className="product-visits">
                      156 {currentTexts.visits}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h2 className="card-title">
            {currentTexts.latestActivities}
          </h2>
          <button className="view-all-btn">
            {currentTexts.viewAll}
          </button>
        </div>
        <div className="card-content">
          {isLoading ? (
            <div className="list-loading"></div>
          ) : (
            <div className="activity-list">
              <div className="activity-item">
                <div className="activity-icon visit-icon">👁️</div>
                <div className="activity-info">
                  <div className="activity-title">زيارة جديدة</div>
                  <div className="activity-details">
                    تمت زيارة "سماعات بلوتوث لاسلكية"
                  </div>
                </div>
                <div className="activity-time">منذ 5 دقائق</div>
              </div>
              <div className="activity-item">
                <div className="activity-icon click-icon">👆</div>
                <div className="activity-info">
                  <div className="activity-title">نقرة على رابط</div>
                  <div className="activity-details">
                    تم النقر على رابط "ساعة ذكية متعددة الوظائف"
                  </div>
                </div>
                <div className="activity-time">منذ 12 دقيقة</div>
              </div>
              <div className="activity-item">
                <div className="activity-icon visit-icon">👁️</div>
                <div className="activity-info">
                  <div className="activity-title">زيارة جديدة</div>
                  <div className="activity-details">
                    تمت زيارة "حقيبة ظهر للكمبيوتر المحمول"
                  </div>
                </div>
                <div className="activity-time">منذ 25 دقيقة</div>
              </div>
              <div className="activity-item">
                <div className="activity-icon click-icon">👆</div>
                <div className="activity-info">
                  <div className="activity-title">نقرة على رابط</div>
                  <div className="activity-details">
                    تم النقر على رابط "سماعات بلوتوث لاسلكية"
                  </div>
                </div>
                <div className="activity-time">منذ 38 دقيقة</div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
