"use client";

import "./admin-styles.css";
import "./admin-footer.css";
import Sidebar from "./components/Sidebar";
import AdminFooter from "./components/AdminFooter";
import { useTranslation } from "../components/TranslationProvider";

export default function AdminLayout({ children }) {
  const { t, language, direction } = useTranslation();

  return (
    <div className="admin-layout">
      <Sidebar />
      <main className="main-content">
        <header className="admin-header">
          <div className="header-left"></div>
          <div className="header-right">
            <div className="search-bar">
              <input
                type="search"
                placeholder={t("common.labels.search") + "..."}
              />
              <button type="submit" className="search-button">
                <span>🔍</span>
              </button>
            </div>
            <div className="header-actions">
              <button className="notification-button">
                <span>🔔</span>
              </button>
            </div>
          </div>
        </header>
        <div className="content-area">{children}</div>
        <AdminFooter />
      </main>
    </div>
  );
}
