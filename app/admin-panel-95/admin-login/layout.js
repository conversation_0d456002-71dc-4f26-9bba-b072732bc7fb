'use client';

import { TranslationProvider } from '../../components/TranslationProvider'
import Header from '../components/Header';
import AdminFooter from '../components/AdminFooter';
import '../admin-styles.css';
import '../admin-footer.css';

export default function AdminLoginLayout({ children }) {
  return (
    <TranslationProvider>
      <div className="admin-login-layout">
        <Header />
        {children}
        <AdminFooter />
      </div>
    </TranslationProvider>
  )
}