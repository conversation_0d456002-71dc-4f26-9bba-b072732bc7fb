"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState, useEffect } from "react";
import { useTranslation } from "../../components/TranslationProvider";

export default function Sidebar() {
  const pathname = usePathname();
  const { language, direction } = useTranslation();

  // النصوص حسب اللغة
  const texts = {
    ar: {
      dashboard: "لوحة التحكم",
      welcome: "مرحباً بك في لوحة التحكم",
      settings: "الإعدادات",
      analytics: "الإحصائيات",
      users: "المستخدمين",
      assistants: "المساعدين",
      profile: "الملف الشخصي",
      home: "الرئيسية",
      logout: "تسجيل الخروج",
      version: "الإصدار"
    },
    en: {
      dashboard: "Dashboard",
      welcome: "Welcome to Control Panel",
      settings: "Settings",
      analytics: "Analytics",
      users: "Users",
      assistants: "Assistants",
      profile: "Profile",
      home: "Home",
      logout: "Logout",
      version: "Version"
    },
    fr: {
      dashboard: "Tableau de Bord",
      welcome: "Bienvenue dans le Panneau de Contrôle",
      settings: "Paramètres",
      analytics: "Analytiques",
      users: "Utilisateurs",
      assistants: "Assistants",
      profile: "Profil",
      home: "Accueil",
      logout: "Déconnexion",
      version: "Version"
    },
    es: {
      dashboard: "Panel de Control",
      welcome: "Bienvenido al Panel de Control",
      settings: "Configuración",
      analytics: "Analíticas",
      users: "Usuarios",
      assistants: "Asistentes",
      profile: "Perfil",
      home: "Inicio",
      logout: "Cerrar Sesión",
      version: "Versión"
    },
    uk: {
      dashboard: "Панель Керування",
      welcome: "Ласкаво просимо до Панелі Керування",
      settings: "Налаштування",
      analytics: "Аналітика",
      users: "Користувачі",
      assistants: "Помічники",
      profile: "Профіль",
      home: "Головна",
      logout: "Вийти",
      version: "Версія"
    }
  };

  const currentTexts = texts[language] || texts.ar;

  const isActive = (path) => {
    return pathname === path ? "active-link" : "";
  };

  const handleLogout = () => {
    const confirmMessage =
      language === "ar"
        ? "هل أنت متأكد من أنك تريد تسجيل الخروج؟"
        : language === "en"
          ? "Are you sure you want to logout?"
          : language === "fr"
            ? "Êtes-vous sûr de vouloir vous déconnecter?"
            : language === "es"
              ? "¿Estás seguro de que quieres cerrar sesión?"
              : language === "uk"
                ? "Ви впевнені, що хочете вийти?"
                : "هل أنت متأكد من أنك تريد تسجيل الخروج؟";

    if (confirm(confirmMessage)) {
      // إزالة بيانات المصادقة من localStorage
      localStorage.removeItem('admin_authenticated');
      localStorage.removeItem('admin_email');
      
      // توجيه المستخدم إلى صفحة تسجيل الدخول
      window.location.href = "/admin-panel-95/admin-login";
    }
  };

  return (
    <aside className="sidebar">
      <div className="sidebar-header">
        <div className="logo">
          <h1>{t("adminPanel.sidebar.dashboard")}</h1>
        </div>
        <div className="admin-info">
          <div className="admin-avatar">
            <span>م</span>
          </div>
          <div className="admin-name">{t("admin.dashboard.welcome")}</div>
        </div>
      </div>
      <nav className="sidebar-nav">
        <ul>
          <li>
            <Link
              href="/admin-panel-95/dashboard"
              className={isActive("/admin-panel-95/dashboard")}
            >
              <span className="nav-icon">📊</span>
              <span className="nav-text">{t("adminPanel.sidebar.dashboard")}</span>
            </Link>
          </li>
          <li>
            <Link
              href="/admin-panel-95/settings"
              className={isActive("/admin-panel-95/settings")}
            >
              <span className="nav-icon">⚙️</span>
              <span className="nav-text">{t("adminPanel.sidebar.settings")}</span>
            </Link>
          </li>
          <li>
            <Link
              href="/admin-panel-95/analytics"
              className={isActive("/admin-panel-95/analytics")}
            >
              <span className="nav-icon">📈</span>
              <span className="nav-text">{t("adminPanel.sidebar.analytics")}</span>
            </Link>
          </li>
          <li>
            <Link
              href="/admin-panel-95/users"
              className={isActive("/admin-panel-95/users")}
            >
              <span className="nav-icon">👥</span>
              <span className="nav-text">{t("adminPanel.sidebar.users")}</span>
            </Link>
          </li>
          <li>
            <Link
              href="/admin-panel-95/assistants"
              className={isActive("/admin-panel-95/assistants")}
            >
              <span className="nav-icon">👨‍💼</span>
              <span className="nav-text">
                {t("adminPanel.sidebar.assistants")}
              </span>
            </Link>
          </li>
          <li>
            <Link
              href="/admin-panel-95/profile"
              className={isActive("/admin-panel-95/profile")}
            >
              <span className="nav-icon">👤</span>
              <span className="nav-text">{t("adminPanel.sidebar.profile")}</span>
            </Link>
          </li>
          <li>
            <Link href="/" className="">
              <span className="nav-icon">🏠</span>
              <span className="nav-text">{t("adminPanel.sidebar.home")}</span>
            </Link>
          </li>
          <li>
            <button
              onClick={handleLogout}
              className="logout-button"
              style={{
                background: "none",
                border: "none",
                color: "inherit",
                width: "100%",
                textAlign: direction === "rtl" ? "right" : "left",
                padding: "15px 20px",
                cursor: "pointer",
                display: "flex",
                alignItems: "center",
                gap: "10px",
                fontSize: "inherit",
                fontFamily: "inherit",
                flexDirection: direction === "rtl" ? "row-reverse" : "row",
              }}
            >
              <span className="nav-icon">🚪</span>
              <span className="nav-text">{t("adminPanel.sidebar.logout")}</span>
            </button>
          </li>
        </ul>
      </nav>
      <div className="sidebar-footer">
        <div className="version">{t("common.labels.version")} 1.0.0</div>
      </div>
    </aside>
  );
}
