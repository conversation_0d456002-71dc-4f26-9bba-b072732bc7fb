'use client';

import { useTranslation } from '../../components/TranslationProvider';
import LanguageSwitcher from '../../components/LanguageSwitcher';
import CurrencySwitcher from '../../components/CurrencySwitcher';

export default function Header() {
  const { t, direction } = useTranslation();
  
  // أنماط CSS
  const headerStyle = {
    backgroundColor: '#fff',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
    padding: '1rem 0',
    position: 'sticky',
    top: 0,
    zIndex: 1000,
  };
  
  const containerStyle = {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '0 1rem',
  };
  
  const headerContentStyle = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  };
  
  const logoStyle = {
    display: 'flex',
    alignItems: 'center',
    textDecoration: 'none',
    color: '#333',
  };
  
  const logoImageStyle = {
    width: '40px',
    height: '40px',
    marginRight: direction === 'rtl' ? 0 : '0.5rem',
    marginLeft: direction === 'rtl' ? '0.5rem' : 0,
    backgroundColor: '#f0f0f0',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '1.5rem',
  };
  
  const logoTextStyle = {
    fontSize: '1.2rem',
    fontWeight: 'bold',
  };
  
  const menuContainerStyle = {
    display: 'flex',
    alignItems: 'center',
  };
  
  const langMenuStyle = {
    position: 'relative',
    marginRight: direction === 'rtl' ? 0 : '1rem',
    marginLeft: direction === 'rtl' ? '1rem' : 0,
  };
  
  return (
    <header style={headerStyle}>
      <div style={containerStyle}>
        <div style={headerContentStyle}>
          {/* الشعار */}
          <div style={logoStyle}>
            <a href="/" style={logoStyle}>
              <div style={logoImageStyle}>
                <span>🛒</span>
              </div>
              <div style={logoTextStyle}>
                {t('common.siteTitle')}
              </div>
            </a>
          </div>
          
          {/* قائمة اللغات والعملات */}
          <div style={menuContainerStyle}>
            <div style={langMenuStyle}>
              <LanguageSwitcher />
            </div>
            <div style={langMenuStyle}>
              <CurrencySwitcher />
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}