:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --secondary-light: #34495e;
    --background-color: #f5f7fa;
    --text-color: #333;
    --text-light: #666;
    --text-lighter: #999;
    --sidebar-width: 280px;
    --header-height: 70px;
    --border-radius: 8px;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;

    /* ألوان إضافية للإحصائيات والرسوم البيانية */
    --success-color: #2ecc71;
    --success-dark: #27ae60;
    --warning-color: #f39c12;
    --warning-dark: #e67e22;
    --danger-color: #e74c3c;
    --danger-dark: #c0392b;
    --info-color: #3498db;
    --info-dark: #2980b9;
}

* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

html,
body {
    max-width: 100vw;
    overflow-x: hidden;
    font-family:
        -apple-system,
        BlinkMacSystemFont,
        Segoe UI,
        Roboto,
        Oxygen,
        Ubuntu,
        Cantarell,
        Fira Sans,
        Droid Sans,
        Helvetica Neue,
        sans-serif;
}

body {
    color: var(--text-color);
    background: var(--background-color);
}

a {
    color: inherit;
    text-decoration: none;
}

button {
    cursor: pointer;
    border: none;
    background: none;
}

/* ===== Layout Structure ===== */
.admin-layout {
    display: flex;
    min-height: 100vh;
    position: relative;
    flex-direction: row-reverse; /* تغيير اتجاه العناصر ليناسب RTL */
    width: 100%;
    overflow-x: hidden;
}

/* دعم اللغات LTR */
html[dir="ltr"] .admin-layout {
    flex-direction: row;
}

/* ===== Sidebar ===== */
.sidebar {
    width: var(--sidebar-width);
    min-width: var(--sidebar-width);
    background: linear-gradient(
        to bottom,
        var(--secondary-color),
        var(--secondary-light)
    );
    color: white;
    height: 100vh;
    overflow-y: auto;
    z-index: 100;
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 0;
}

/* RTL positioning */
html[dir="rtl"] .sidebar {
    right: 0;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
}

/* LTR positioning */
html[dir="ltr"] .sidebar {
    left: 0;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

/* دعم اللغات LTR للشريط الجانبي */

.sidebar-header {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
}

html[dir="rtl"] .sidebar-header {
    text-align: center;
}

html[dir="ltr"] .sidebar-header {
    text-align: center;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    text-align: center;
    letter-spacing: 0.5px;
}

.admin-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
}

.admin-avatar {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.25rem;
}

.admin-name {
    font-size: 0.9rem;
    font-weight: 500;
}

.sidebar-nav {
    padding: 0 1rem 1rem;
    flex: 1;
}

.sidebar-nav ul {
    list-style: none;
}

.sidebar-nav li {
    margin-bottom: 0.5rem;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.25rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    text-decoration: none;
    color: inherit;
}

/* RTL navigation alignment */
html[dir="rtl"] .sidebar-nav a {
    flex-direction: row-reverse;
    text-align: right;
}

/* LTR navigation alignment */
html[dir="ltr"] .sidebar-nav a {
    flex-direction: row;
    text-align: left;
}

.sidebar-nav a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-nav a.active-link {
    background-color: var(--primary-color);
}

.nav-icon {
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
}

.nav-text {
    font-weight: 500;
}

.sidebar-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
}

/* ===== Main Content ===== */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: calc(100% - var(--sidebar-width));
    position: relative;
}

/* RTL main content positioning */
html[dir="rtl"] .main-content {
    margin-right: var(--sidebar-width);
    margin-left: 0;
}

/* LTR main content positioning */
html[dir="ltr"] .main-content {
    margin-left: var(--sidebar-width);
    margin-right: 0;
}

/* ===== Header ===== */
.admin-header {
    height: var(--header-height);
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    position: sticky;
    top: 0;
    z-index: 10;
    width: 100%;
}

.header-left {
    display: flex;
    align-items: center;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--secondary-color);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.search-bar {
    display: flex;
    align-items: center;
    background-color: var(--background-color);
    border-radius: 30px;
    padding: 0.25rem 0.25rem 0.25rem 1rem;
    width: 300px;
    transition: var(--transition);
}

.search-bar:focus-within {
    box-shadow: 0 0 0 2px var(--primary-color);
}

.search-bar input {
    background: transparent;
    border: none;
    outline: none;
    padding: 0.5rem 0;
    width: 100%;
    font-size: 0.9rem;
}

.search-button {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.search-button:hover {
    background-color: var(--primary-dark);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.notification-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--background-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: var(--transition);
}

.notification-button:hover {
    background-color: #e0e0e0;
}

/* ===== Content Area ===== */
.content-area {
    padding: 2rem;
    flex: 1;
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    box-sizing: border-box;
}

/* ===== Cards ===== */
.card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    box-shadow:
        0 6px 12px rgba(0, 0, 0, 0.08),
        0 2px 4px rgba(0, 0, 0, 0.12);
}

.card.card-primary {
    border-top: 4px solid var(--primary-color);
}

.card.card-success {
    border-top: 4px solid var(--success-color);
}

.card.card-warning {
    border-top: 4px solid var(--warning-color);
}

.card.card-danger {
    border-top: 4px solid var(--danger-color);
}

.card-header {
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--secondary-color);
}

/* ===== Settings Tabs ===== */
.settings-tabs,
.analytics-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 1rem;
    flex-wrap: wrap;
}

.settings-tab,
.analytics-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-color);
    background-color: transparent;
    transition: var(--transition);
    border: 1px solid transparent;
}

.settings-tab:hover,
.analytics-tab:hover {
    background-color: var(--background-color);
}

.settings-tab.active,
.analytics-tab.active {
    background-color: var(--primary-color);
    color: white;
}

.tab-icon {
    font-size: 1.25rem;
}

/* ===== Forms ===== */
.form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.875rem 1rem;
    padding-right: 2.5rem;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    outline: none;
}

.input-icon {
    position: absolute;
    right: 1rem;
    color: var(--text-lighter);
    font-size: 1.25rem;
    pointer-events: none;
}

.form-hint {
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-lighter);
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.form-loading {
    height: 300px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: var(--border-radius);
}

.save-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
}

.save-message.success {
    background-color: rgba(46, 204, 113, 0.1);
    color: #27ae60;
}

.message-icon {
    font-weight: bold;
}

.spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.api-test {
    padding: 1rem 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
}

.settings-placeholder {
    padding: 2rem;
    text-align: center;
    color: var(--text-lighter);
    background-color: var(--background-color);
    border-radius: var(--border-radius);
}

.help-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    font-size: 0.875rem;
    transition: var(--transition);
}

.help-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* ===== Buttons ===== */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--background-color);
    color: var(--text-color);
    border: 1px solid #e0e0e0;
}

.btn-secondary:hover {
    background-color: #e0e0e0;
}

.btn-success {
    background-color: var(--success-color);
    color: white;
    border: none;
}

.btn-success:hover {
    background-color: var(--success-dark);
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
    border: none;
}

.btn-warning:hover {
    background-color: var(--warning-dark);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
    border: none;
}

.btn-danger:hover {
    background-color: var(--danger-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid currentColor;
}

.btn-outline.btn-primary {
    color: var(--primary-color);
}

.btn-outline.btn-primary:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

.btn-outline.btn-success {
    color: var(--success-color);
}

.btn-outline.btn-success:hover {
    background-color: rgba(46, 204, 113, 0.1);
}

.btn-outline.btn-warning {
    color: var(--warning-color);
}

.btn-outline.btn-warning:hover {
    background-color: rgba(243, 156, 18, 0.1);
}

.btn-outline.btn-danger {
    color: var(--danger-color);
}

.btn-outline.btn-danger:hover {
    background-color: rgba(231, 76, 60, 0.1);
}

.btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* ===== Dashboard Header ===== */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.date-filter {
    position: relative;
}

.date-select {
    padding: 0.5rem 1rem;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    background-color: white;
    font-size: 0.9rem;
    cursor: pointer;
    min-width: 150px;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.7rem center;
    background-size: 1em;
}

/* ===== Dashboard Row ===== */
.dashboard-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

@media (max-width: 1024px) {
    .dashboard-row {
        grid-template-columns: 1fr;
    }
}

/* ===== Stats Grid ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.stat-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background-color: var(--primary-color);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0.5rem 0;
}

.stat-label {
    color: var(--text-light);
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-description {
    color: var(--text-lighter);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.stat-trend {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    margin-top: auto;
}

.stat-trend.positive {
    color: #27ae60;
}

.stat-trend.negative {
    color: #e74c3c;
}

.trend-icon {
    font-weight: bold;
}

.trend-value {
    font-weight: 600;
}

.trend-period {
    color: var(--text-lighter);
    font-size: 0.75rem;
}

/* ===== Loading States ===== */
.stat-loading,
.chart-loading,
.list-loading {
    height: 100%;
    min-height: 100px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: var(--border-radius);
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* ===== Chart Placeholder ===== */
.chart-placeholder {
    height: 300px;
    display: flex;
    flex-direction: column;
    padding: 1rem 0;
}

.chart-bars {
    display: flex;
    align-items: flex-end;
    justify-content: space-around;
    height: 250px;
    padding: 0 1rem;
}

.chart-bar {
    width: 40px;
    background-color: var(--primary-color);
    border-radius: 4px 4px 0 0;
    transition: var(--transition);
}

.chart-bar:hover {
    background-color: var(--primary-dark);
}

.chart-labels {
    display: flex;
    justify-content: space-around;
    padding: 1rem 0 0;
    color: var(--text-light);
    font-size: 0.75rem;
}

/* ===== Card Actions ===== */
.card-actions {
    display: flex;
    gap: 0.5rem;
}

.card-action-btn {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    background-color: var(--background-color);
    color: var(--text-color);
    transition: var(--transition);
}

.card-action-btn:hover {
    background-color: #e0e0e0;
}

.card-action-btn.active {
    background-color: var(--primary-color);
    color: white;
}

.view-all-btn {
    color: var(--primary-color);
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition);
}

.view-all-btn:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* ===== Product List ===== */
.product-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.product-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.product-item:hover {
    background-color: var(--background-color);
}

.product-rank {
    width: 30px;
    height: 30px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 1rem;
}

.product-info {
    flex: 1;
}

.product-name {
    font-weight: 500;
}

.product-category {
    font-size: 0.75rem;
    color: var(--text-lighter);
}

.product-stats {
    text-align: right;
}

.product-visits {
    font-weight: 500;
    color: var(--primary-color);
}

/* ===== Activity List ===== */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.activity-item:hover {
    background-color: var(--background-color);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.25rem;
}

.visit-icon {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
}

.click-icon {
    background-color: rgba(46, 204, 113, 0.1);
    color: #27ae60;
}

.activity-info {
    flex: 1;
}

.activity-title {
    font-weight: 500;
}

.activity-details {
    font-size: 0.875rem;
    color: var(--text-light);
}

.activity-time {
    font-size: 0.75rem;
    color: var(--text-lighter);
}

/* ===== Analytics Table ===== */
.analytics-table {
    width: 100%;
    border-collapse: collapse;
}

.table-header {
    display: flex;
    background-color: var(--background-color);
    font-weight: 600;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.table-row {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    transition: var(--transition);
}

.table-row:hover {
    background-color: var(--background-color);
}

.table-row:last-child {
    border-bottom: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.table-cell {
    padding: 1rem;
    flex: 1;
    display: flex;
    align-items: center;
}

.table-cell:first-child {
    flex: 0 0 50px;
    justify-content: center;
    font-weight: 600;
}

.product-name {
    font-weight: 500;
    color: var(--primary-color);
}

/* ===== Traffic Sources ===== */
.traffic-sources {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    padding: 1rem 0;
}

.traffic-chart-placeholder {
    flex: 1;
    min-width: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.donut-chart {
    position: relative;
    width: 250px;
    height: 250px;
    border-radius: 50%;
    background-color: #f5f5f5;
}

.donut-segment {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
}

.donut-hole {
    position: absolute;
    width: 60%;
    height: 60%;
    background-color: white;
    border-radius: 50%;
    top: 20%;
    left: 20%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: var(--text-color);
}

.traffic-legend {
    flex: 1;
    min-width: 250px;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    justify-content: center;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 4px;
}

.legend-text {
    display: flex;
    flex-direction: column;
}

.legend-label {
    font-weight: 500;
}

.legend-value {
    color: var(--text-light);
    font-size: 0.875rem;
}

.reports-placeholder {
    padding: 3rem;
    text-align: center;
    color: var(--text-light);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

/* تنسيقات صفحة تسجيل دخول الإدارة */
.admin-login-layout {
    width: 100%;
    min-height: 100vh;
    background: white;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: flex;
    flex-direction: column;
}

.admin-login-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

.admin-login-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 25px;
    width: 100%;
    max-width: 320px;
    margin-top: -60px; /* لتعويض المسافة التي يشغلها الهيدر */
}

.admin-login-header {
    text-align: center;
    margin-bottom: 15px;
}

.admin-login-logo {
    font-size: 32px;
    margin-bottom: 10px;
}

.admin-login-title {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 6px;
}

.admin-login-subtitle {
    color: #666;
    font-size: 14px;
}

.admin-login-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.admin-login-form .form-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.admin-login-form .form-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    outline: none;
}

.admin-login-form .form-input.error {
    border-color: #e74c3c;
}

.admin-login-form .error-message {
    color: #e74c3c;
    font-size: 12px;
    margin-top: 5px;
}

.admin-login-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 5px;
}

.admin-login-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.admin-login-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.admin-login-footer {
    text-align: center;
    margin-top: 20px;
}

.password-input-wrapper {
    position: relative;
}

.password-toggle-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    z-index: 10;
}

.password-toggle-icon:hover {
    color: #333;
}

.back-to-site-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.back-to-site-link:hover {
    text-decoration: underline;
}

.test-credentials {
    margin-top: 20px;
    padding: 15px;
    background-color: #f0f8ff;
    border-radius: 8px;
    font-size: 14px;
    color: #666;
    border: 1px solid #e0e7ff;
}

.test-credentials strong {
    color: #333;
    display: block;
    margin-bottom: 8px;
}

.form-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input.error {
    border-color: #ef4444;
    background-color: #fef2f2;
}

.error-message {
    color: #ef4444;
    font-size: 12px;
    margin-top: 4px;
    display: block;
}

.error-message.general-error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 16px;
    text-align: center;
}

/* تنسيقات صفحة إدارة المساعدين */
.assistants-management {
    padding: 20px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.page-title {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
}

.assistants-controls {
    margin-bottom: 20px;
}

.search-box {
    max-width: 400px;
}

.search-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.assistants-table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.assistants-table {
    width: 100%;
    border-collapse: collapse;
}

.assistants-table th {
    background: #f9fafb;
    padding: 16px;
    text-align: right;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
}

.assistants-table td {
    padding: 16px;
    border-bottom: 1px solid #f3f4f6;
    color: #6b7280;
}

.assistants-table tbody tr:hover {
    background: #f9fafb;
}

.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.active {
    background: #d1fae5;
    color: #065f46;
}

.status-badge.inactive {
    background: #fee2e2;
    color: #991b1b;
}

.status-badge.suspended {
    background: #fef3c7;
    color: #92400e;
}

.permissions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.permission-tag {
    background: #e0e7ff;
    color: #3730a3;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
}

.btn-secondary {
    background: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.no-data {
    text-align: center;
    padding: 40px;
    color: #6b7280;
    font-size: 16px;
}

/* تنسيقات النوافذ المنبثقة */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
}

.modal {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6b7280;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.modal-close:hover {
    background: #f3f4f6;
}

.modal-form {
    padding: 24px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input.error {
    border-color: #ef4444;
}

.error-message {
    color: #ef4444;
    font-size: 12px;
    margin-top: 4px;
    display: block;
}

.error-message.general-error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 16px;
}

.permissions-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 8px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #374151;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #667eea;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
}

/* تحسينات للاتجاه من اليمين لليسار */
[dir="rtl"] .assistants-table th,
[dir="rtl"] .assistants-table td {
    text-align: right;
}

[dir="rtl"] .page-header {
    flex-direction: row-reverse;
}

[dir="rtl"] .action-buttons {
    flex-direction: row-reverse;
}

[dir="rtl"] .modal-header {
    flex-direction: row-reverse;
}

[dir="rtl"] .modal-actions {
    flex-direction: row-reverse;
}

[dir="rtl"] .checkbox-label {
    flex-direction: row-reverse;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .admin-login-card {
        padding: 30px 20px;
    }

    .page-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .assistants-table-container {
        overflow-x: auto;
    }

    .assistants-table {
        min-width: 800px;
    }

    .modal {
        margin: 10px;
        max-width: none;
    }

    .action-buttons {
        flex-direction: column;
    }

    .btn-sm {
        padding: 8px 12px;
        font-size: 13px;
    }
}

/* تنسيقات زر الخروج */
.logout-button {
    background-color: var(--danger-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.logout-button:hover {
    background-color: var(--danger-dark);
}

html[dir="ltr"] .logout-button {
    margin-left: 1rem;
}

html[dir="rtl"] .logout-button {
    margin-right: 1rem;
}

.logout-btn {
    background-color: var(--danger-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.logout-btn:hover {
    background-color: var(--danger-dark);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}
