/* تنسيقات صفحة الملف الشخصي */

.admin-profile {
  width: 100%;
}

.page-title {
  margin-bottom: 1.5rem;
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--secondary-color);
}

.success-message {
  background-color: rgba(46, 204, 113, 0.1);
  color: #27ae60;
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.success-message::before {
  content: '✓';
  font-weight: bold;
}

.error-message {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.error-message::before {
  content: '✗';
  font-weight: bold;
}

/* تنسيق رسالة التنبيه */
.warning-message {
  background-color: rgba(241, 196, 15, 0.1);
  color: #f39c12;
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.warning-icon {
  font-size: 1.25rem;
  margin-right: 0.5rem;
}

.warning-link {
  margin-left: auto;
  color: #3498db;
  text-decoration: none;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.warning-link:hover {
  background-color: rgba(52, 152, 219, 0.1);
  text-decoration: underline;
}

.profile-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.profile-section {
  margin-bottom: 1.5rem;
}

.profile-form {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.input-wrapper {
  position: relative;
}

.input-wrapper input {
  width: 100%;
  padding: 0.875rem 1rem;
  padding-right: 2.5rem;
  border: 1px solid #e0e0e0;
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
}

.input-wrapper input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
  outline: none;
}

.input-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-lighter);
  pointer-events: none;
}

.input-disabled {
  background-color: #f9f9f9;
  color: var(--text-light);
  cursor: not-allowed;
}

.input-error {
  border-color: #e74c3c !important;
}

.error-text {
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.form-hint {
  color: var(--text-lighter);
  font-size: 0.75rem;
  margin-top: 0.5rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.password-requirements {
  font-size: 0.75rem;
  color: var(--text-lighter);
  margin-top: 0.5rem;
  line-height: 1.5;
}

.security-code-group {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسينات للتجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
  .profile-container {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions button {
    width: 100%;
  }
}