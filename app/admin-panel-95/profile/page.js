'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslation } from '../../components/TranslationProvider'
import './profile.css'

export default function AdminProfile() {
  const { language } = useTranslation();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  // النصوص حسب اللغة
  const texts = {
    ar: {
      title: "الملف الشخصي",
      personalInfo: "المعلومات الشخصية",
      name: "الاسم",
      email: "البريد الإلكتروني",
      phone: "رقم الهاتف",
      role: "الدور",
      lastLogin: "آخر تسجيل دخول",
      changePassword: "تغيير كلمة المرور",
      currentPassword: "كلمة المرور الحالية",
      newPassword: "كلمة المرور الجديدة",
      confirmNewPassword: "تأكيد كلمة المرور الجديدة",
      securityCode: "رمز الأمان",
      save: "حفظ",
      cancel: "إلغاء",
      edit: "تعديل",
      loading: "جاري التحميل...",
      saving: "جاري الحفظ...",
      saved: "تم الحفظ بنجاح!",
      error: "حدث خطأ أثناء الحفظ",
      enterName: "أدخل الاسم",
      enterEmail: "أدخل البريد الإلكتروني",
      enterPhone: "أدخل رقم الهاتف",
      enterCurrentPassword: "أدخل كلمة المرور الحالية",
      enterNewPassword: "أدخل كلمة المرور الجديدة",
      confirmPassword: "أكد كلمة المرور الجديدة",
      enterSecurityCode: "أدخل رمز الأمان",
      passwordMismatch: "كلمات المرور غير متطابقة",
      weakPassword: "كلمة المرور ضعيفة",
      passwordRequirements: "يجب أن تحتوي كلمة المرور على 8 أحرف على الأقل",
      optional: "اختياري",
      sendSecurityCode: "إرسال رمز الأمان",
      confirmPasswordChange: "تأكيد تغيير كلمة المرور",
      adminRole: "مدير رئيسي",
      adminName: "مدير الموقع"
    },
    en: {
      title: "Profile",
      personalInfo: "Personal Information",
      name: "Name",
      email: "Email",
      phone: "Phone",
      role: "Role",
      lastLogin: "Last Login",
      changePassword: "Change Password",
      currentPassword: "Current Password",
      newPassword: "New Password",
      confirmNewPassword: "Confirm New Password",
      securityCode: "Security Code",
      save: "Save",
      cancel: "Cancel",
      edit: "Edit",
      loading: "Loading...",
      saving: "Saving...",
      saved: "Saved successfully!",
      error: "Error occurred while saving",
      enterName: "Enter Name",
      enterEmail: "Enter Email",
      enterPhone: "Enter Phone",
      enterCurrentPassword: "Enter Current Password",
      enterNewPassword: "Enter New Password",
      confirmPassword: "Confirm New Password",
      enterSecurityCode: "Enter Security Code",
      passwordMismatch: "Passwords do not match",
      weakPassword: "Password is weak",
      passwordRequirements: "Password must contain at least 8 characters",
      optional: "Optional",
      sendSecurityCode: "Send Security Code",
      confirmPasswordChange: "Confirm Password Change",
      adminRole: "Main Administrator",
      adminName: "Site Administrator"
    },
    fr: {
      title: "Profil",
      personalInfo: "Informations Personnelles",
      name: "Nom",
      email: "Email",
      phone: "Téléphone",
      role: "Rôle",
      lastLogin: "Dernière Connexion",
      changePassword: "Changer le Mot de Passe",
      currentPassword: "Mot de Passe Actuel",
      newPassword: "Nouveau Mot de Passe",
      confirmNewPassword: "Confirmer le Nouveau Mot de Passe",
      securityCode: "Code de Sécurité",
      save: "Enregistrer",
      cancel: "Annuler",
      edit: "Modifier",
      loading: "Chargement...",
      saving: "Enregistrement...",
      saved: "Enregistré avec succès!",
      error: "Erreur lors de l'enregistrement",
      enterName: "Entrer le Nom",
      enterEmail: "Entrer l'Email",
      enterPhone: "Entrer le Téléphone",
      enterCurrentPassword: "Entrer le Mot de Passe Actuel",
      enterNewPassword: "Entrer le Nouveau Mot de Passe",
      confirmPassword: "Confirmer le Nouveau Mot de Passe",
      enterSecurityCode: "Entrer le Code de Sécurité",
      passwordMismatch: "Les mots de passe ne correspondent pas",
      weakPassword: "Le mot de passe est faible",
      passwordRequirements: "Le mot de passe doit contenir au moins 8 caractères",
      optional: "Optionnel",
      sendSecurityCode: "Envoyer le Code de Sécurité",
      confirmPasswordChange: "Confirmer le Changement de Mot de Passe",
      adminRole: "Administrateur Principal",
      adminName: "Administrateur du Site"
    },
    es: {
      title: "Perfil",
      personalInfo: "Información Personal",
      name: "Nombre",
      email: "Email",
      phone: "Teléfono",
      role: "Rol",
      lastLogin: "Último Inicio de Sesión",
      changePassword: "Cambiar Contraseña",
      currentPassword: "Contraseña Actual",
      newPassword: "Nueva Contraseña",
      confirmNewPassword: "Confirmar Nueva Contraseña",
      securityCode: "Código de Seguridad",
      save: "Guardar",
      cancel: "Cancelar",
      edit: "Editar",
      loading: "Cargando...",
      saving: "Guardando...",
      saved: "¡Guardado exitosamente!",
      error: "Error al guardar",
      enterName: "Ingrese el Nombre",
      enterEmail: "Ingrese el Email",
      enterPhone: "Ingrese el Teléfono",
      enterCurrentPassword: "Ingrese la Contraseña Actual",
      enterNewPassword: "Ingrese la Nueva Contraseña",
      confirmPassword: "Confirme la Nueva Contraseña",
      enterSecurityCode: "Ingrese el Código de Seguridad",
      passwordMismatch: "Las contraseñas no coinciden",
      weakPassword: "La contraseña es débil",
      passwordRequirements: "La contraseña debe contener al menos 8 caracteres",
      optional: "Opcional",
      sendSecurityCode: "Enviar Código de Seguridad",
      confirmPasswordChange: "Confirmar Cambio de Contraseña",
      adminRole: "Administrador Principal",
      adminName: "Administrador del Sitio"
    },
    uk: {
      title: "Профіль",
      personalInfo: "Особиста Інформація",
      name: "Ім'я",
      email: "Email",
      phone: "Телефон",
      role: "Роль",
      lastLogin: "Останній Вхід",
      changePassword: "Змінити Пароль",
      currentPassword: "Поточний Пароль",
      newPassword: "Новий Пароль",
      confirmNewPassword: "Підтвердити Новий Пароль",
      securityCode: "Код Безпеки",
      save: "Зберегти",
      cancel: "Скасувати",
      edit: "Редагувати",
      loading: "Завантаження...",
      saving: "Збереження...",
      saved: "Успішно збережено!",
      error: "Помилка при збереженні",
      enterName: "Введіть Ім'я",
      enterEmail: "Введіть Email",
      enterPhone: "Введіть Телефон",
      enterCurrentPassword: "Введіть Поточний Пароль",
      enterNewPassword: "Введіть Новий Пароль",
      confirmPassword: "Підтвердіть Новий Пароль",
      enterSecurityCode: "Введіть Код Безпеки",
      passwordMismatch: "Паролі не співпадають",
      weakPassword: "Пароль слабкий",
      passwordRequirements: "Пароль повинен містити принаймні 8 символів",
      optional: "Необов'язково",
      sendSecurityCode: "Надіслати Код Безпеки",
      confirmPasswordChange: "Підтвердити Зміну Пароля",
      adminRole: "Головний Адміністратор",
      adminName: "Адміністратор Сайту"
    }
  };

  const currentTexts = texts[language] || texts.ar;

  // حالة لتتبع ما إذا كان المستخدم مسجل دخول كمستخدم عادي أيضاً
  const [isDualLogin, setIsDualLogin] = useState(false);


  // التحقق من حالة تسجيل الدخول كمسؤول
  useEffect(() => {
    const isAdminAuthenticated = localStorage.getItem('admin_authenticated');
    if (!isAdminAuthenticated || isAdminAuthenticated !== 'true') {
      // إذا لم يكن المستخدم مسؤولاً، قم بتوجيهه إلى صفحة تسجيل دخول المسؤول
      router.push('/admin-panel-95/admin-login');
      return;
    }
    
    // التحقق مما إذا كان المستخدم مسجل دخول كمستخدم عادي أيضاً
    const isUserAuthenticated = localStorage.getItem('is_authenticated');
    if (isUserAuthenticated === 'true') {
      // تعيين حالة تسجيل الدخول المزدوج
      setIsDualLogin(true);
    }
    
    setIsLoading(false);
  }, [router]);
  
  // بيانات المدير
  const [adminData, setAdminData] = useState({
    name: currentTexts.adminName,
    email: '<EMAIL>',
    phone: '',
    role: currentTexts.adminRole,
    lastLogin: '2023-06-20T14:45:00',
  });

  // بيانات تغيير كلمة المرور
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmNewPassword: '',
    securityCode: ''
  });

  // حالة الأخطاء والنجاح
  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [securityCodeSent, setSecurityCodeSent] = useState(false);
  const [showSecurityCodeInput, setShowSecurityCodeInput] = useState(false);

  // وظيفة تغيير بيانات المدير
  const handleAdminDataChange = (e) => {
    const { name, value } = e.target;
    setAdminData({
      ...adminData,
      [name]: value
    });
    
    // مسح رسالة الخطأ عند الكتابة
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };

  // وظيفة تغيير بيانات كلمة المرور
  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData({
      ...passwordData,
      [name]: value
    });
    
    // مسح رسالة الخطأ عند الكتابة
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };

  // التحقق من صحة بيانات المدير
  const validateAdminData = () => {
    const newErrors = {};
    
    // التحقق من البريد الإلكتروني
    if (!adminData.email) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/\S+@\S+\.\S+/.test(adminData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صالح';
    }
    
    // التحقق من الاسم
    if (!adminData.name) {
      newErrors.name = 'الاسم مطلوب';
    }
    
    // التحقق من رقم الهاتف (اختياري)
    if (adminData.phone && !/^[\d\+\-\(\) ]+$/.test(adminData.phone)) {
      newErrors.phone = 'رقم الهاتف غير صالح';
    }
    
    return newErrors;
  };

  // التحقق من صحة بيانات تغيير كلمة المرور
  const validatePasswordData = () => {
    const newErrors = {};
    
    // التحقق من كلمة المرور الحالية
    if (!passwordData.currentPassword) {
      newErrors.currentPassword = currentTexts.enterCurrentPassword;
    }

    // التحقق من كلمة المرور الجديدة
    if (!passwordData.newPassword) {
      newErrors.newPassword = currentTexts.enterNewPassword;
    } else if (passwordData.newPassword.length < 8) {
      newErrors.newPassword = currentTexts.weakPassword;
    }

    // التحقق من تطابق كلمة المرور الجديدة
    if (!passwordData.confirmNewPassword) {
      newErrors.confirmNewPassword = currentTexts.confirmPassword;
    } else if (passwordData.newPassword !== passwordData.confirmNewPassword) {
      newErrors.confirmNewPassword = 'كلمة المرور الجديدة غير متطابقة';
    }
    
    return newErrors;
  };

  // التحقق من رمز الأمان
  const validateSecurityCode = () => {
    const newErrors = {};
    
    if (!passwordData.securityCode) {
      newErrors.securityCode = 'رمز الأمان مطلوب';
    } else if (passwordData.securityCode.length !== 6) {
      newErrors.securityCode = 'رمز الأمان يجب أن يكون 6 أرقام';
    }
    
    return newErrors;
  };

  // وظيفة تحديث بيانات المدير
  const handleUpdateAdminData = async (e) => {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    const formErrors = validateAdminData();
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }
    
    setIsSubmitting(true);
    setSuccessMessage('');
    
    try {
      // محاكاة تحديث البيانات
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // عرض رسالة نجاح
      setSuccessMessage(currentTexts.saved);

    } catch (error) {
      console.error('خطأ في تحديث البيانات:', error);
      setErrors({
        submit: currentTexts.error
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // وظيفة إرسال رمز الأمان
  const handleSendSecurityCode = async (e) => {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    const formErrors = validatePasswordData();
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }
    
    setIsSubmitting(true);
    setSuccessMessage('');
    
    try {
      // محاكاة إرسال رمز الأمان
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // عرض رسالة نجاح
      setSuccessMessage('تم إرسال رمز الأمان إلى بريدك الإلكتروني. يرجى التحقق من البريد الوارد.');
      
      // إظهار حقل إدخال رمز الأمان
      setSecurityCodeSent(true);
      setShowSecurityCodeInput(true);
      
    } catch (error) {
      console.error('خطأ في إرسال رمز الأمان:', error);
      setErrors({
        submit: currentTexts.error
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // وظيفة تغيير كلمة المرور
  const handleChangePassword = async (e) => {
    e.preventDefault();
    
    // التحقق من صحة رمز الأمان
    const securityCodeErrors = validateSecurityCode();
    if (Object.keys(securityCodeErrors).length > 0) {
      setErrors(securityCodeErrors);
      return;
    }
    
    setIsSubmitting(true);
    setSuccessMessage('');
    
    try {
      // محاكاة تغيير كلمة المرور
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // عرض رسالة نجاح
      setSuccessMessage(currentTexts.saved);

      // إعادة تعيين حقول النموذج
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmNewPassword: '',
        securityCode: ''
      });

      // إعادة تعيين حالة رمز الأمان
      setSecurityCodeSent(false);
      setShowSecurityCodeInput(false);

    } catch (error) {
      console.error('خطأ في تغيير كلمة المرور:', error);
      setErrors({
        submit: currentTexts.error
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    const options = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    const locale = language === 'ar' ? 'ar-EG' : 
                  language === 'fr' ? 'fr-FR' : 
                  language === 'es' ? 'es-ES' : 
                  language === 'uk' ? 'uk-UA' : 'en-US';
    return new Date(dateString).toLocaleDateString(locale, options);
  };

  return (
    <div className="admin-profile">
      <h1 className="page-title">{currentTexts.title}</h1>

      {/* رسالة تنبيه لتسجيل الدخول المزدوج */}
      {isDualLogin && (
        <div className="warning-message">
          <span className="warning-icon">⚠️</span>
          أنت مسجل دخول حالياً كمستخدم عادي ومسؤول في نفس الوقت. يمكنك الوصول إلى كلا الحسابين من خلال صفحاتهما المخصصة.
          <a href="/profile" className="warning-link">الذهاب إلى الملف الشخصي للمستخدم</a>
        </div>
      )}
      
      {successMessage && (
        <div className="success-message">
          {successMessage}
        </div>
      )}
      
      {errors.submit && (
        <div className="error-message">
          {errors.submit}
        </div>
      )}
      
      <div className="profile-container">
        <div className="profile-section card">
          <div className="card-header">
            <h2 className="card-title">{currentTexts.personalInfo}</h2>
          </div>

          <form onSubmit={handleUpdateAdminData} className="profile-form">
            <div className="form-group">
              <label htmlFor="name">{currentTexts.name}</label>
              <div className="input-wrapper">
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={adminData.name}
                  onChange={handleAdminDataChange}
                  className={errors.name ? 'input-error' : ''}
                  placeholder={currentTexts.enterName}
                  disabled={isSubmitting}
                />
                <div className="input-icon">👤</div>
              </div>
              {errors.name && <p className="error-text">{errors.name}</p>}
            </div>

            <div className="form-group">
              <label htmlFor="email">{currentTexts.email}</label>
              <div className="input-wrapper">
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={adminData.email}
                  onChange={handleAdminDataChange}
                  className={errors.email ? 'input-error' : ''}
                  placeholder={currentTexts.enterEmail}
                  disabled={isSubmitting}
                />
                <div className="input-icon">✉️</div>
              </div>
              {errors.email && <p className="error-text">{errors.email}</p>}
            </div>

            <div className="form-group">
              <label htmlFor="phone">{currentTexts.phone} ({currentTexts.optional})</label>
              <div className="input-wrapper">
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={adminData.phone}
                  onChange={handleAdminDataChange}
                  className={errors.phone ? 'input-error' : ''}
                  placeholder={currentTexts.enterPhone}
                  disabled={isSubmitting}
                />
                <div className="input-icon">📱</div>
              </div>
              {errors.phone && <p className="error-text">{errors.phone}</p>}
            </div>

            <div className="form-group">
              <label htmlFor="role">{currentTexts.role}</label>
              <div className="input-wrapper">
                <input
                  type="text"
                  id="role"
                  value={adminData.role}
                  disabled
                  className="input-disabled"
                />
                <div className="input-icon">🔑</div>
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="lastLogin">{currentTexts.lastLogin}</label>
              <div className="input-wrapper">
                <input
                  type="text"
                  id="lastLogin"
                  value={formatDate(adminData.lastLogin)}
                  disabled
                  className="input-disabled"
                />
                <div className="input-icon">🕒</div>
              </div>
            </div>

            <div className="form-actions">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="spinner"></span>
                    <span>{currentTexts.saving}</span>
                  </>
                ) : (
                  currentTexts.save
                )}
              </button>
            </div>
          </form>
        </div>
        
        <div className="profile-section card">
          <div className="card-header">
            <h2 className="card-title">{currentTexts.changePassword}</h2>
          </div>
          
          <form onSubmit={securityCodeSent ? handleChangePassword : handleSendSecurityCode} className="profile-form">
            <div className="form-group">
              <label htmlFor="currentPassword">{currentTexts.currentPassword}</label>
              <div className="input-wrapper">
                <input
                  type="password"
                  id="currentPassword"
                  name="currentPassword"
                  value={passwordData.currentPassword}
                  onChange={handlePasswordChange}
                  className={errors.currentPassword ? 'input-error' : ''}
                  placeholder={currentTexts.enterCurrentPassword}
                  disabled={isSubmitting || securityCodeSent}
                />
                <div className="input-icon">🔒</div>
              </div>
              {errors.currentPassword && <p className="error-text">{errors.currentPassword}</p>}
            </div>
            
            <div className="form-group">
              <label htmlFor="newPassword">{currentTexts.newPassword}</label>
              <div className="input-wrapper">
                <input
                  type="password"
                  id="newPassword"
                  name="newPassword"
                  value={passwordData.newPassword}
                  onChange={handlePasswordChange}
                  className={errors.newPassword ? 'input-error' : ''}
                  placeholder={currentTexts.enterNewPassword}
                  disabled={isSubmitting || securityCodeSent}
                />
                <div className="input-icon">🔑</div>
              </div>
              {errors.newPassword && <p className="error-text">{errors.newPassword}</p>}
              <p className="form-hint">
                {currentTexts.passwordRequirements}
              </p>
            </div>
            
            <div className="form-group">
              <label htmlFor="confirmNewPassword">{currentTexts.confirmNewPassword}</label>
              <div className="input-wrapper">
                <input
                  type="password"
                  id="confirmNewPassword"
                  name="confirmNewPassword"
                  value={passwordData.confirmNewPassword}
                  onChange={handlePasswordChange}
                  className={errors.confirmNewPassword ? 'input-error' : ''}
                  placeholder={currentTexts.confirmPassword}
                  disabled={isSubmitting || securityCodeSent}
                />
                <div className="input-icon">🔑</div>
              </div>
              {errors.confirmNewPassword && <p className="error-text">{errors.confirmNewPassword}</p>}
            </div>
            
            {showSecurityCodeInput && (
              <div className="form-group">
                <label htmlFor="securityCode">{currentTexts.securityCode}</label>
                <div className="input-wrapper">
                  <input
                    type="text"
                    id="securityCode"
                    name="securityCode"
                    value={passwordData.securityCode}
                    onChange={handlePasswordChange}
                    className={errors.securityCode ? 'input-error' : ''}
                    placeholder={currentTexts.enterSecurityCode}
                    maxLength={6}
                    disabled={isSubmitting}
                  />
                  <div className="input-icon">🔢</div>
                </div>
                {errors.securityCode && <p className="error-text">{errors.securityCode}</p>}
              </div>
            )}
            
            <div className="form-actions">
              <button 
                type="submit" 
                className="btn btn-primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="spinner"></span>
                    <span>{currentTexts.saving}</span>
                  </>
                ) : securityCodeSent ? (
                  currentTexts.confirmPasswordChange
                ) : (
                  currentTexts.sendSecurityCode
                )}
              </button>
              
              {securityCodeSent && (
                <button 
                  type="button" 
                  className="btn btn-secondary"
                  onClick={() => {
                    setSecurityCodeSent(false);
                    setShowSecurityCodeInput(false);
                    setPasswordData({
                      ...passwordData,
                      securityCode: ''
                    });
                  }}
                  disabled={isSubmitting}
                >
                  {currentTexts.cancel}
                </button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}