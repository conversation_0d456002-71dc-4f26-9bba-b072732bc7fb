'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslation } from '../../components/TranslationProvider'
import './profile.css'

export default function AdminProfile() {
  const { t, language } = useTranslation();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  // حالة لتتبع ما إذا كان المستخدم مسجل دخول كمستخدم عادي أيضاً
  const [isDualLogin, setIsDualLogin] = useState(false);


  // التحقق من حالة تسجيل الدخول كمسؤول
  useEffect(() => {
    const isAdminAuthenticated = localStorage.getItem('admin_authenticated');
    if (!isAdminAuthenticated || isAdminAuthenticated !== 'true') {
      // إذا لم يكن المستخدم مسؤولاً، قم بتوجيهه إلى صفحة تسجيل دخول المسؤول
      router.push('/admin-panel-95/admin-login');
      return;
    }
    
    // التحقق مما إذا كان المستخدم مسجل دخول كمستخدم عادي أيضاً
    const isUserAuthenticated = localStorage.getItem('is_authenticated');
    if (isUserAuthenticated === 'true') {
      // تعيين حالة تسجيل الدخول المزدوج
      setIsDualLogin(true);
    }
    
    setIsLoading(false);
  }, [router]);
  
  // بيانات المدير
  const [adminData, setAdminData] = useState({
    name: 'مدير الموقع',
    email: '<EMAIL>',
    phone: '',
    role: 'مدير رئيسي',
    lastLogin: '2023-06-20T14:45:00',
  });

  // بيانات تغيير كلمة المرور
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmNewPassword: '',
    securityCode: ''
  });

  // حالة الأخطاء والنجاح
  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [securityCodeSent, setSecurityCodeSent] = useState(false);
  const [showSecurityCodeInput, setShowSecurityCodeInput] = useState(false);

  // وظيفة تغيير بيانات المدير
  const handleAdminDataChange = (e) => {
    const { name, value } = e.target;
    setAdminData({
      ...adminData,
      [name]: value
    });
    
    // مسح رسالة الخطأ عند الكتابة
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };

  // وظيفة تغيير بيانات كلمة المرور
  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData({
      ...passwordData,
      [name]: value
    });
    
    // مسح رسالة الخطأ عند الكتابة
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };

  // التحقق من صحة بيانات المدير
  const validateAdminData = () => {
    const newErrors = {};
    
    // التحقق من البريد الإلكتروني
    if (!adminData.email) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/\S+@\S+\.\S+/.test(adminData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صالح';
    }
    
    // التحقق من الاسم
    if (!adminData.name) {
      newErrors.name = 'الاسم مطلوب';
    }
    
    // التحقق من رقم الهاتف (اختياري)
    if (adminData.phone && !/^[\d\+\-\(\) ]+$/.test(adminData.phone)) {
      newErrors.phone = 'رقم الهاتف غير صالح';
    }
    
    return newErrors;
  };

  // التحقق من صحة بيانات تغيير كلمة المرور
  const validatePasswordData = () => {
    const newErrors = {};
    
    // التحقق من كلمة المرور الحالية
    if (!passwordData.currentPassword) {
      newErrors.currentPassword = t('common.errors.currentPasswordRequired');
    }
    
    // التحقق من كلمة المرور الجديدة
    if (!passwordData.newPassword) {
      newErrors.newPassword = t('common.errors.newPasswordRequired');
    } else if (passwordData.newPassword.length < 8) {
      newErrors.newPassword = t('admin.profile.passwordRequirements');
    }
    
    // التحقق من تطابق كلمة المرور الجديدة
    if (!passwordData.confirmNewPassword) {
      newErrors.confirmNewPassword = t('common.errors.confirmPasswordRequired');
    } else if (passwordData.newPassword !== passwordData.confirmNewPassword) {
      newErrors.confirmNewPassword = 'كلمة المرور الجديدة غير متطابقة';
    }
    
    return newErrors;
  };

  // التحقق من رمز الأمان
  const validateSecurityCode = () => {
    const newErrors = {};
    
    if (!passwordData.securityCode) {
      newErrors.securityCode = 'رمز الأمان مطلوب';
    } else if (passwordData.securityCode.length !== 6) {
      newErrors.securityCode = 'رمز الأمان يجب أن يكون 6 أرقام';
    }
    
    return newErrors;
  };

  // وظيفة تحديث بيانات المدير
  const handleUpdateAdminData = async (e) => {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    const formErrors = validateAdminData();
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }
    
    setIsSubmitting(true);
    setSuccessMessage('');
    
    try {
      // محاكاة تحديث البيانات
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // عرض رسالة نجاح
      setSuccessMessage(t('admin.profile.changesSaved'));
      
    } catch (error) {
      console.error('خطأ في تحديث البيانات:', error);
      setErrors({
        submit: t('common.errors.updateFailed')
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // وظيفة إرسال رمز الأمان
  const handleSendSecurityCode = async (e) => {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    const formErrors = validatePasswordData();
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }
    
    setIsSubmitting(true);
    setSuccessMessage('');
    
    try {
      // محاكاة إرسال رمز الأمان
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // عرض رسالة نجاح
      setSuccessMessage('تم إرسال رمز الأمان إلى بريدك الإلكتروني. يرجى التحقق من البريد الوارد.');
      
      // إظهار حقل إدخال رمز الأمان
      setSecurityCodeSent(true);
      setShowSecurityCodeInput(true);
      
    } catch (error) {
      console.error('خطأ في إرسال رمز الأمان:', error);
      setErrors({
        submit: t('common.errors.securityCodeSendFailed')
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // وظيفة تغيير كلمة المرور
  const handleChangePassword = async (e) => {
    e.preventDefault();
    
    // التحقق من صحة رمز الأمان
    const securityCodeErrors = validateSecurityCode();
    if (Object.keys(securityCodeErrors).length > 0) {
      setErrors(securityCodeErrors);
      return;
    }
    
    setIsSubmitting(true);
    setSuccessMessage('');
    
    try {
      // محاكاة تغيير كلمة المرور
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // عرض رسالة نجاح
      setSuccessMessage(t('admin.profile.passwordChanged'));
      
      // إعادة تعيين حقول النموذج
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmNewPassword: '',
        securityCode: ''
      });
      
      // إعادة تعيين حالة رمز الأمان
      setSecurityCodeSent(false);
      setShowSecurityCodeInput(false);
      
    } catch (error) {
      console.error('خطأ في تغيير كلمة المرور:', error);
      setErrors({
        submit: t('common.errors.passwordChangeFailed')
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    const options = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    const locale = language === 'ar' ? 'ar-EG' : 
                  language === 'fr' ? 'fr-FR' : 
                  language === 'es' ? 'es-ES' : 
                  language === 'uk' ? 'uk-UA' : 'en-US';
    return new Date(dateString).toLocaleDateString(locale, options);
  };

  // عدم عرض المكون حتى يتم تحميله من جانب العميل
  if (!isClient) {
    return (
      <div className="admin-profile">
        <h1 className="page-title">Loading...</h1>
        <div className="loading-spinner">جاري التحميل...</div>
      </div>
    );
  }

  return (
    <div className="admin-profile">
      <h1 className="page-title">{t('admin.profile.title', 'الملف الشخصي للمسؤول')}</h1>
      
      {/* رسالة تنبيه لتسجيل الدخول المزدوج */}
      {isDualLogin && (
        <div className="warning-message">
          <span className="warning-icon">⚠️</span>
          {t('admin.profile.userAndAdminAlert', 'أنت مسجل دخول حالياً كمستخدم عادي ومسؤول في نفس الوقت. يمكنك الوصول إلى كلا الحسابين من خلال صفحاتهما المخصصة.')}
          <a href="/profile" className="warning-link">{t('admin.profile.goToUserProfile', 'الذهاب إلى الملف الشخصي للمستخدم')}</a>
        </div>
      )}
      
      {successMessage && (
        <div className="success-message">
          {successMessage}
        </div>
      )}
      
      {errors.submit && (
        <div className="error-message">
          {errors.submit}
        </div>
      )}
      
      <div className="profile-container">
        <div className="profile-section card">
          <div className="card-header">
            <h2 className="card-title">{t('admin.profile.personalInfo')}</h2>
          </div>
          
          <form onSubmit={handleUpdateAdminData} className="profile-form">
            <div className="form-group">
              <label htmlFor="name">{t('common.labels.name')}</label>
              <div className="input-wrapper">
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={adminData.name}
                  onChange={handleAdminDataChange}
                  className={errors.name ? 'input-error' : ''}
                  placeholder={t('admin.profile.namePlaceholder')}
                  disabled={isSubmitting}
                />
                <div className="input-icon">👤</div>
              </div>
              {errors.name && <p className="error-text">{errors.name}</p>}
            </div>
            
            <div className="form-group">
              <label htmlFor="email">{t('common.labels.email')}</label>
              <div className="input-wrapper">
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={adminData.email}
                  onChange={handleAdminDataChange}
                  className={errors.email ? 'input-error' : ''}
                  placeholder={t('admin.profile.emailPlaceholder')}
                  disabled={isSubmitting}
                />
                <div className="input-icon">✉️</div>
              </div>
              {errors.email && <p className="error-text">{errors.email}</p>}
            </div>
            
            <div className="form-group">
              <label htmlFor="phone">{t('common.labels.phone')} ({t('common.labels.optional')})</label>
              <div className="input-wrapper">
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={adminData.phone}
                  onChange={handleAdminDataChange}
                  className={errors.phone ? 'input-error' : ''}
                  placeholder={t('admin.profile.phonePlaceholder')}
                  disabled={isSubmitting}
                />
                <div className="input-icon">📱</div>
              </div>
              {errors.phone && <p className="error-text">{errors.phone}</p>}
            </div>
            
            <div className="form-group">
              <label htmlFor="role">{t('admin.users.role')}</label>
              <div className="input-wrapper">
                <input
                  type="text"
                  id="role"
                  value={adminData.role}
                  disabled
                  className="input-disabled"
                />
                <div className="input-icon">🔑</div>
              </div>
            </div>
            
            <div className="form-group">
              <label htmlFor="lastLogin">{t('admin.users.lastLogin')}</label>
              <div className="input-wrapper">
                <input
                  type="text"
                  id="lastLogin"
                  value={formatDate(adminData.lastLogin)}
                  disabled
                  className="input-disabled"
                />
                <div className="input-icon">🕒</div>
              </div>
            </div>
            
            <div className="form-actions">
              <button 
                type="submit" 
                className="btn btn-primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="spinner"></span>
                    <span>{t('common.messages.processing')}</span>
                  </>
                ) : (
                  t('admin.profile.saveChanges')
                )}
              </button>
            </div>
          </form>
        </div>
        
        <div className="profile-section card">
          <div className="card-header">
            <h2 className="card-title">{t('admin.profile.changePassword')}</h2>
          </div>
          
          <form onSubmit={securityCodeSent ? handleChangePassword : handleSendSecurityCode} className="profile-form">
            <div className="form-group">
              <label htmlFor="currentPassword">{t('common.labels.currentPassword')}</label>
              <div className="input-wrapper">
                <input
                  type="password"
                  id="currentPassword"
                  name="currentPassword"
                  value={passwordData.currentPassword}
                  onChange={handlePasswordChange}
                  className={errors.currentPassword ? 'input-error' : ''}
                  placeholder={t('admin.profile.currentPasswordPlaceholder')}
                  disabled={isSubmitting || securityCodeSent}
                />
                <div className="input-icon">🔒</div>
              </div>
              {errors.currentPassword && <p className="error-text">{errors.currentPassword}</p>}
            </div>
            
            <div className="form-group">
              <label htmlFor="newPassword">{t('common.labels.newPassword')}</label>
              <div className="input-wrapper">
                <input
                  type="password"
                  id="newPassword"
                  name="newPassword"
                  value={passwordData.newPassword}
                  onChange={handlePasswordChange}
                  className={errors.newPassword ? 'input-error' : ''}
                  placeholder={t('admin.profile.newPasswordPlaceholder')}
                  disabled={isSubmitting || securityCodeSent}
                />
                <div className="input-icon">🔑</div>
              </div>
              {errors.newPassword && <p className="error-text">{errors.newPassword}</p>}
              <p className="form-hint">
                {t('admin.profile.passwordRequirements')}
              </p>
            </div>
            
            <div className="form-group">
              <label htmlFor="confirmNewPassword">{t('common.labels.confirmPassword')}</label>
              <div className="input-wrapper">
                <input
                  type="password"
                  id="confirmNewPassword"
                  name="confirmNewPassword"
                  value={passwordData.confirmNewPassword}
                  onChange={handlePasswordChange}
                  className={errors.confirmNewPassword ? 'input-error' : ''}
                  placeholder={t('admin.profile.confirmPasswordPlaceholder')}
                  disabled={isSubmitting || securityCodeSent}
                />
                <div className="input-icon">🔑</div>
              </div>
              {errors.confirmNewPassword && <p className="error-text">{errors.confirmNewPassword}</p>}
            </div>
            
            {showSecurityCodeInput && (
              <div className="form-group">
                <label htmlFor="securityCode">{t('admin.profile.securityCode')}</label>
                <div className="input-wrapper">
                  <input
                    type="text"
                    id="securityCode"
                    name="securityCode"
                    value={passwordData.securityCode}
                    onChange={handlePasswordChange}
                    className={errors.securityCode ? 'input-error' : ''}
                    placeholder={t('admin.profile.securityCodePlaceholder')}
                    maxLength={6}
                    disabled={isSubmitting}
                  />
                  <div className="input-icon">🔢</div>
                </div>
                {errors.securityCode && <p className="error-text">{errors.securityCode}</p>}
              </div>
            )}
            
            <div className="form-actions">
              <button 
                type="submit" 
                className="btn btn-primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="spinner"></span>
                    <span>{t('common.messages.processing')}</span>
                  </>
                ) : securityCodeSent ? (
                  t('admin.profile.confirmPasswordChange')
                ) : (
                  t('admin.profile.sendSecurityCode')
                )}
              </button>
              
              {securityCodeSent && (
                <button 
                  type="button" 
                  className="btn btn-secondary"
                  onClick={() => {
                    setSecurityCodeSent(false);
                    setShowSecurityCodeInput(false);
                    setPasswordData({
                      ...passwordData,
                      securityCode: ''
                    });
                  }}
                  disabled={isSubmitting}
                >
                  {t('common.buttons.cancel')}
                </button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}