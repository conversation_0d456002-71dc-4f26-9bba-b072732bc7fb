"use client";

import { useState, useEffect } from "react";
import { useTranslation } from "../../components/TranslationProvider";
import "../admin-styles.css";

export default function AssistantsManagement() {
  const { t, language, direction } = useTranslation();
  const [assistants, setAssistants] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedAssistant, setSelectedAssistant] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    permissions: {
      viewDashboard: true,
      viewAnalytics: false,
      manageUsers: false,
      manageSettings: false,
    },
  });

  // النصوص حسب اللغة
  const texts = {
    ar: {
      title: "إدارة المساعدين",
      addAssistant: "إضافة مساعد جديد",
      editAssistant: "تعديل المساعد",
      searchPlaceholder: "البحث عن مساعد...",
      name: "الاسم",
      email: "البريد الإلكتروني",
      status: "الحالة",
      createdAt: "تاريخ الإنشاء",
      lastLogin: "آخر تسجيل دخول",
      permissions: "الصلاحيات",
      actions: "الإجراءات",
      password: "كلمة المرور",
      newPassword: "كلمة المرور الجديدة",
      optional: "اختياري",
      passwordPlaceholder: "اتركه فارغاً للاحتفاظ بكلمة المرور الحالية",
      edit: "تعديل",
      delete: "حذف",
      cancel: "إلغاء",
      add: "إضافة",
      save: "حفظ",
      loading: "جاري التحميل...",
      noAssistants: "لا يوجد مساعدين",
      neverLoggedIn: "لم يسجل دخول من قبل",
      confirmDelete: "هل أنت متأكد من حذف هذا المساعد؟",
      active: "نشط",
      inactive: "غير نشط",
      suspended: "معلق",
      viewDashboard: "عرض لوحة التحكم",
      viewAnalytics: "عرض الإحصائيات",
      manageUsers: "إدارة المستخدمين",
      manageSettings: "إدارة الإعدادات",
    },
    en: {
      title: "Assistant Management",
      addAssistant: "Add New Assistant",
      editAssistant: "Edit Assistant",
      searchPlaceholder: "Search for assistant...",
      name: "Name",
      email: "Email",
      status: "Status",
      createdAt: "Created At",
      lastLogin: "Last Login",
      permissions: "Permissions",
      actions: "Actions",
      password: "Password",
      newPassword: "New Password",
      optional: "Optional",
      passwordPlaceholder: "Leave empty to keep current password",
      edit: "Edit",
      delete: "Delete",
      cancel: "Cancel",
      add: "Add",
      save: "Save",
      loading: "Loading...",
      noAssistants: "No assistants found",
      neverLoggedIn: "Never logged in",
      confirmDelete: "Are you sure you want to delete this assistant?",
      active: "Active",
      inactive: "Inactive",
      suspended: "Suspended",
      viewDashboard: "View Dashboard",
      viewAnalytics: "View Analytics",
      manageUsers: "Manage Users",
      manageSettings: "Manage Settings",
    },
    fr: {
      title: "Gestion des Assistants",
      addAssistant: "Ajouter un Nouvel Assistant",
      editAssistant: "Modifier l'Assistant",
      searchPlaceholder: "Rechercher un assistant...",
      name: "Nom",
      email: "Email",
      status: "Statut",
      createdAt: "Créé le",
      lastLogin: "Dernière Connexion",
      permissions: "Permissions",
      actions: "Actions",
      password: "Mot de passe",
      newPassword: "Nouveau Mot de passe",
      optional: "Optionnel",
      passwordPlaceholder: "Laisser vide pour conserver le mot de passe actuel",
      edit: "Modifier",
      delete: "Supprimer",
      cancel: "Annuler",
      add: "Ajouter",
      save: "Enregistrer",
      loading: "Chargement...",
      noAssistants: "Aucun assistant trouvé",
      neverLoggedIn: "Jamais connecté",
      confirmDelete: "Êtes-vous sûr de vouloir supprimer cet assistant?",
      active: "Actif",
      inactive: "Inactif",
      suspended: "Suspendu",
      viewDashboard: "Voir le Tableau de bord",
      viewAnalytics: "Voir l'Analytique",
      manageUsers: "Gérer les Utilisateurs",
      manageSettings: "Gérer les Paramètres",
    },
    es: {
      title: "Gestión de Asistentes",
      addAssistant: "Agregar Nuevo Asistente",
      editAssistant: "Editar Asistente",
      searchPlaceholder: "Buscar asistente...",
      name: "Nombre",
      email: "Email",
      status: "Estado",
      createdAt: "Creado en",
      lastLogin: "Último Acceso",
      permissions: "Permisos",
      actions: "Acciones",
      password: "Contraseña",
      newPassword: "Nueva Contraseña",
      optional: "Opcional",
      passwordPlaceholder: "Dejar vacío para mantener la contraseña actual",
      edit: "Editar",
      delete: "Eliminar",
      cancel: "Cancelar",
      add: "Agregar",
      save: "Guardar",
      loading: "Cargando...",
      noAssistants: "No se encontraron asistentes",
      neverLoggedIn: "Nunca se conectó",
      confirmDelete: "¿Estás seguro de que quieres eliminar este asistente?",
      active: "Activo",
      inactive: "Inactivo",
      suspended: "Suspendido",
      viewDashboard: "Ver Panel de Control",
      viewAnalytics: "Ver Analíticas",
      manageUsers: "Gestionar Usuarios",
      manageSettings: "Gestionar Configuración",
    },
    uk: {
      title: "Управління Помічниками",
      addAssistant: "Додати Нового Помічника",
      editAssistant: "Редагувати Помічника",
      searchPlaceholder: "Шукати помічника...",
      name: "Ім'я",
      email: "Email",
      status: "Статус",
      createdAt: "Створено",
      lastLogin: "Останній Вхід",
      permissions: "Дозволи",
      actions: "Дії",
      password: "Пароль",
      newPassword: "Новий Пароль",
      optional: "Необов'язково",
      passwordPlaceholder: "Залишити порожнім для збереження поточного пароля",
      edit: "Редагувати",
      delete: "Видалити",
      cancel: "Скасувати",
      add: "Додати",
      save: "Зберегти",
      loading: "Завантаження...",
      noAssistants: "Помічників не знайдено",
      neverLoggedIn: "Ніколи не входив",
      confirmDelete: "Ви впевнені, що хочете видалити цього помічника?",
      active: "Активний",
      inactive: "Неактивний",
      suspended: "Призупинений",
      viewDashboard: "Переглянути Панель",
      viewAnalytics: "Переглянути Аналітику",
      manageUsers: "Управляти Користувачами",
      manageSettings: "Управляти Налаштуваннями",
    },
  };

  const currentTexts = texts[language] || texts.ar;

  // جلب بيانات المساعدين من API
  useEffect(() => {
    const fetchAssistants = async () => {
      try {
        setIsLoading(true);
        // في المستقبل، سيتم استبدال هذا بطلب API حقيقي
        // const response = await fetch('/api/admin/assistants');
        // const data = await response.json();

        // حالياً نعرض قائمة فارغة حتى يتم ربط النظام بقاعدة البيانات
        setAssistants([]);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching assistants:", error);
        setAssistants([]);
        setIsLoading(false);
      }
    };

    fetchAssistants();
  }, []);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (name.startsWith("permissions.")) {
      const permissionKey = name.split(".")[1];
      setFormData((prev) => ({
        ...prev,
        permissions: {
          ...prev.permissions,
          [permissionKey]: checked,
        },
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: type === "checkbox" ? checked : value,
      }));
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      email: "",
      password: "",
      permissions: {
        viewDashboard: true,
        viewAnalytics: false,
        manageUsers: false,
        manageSettings: false,
      },
    });
  };

  const handleAddAssistant = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // محاكاة إضافة مساعد جديد
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const newAssistant = {
        id: Date.now(),
        name: formData.name,
        email: formData.email,
        status: "active",
        createdAt: new Date().toISOString().split("T")[0],
        lastLogin: null,
        permissions: formData.permissions,
      };

      setAssistants((prev) => [...prev, newAssistant]);
      setShowAddModal(false);
      resetForm();
    } catch (error) {
      console.error("Error adding assistant:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditAssistant = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // محاكاة تحديث المساعد
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setAssistants((prev) =>
        prev.map((assistant) =>
          assistant.id === selectedAssistant.id
            ? { ...assistant, ...formData }
            : assistant,
        ),
      );

      setShowEditModal(false);
      setSelectedAssistant(null);
      resetForm();
    } catch (error) {
      console.error("Error updating assistant:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAssistant = async (assistantId) => {
    if (!confirm(currentTexts.confirmDelete)) {
      return;
    }

    try {
      // محاكاة حذف المساعد
      await new Promise((resolve) => setTimeout(resolve, 500));
      setAssistants((prev) =>
        prev.filter((assistant) => assistant.id !== assistantId),
      );
    } catch (error) {
      console.error("Error deleting assistant:", error);
    }
  };

  const openEditModal = (assistant) => {
    setSelectedAssistant(assistant);
    setFormData({
      name: assistant.name,
      email: assistant.email,
      password: "",
      permissions: assistant.permissions,
    });
    setShowEditModal(true);
  };

  const filteredAssistants = assistants.filter(
    (assistant) =>
      assistant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      assistant.email.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  return (
    <div className="assistants-management" dir={direction}>
      <div className="page-header">
        <h1 className="page-title">{currentTexts.title}</h1>
        <button
          className="btn btn-primary"
          onClick={() => setShowAddModal(true)}
        >
          {currentTexts.addAssistant}
        </button>
      </div>

      <div className="assistants-controls">
        <div className="search-box">
          <input
            type="text"
            placeholder={currentTexts.searchPlaceholder}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
      </div>

      <div className="assistants-table-container">
        <table className="assistants-table">
          <thead>
            <tr>
              <th>{currentTexts.name}</th>
              <th>{currentTexts.email}</th>
              <th>{currentTexts.status}</th>
              <th>{currentTexts.createdAt}</th>
              <th>{currentTexts.lastLogin}</th>
              <th>{currentTexts.permissions}</th>
              <th>{currentTexts.actions}</th>
            </tr>
          </thead>
          <tbody>
            {filteredAssistants.map((assistant) => (
              <tr key={assistant.id}>
                <td>{assistant.name}</td>
                <td>{assistant.email}</td>
                <td>
                  <span className={`status-badge ${assistant.status}`}>
                    {assistant.status === "active"
                      ? currentTexts.active
                      : assistant.status === "inactive"
                        ? currentTexts.inactive
                        : assistant.status === "suspended"
                          ? currentTexts.suspended
                          : assistant.status}
                  </span>
                </td>
                <td>{assistant.createdAt}</td>
                <td>{assistant.lastLogin || currentTexts.neverLoggedIn}</td>
                <td>
                  <div className="permissions-list">
                    {Object.entries(assistant.permissions)
                      .filter(([key, value]) => value)
                      .map(([key]) => (
                        <span key={key} className="permission-tag">
                          {key === "viewDashboard"
                            ? currentTexts.viewDashboard
                            : key === "viewAnalytics"
                              ? currentTexts.viewAnalytics
                              : key === "manageUsers"
                                ? currentTexts.manageUsers
                                : key === "manageSettings"
                                  ? currentTexts.manageSettings
                                  : key}
                        </span>
                      ))}
                  </div>
                </td>
                <td>
                  <div className="action-buttons">
                    <button
                      className="btn btn-sm btn-secondary"
                      onClick={() => openEditModal(assistant)}
                    >
                      {currentTexts.edit}
                    </button>
                    <button
                      className="btn btn-sm btn-danger"
                      onClick={() => handleDeleteAssistant(assistant.id)}
                    >
                      {currentTexts.delete}
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {filteredAssistants.length === 0 && (
          <div className="no-data">{currentTexts.noAssistants}</div>
        )}
      </div>

      {/* نافذة إضافة مساعد */}
      {showAddModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>{currentTexts.addAssistant}</h2>
              <button
                className="modal-close"
                onClick={() => {
                  setShowAddModal(false);
                  resetForm();
                }}
              >
                ×
              </button>
            </div>
            <form onSubmit={handleAddAssistant} className="modal-form">
              <div className="form-group">
                <label>{currentTexts.name}</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  className="form-input"
                />
              </div>

              <div className="form-group">
                <label>{currentTexts.email}</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="form-input"
                />
              </div>

              <div className="form-group">
                <label>{currentTexts.password}</label>
                <input
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  className="form-input"
                />
              </div>

              <div className="form-group">
                <label>{currentTexts.permissions}</label>
                <div className="permissions-checkboxes">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      name="permissions.viewDashboard"
                      checked={formData.permissions.viewDashboard}
                      onChange={handleInputChange}
                    />
                    {currentTexts.viewDashboard}
                  </label>
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      name="permissions.viewAnalytics"
                      checked={formData.permissions.viewAnalytics}
                      onChange={handleInputChange}
                    />
                    {currentTexts.viewAnalytics}
                  </label>
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      name="permissions.manageUsers"
                      checked={formData.permissions.manageUsers}
                      onChange={handleInputChange}
                    />
                    {currentTexts.manageUsers}
                  </label>
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      name="permissions.manageSettings"
                      checked={formData.permissions.manageSettings}
                      onChange={handleInputChange}
                    />
                    {currentTexts.manageSettings}
                  </label>
                </div>
              </div>

              <div className="modal-actions">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowAddModal(false);
                    resetForm();
                  }}
                >
                  {currentTexts.cancel}
                </button>
                <button
                  type="submit"
                  className="btn btn-primary"
                  disabled={isLoading}
                >
                  {isLoading ? currentTexts.loading : currentTexts.add}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* نافذة تعديل مساعد */}
      {showEditModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>{currentTexts.editAssistant}</h2>
              <button
                className="modal-close"
                onClick={() => {
                  setShowEditModal(false);
                  setSelectedAssistant(null);
                  resetForm();
                }}
              >
                ×
              </button>
            </div>
            <form onSubmit={handleEditAssistant} className="modal-form">
              <div className="form-group">
                <label>{currentTexts.name}</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  className="form-input"
                />
              </div>

              <div className="form-group">
                <label>{currentTexts.email}</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="form-input"
                />
              </div>

              <div className="form-group">
                <label>
                  {currentTexts.newPassword} ({currentTexts.optional})
                </label>
                <input
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder={currentTexts.passwordPlaceholder}
                />
              </div>

              <div className="form-group">
                <label>{currentTexts.permissions}</label>
                <div className="permissions-checkboxes">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      name="permissions.viewDashboard"
                      checked={formData.permissions.viewDashboard}
                      onChange={handleInputChange}
                    />
                    {currentTexts.viewDashboard}
                  </label>
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      name="permissions.viewAnalytics"
                      checked={formData.permissions.viewAnalytics}
                      onChange={handleInputChange}
                    />
                    {currentTexts.viewAnalytics}
                  </label>
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      name="permissions.manageUsers"
                      checked={formData.permissions.manageUsers}
                      onChange={handleInputChange}
                    />
                    {currentTexts.manageUsers}
                  </label>
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      name="permissions.manageSettings"
                      checked={formData.permissions.manageSettings}
                      onChange={handleInputChange}
                    />
                    {currentTexts.manageSettings}
                  </label>
                </div>
              </div>

              <div className="modal-actions">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowEditModal(false);
                    setSelectedAssistant(null);
                    resetForm();
                  }}
                >
                  {currentTexts.cancel}
                </button>
                <button
                  type="submit"
                  className="btn btn-primary"
                  disabled={isLoading}
                >
                  {isLoading ? currentTexts.loading : currentTexts.save}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
