'use client';

import Link from 'next/link';
import { useTranslation } from './components/TranslationProvider';

export default function Home() {
  const { t } = useTranslation();
  
  return (
    <div>
      {/* Hero Section */}
      <div style={heroStyle}>
        <div style={containerStyle}>
          <div style={heroContentStyle}>
            <h1 style={titleStyle}>{t('home.heroTitle')}</h1>
            <p style={descriptionStyle}>
              {t('home.heroDescription')}
            </p>
            <div style={buttonContainerStyle}>
              <Link href="/search" style={{ textDecoration: 'none' }}>
                <button style={primaryButtonStyle}>{t('home.startShoppingButton')}</button>
              </Link>
              <button style={secondaryButtonStyle}>{t('home.learnMoreButton')}</button>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div style={featuresStyle}>
        <div style={containerStyle}>
          <h2 style={sectionTitleStyle}>{t('home.whyChooseUs')}</h2>
          <div style={featuresGridStyle}>
            <div style={featureCardStyle}>
              <div style={featureIconStyle}>🔍</div>
              <h3 style={featureHeadingStyle}>{t('home.features.smartSearch.title')}</h3>
              <p style={featureTextStyle}>{t('home.features.smartSearch.description')}</p>
            </div>
            <div style={featureCardStyle}>
              <div style={featureIconStyle}>💰</div>
              <h3 style={featureHeadingStyle}>{t('home.features.bestDeals.title')}</h3>
              <p style={featureTextStyle}>{t('home.features.bestDeals.description')}</p>
            </div>
            <div style={featureCardStyle}>
              <div style={featureIconStyle}>⚡</div>
              <h3 style={featureHeadingStyle}>{t('home.features.fastShopping.title')}</h3>
              <p style={featureTextStyle}>{t('home.features.fastShopping.description')}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Styles
const aliexpressOrange = '#ff4747';
const aliexpressLightOrange = '#ff6e6e';
const aliexpressDarkOrange = '#e53935';

const containerStyle = {
  maxWidth: '1200px',
  margin: '0 auto',
  padding: '0 20px',
};

const heroStyle = {
  backgroundColor: '#f8f8f8',
  padding: '60px 0',
  borderBottom: '1px solid #eee',
};

const heroContentStyle = {
  maxWidth: '800px',
  margin: '0 auto',
  textAlign: 'center',
};

const titleStyle = {
  fontSize: '2.5rem',
  marginBottom: '20px',
  color: '#333',
  fontWeight: 'bold',
};

const descriptionStyle = {
  fontSize: '1.2rem',
  lineHeight: '1.6',
  color: '#666',
  marginBottom: '30px',
};

const buttonContainerStyle = {
  display: 'flex',
  justifyContent: 'center',
  gap: '15px',
  flexWrap: 'wrap',
};

const primaryButtonStyle = {
  padding: '12px 24px',
  backgroundColor: aliexpressOrange,
  color: 'white',
  border: 'none',
  borderRadius: '4px',
  fontSize: '1rem',
  fontWeight: 'bold',
  cursor: 'pointer',
  transition: 'background-color 0.3s',
};

const secondaryButtonStyle = {
  padding: '12px 24px',
  backgroundColor: 'white',
  color: aliexpressOrange,
  border: `1px solid ${aliexpressOrange}`,
  borderRadius: '4px',
  fontSize: '1rem',
  fontWeight: 'bold',
  cursor: 'pointer',
  transition: 'all 0.3s',
};

const featuresStyle = {
  padding: '60px 0',
};

const sectionTitleStyle = {
  fontSize: '2rem',
  textAlign: 'center',
  marginBottom: '40px',
  color: '#333',
};

const featuresGridStyle = {
  display: 'flex',
  justifyContent: 'center',
  gap: '30px',
  flexWrap: 'wrap',
};

const featureCardStyle = {
  backgroundColor: 'white',
  borderRadius: '8px',
  padding: '30px',
  boxShadow: '0 4px 15px rgba(0, 0, 0, 0.05)',
  textAlign: 'center',
  flex: '1 1 300px',
  maxWidth: '350px',
  transition: 'transform 0.3s',
};

const featureIconStyle = {
  fontSize: '3rem',
  marginBottom: '15px',
};

const featureHeadingStyle = {
  fontSize: '1.5rem',
  marginBottom: '15px',
  color: aliexpressOrange,
};

const featureTextStyle = {
  fontSize: '1rem',
  color: '#666',
  lineHeight: '1.5',
};