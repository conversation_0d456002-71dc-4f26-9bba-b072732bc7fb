'use client';

import Link from 'next/link';
import { useTranslation } from './components/TranslationProvider';

export default function Home() {
  const { t } = useTranslation();
  
  return (
    <div>
      {/* Hero Section */}
      <div style={heroStyle}>
        <div style={containerStyle}>
          <div style={heroContentStyle}>
            <h1 style={titleStyle}>{t('home.heroTitle')}</h1>
            <p style={descriptionStyle}>
              {t('home.heroDescription')}
            </p>
            <div style={buttonContainerStyle}>
              <Link href="/search" style={{ textDecoration: 'none' }}>
                <button style={primaryButtonStyle}>{t('home.startShoppingButton')}</button>
              </Link>
              <button style={secondaryButtonStyle}>{t('home.learnMoreButton')}</button>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div style={featuresStyle}>
        <div style={containerStyle}>
          <h2 style={sectionTitleStyle}>{t('home.whyChooseUs')}</h2>
          <div style={featuresGridStyle}>
            <div style={featureCardStyle}>
              <div style={featureIconStyle}>🔍</div>
              <h3 style={featureHeadingStyle}>{t('home.features.smartSearch.title')}</h3>
              <p style={featureTextStyle}>{t('home.features.smartSearch.description')}</p>
            </div>
            <div style={featureCardStyle}>
              <div style={featureIconStyle}>💰</div>
              <h3 style={featureHeadingStyle}>{t('home.features.bestDeals.title')}</h3>
              <p style={featureTextStyle}>{t('home.features.bestDeals.description')}</p>
            </div>
            <div style={featureCardStyle}>
              <div style={featureIconStyle}>⚡</div>
              <h3 style={featureHeadingStyle}>{t('home.features.fastShopping.title')}</h3>
              <p style={featureTextStyle}>{t('home.features.fastShopping.description')}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Styles
const aliexpressOrange = '#ff4747';
const aliexpressLightOrange = '#ff6e6e';
const aliexpressDarkOrange = '#e53935';

const containerStyle = {
  maxWidth: '1200px',
  margin: '0 auto',
  padding: '0 20px',
};

const heroStyle = {
  background: 'linear-gradient(135deg, #4A90E2 0%, #FF8C42 100%)',
  padding: '80px 0',
  position: 'relative',
  overflow: 'hidden',
};

const heroContentStyle = {
  maxWidth: '800px',
  margin: '0 auto',
  textAlign: 'center',
};

const titleStyle = {
  fontSize: '3.5rem',
  marginBottom: '25px',
  color: 'white',
  fontWeight: 'bold',
  textShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
  lineHeight: '1.2',
};

const descriptionStyle = {
  fontSize: '1.4rem',
  lineHeight: '1.8',
  color: 'rgba(255, 255, 255, 0.9)',
  marginBottom: '40px',
  textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
};

const buttonContainerStyle = {
  display: 'flex',
  justifyContent: 'center',
  gap: '15px',
  flexWrap: 'wrap',
};

const primaryButtonStyle = {
  padding: '16px 32px',
  background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1))',
  color: 'white',
  border: '2px solid rgba(255, 255, 255, 0.3)',
  borderRadius: '30px',
  fontSize: '1.1rem',
  fontWeight: '600',
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  backdropFilter: 'blur(10px)',
  textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)',
};

const secondaryButtonStyle = {
  padding: '16px 32px',
  background: 'rgba(255, 255, 255, 0.9)',
  color: '#4A90E2',
  border: '2px solid rgba(255, 255, 255, 0.5)',
  borderRadius: '30px',
  fontSize: '1.1rem',
  fontWeight: '600',
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  backdropFilter: 'blur(10px)',
};

const featuresStyle = {
  padding: '60px 0',
};

const sectionTitleStyle = {
  fontSize: '2rem',
  textAlign: 'center',
  marginBottom: '40px',
  color: '#333',
};

const featuresGridStyle = {
  display: 'flex',
  justifyContent: 'center',
  gap: '30px',
  flexWrap: 'wrap',
};

const featureCardStyle = {
  backgroundColor: 'white',
  borderRadius: '8px',
  padding: '30px',
  boxShadow: '0 4px 15px rgba(0, 0, 0, 0.05)',
  textAlign: 'center',
  flex: '1 1 300px',
  maxWidth: '350px',
  transition: 'transform 0.3s',
};

const featureIconStyle = {
  fontSize: '3rem',
  marginBottom: '15px',
};

const featureHeadingStyle = {
  fontSize: '1.5rem',
  marginBottom: '15px',
  color: aliexpressOrange,
};

const featureTextStyle = {
  fontSize: '1rem',
  color: '#666',
  lineHeight: '1.5',
};