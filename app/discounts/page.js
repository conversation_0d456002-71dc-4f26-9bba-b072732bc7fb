'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function DiscountsPage() {
  const router = useRouter();
  
  useEffect(() => {
    // تحويل المستخدم إلى الصفحة الجديدة
    router.push('/commission-discounts');
  }, [router]);
  return (
    <div style={loadingStyle}>
      <p>جاري التحويل إلى صفحة منتجات التخفيض بالعمولة...</p>
    </div>
  );
}

// نمط صفحة التحميل
const loadingStyle = {
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  height: '100vh',
  fontSize: '1.2rem',
  color: '#666',
};