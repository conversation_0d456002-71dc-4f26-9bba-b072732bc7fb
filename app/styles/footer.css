/* أنماط الفوتر */
.site-footer {
  background-color: #f8f9fa;
  padding: 3rem 0 1.5rem;
  margin-top: 3rem;
  border-top: 1px solid #e9ecef;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.footer-top {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.footer-section {
  flex: 1;
  min-width: 250px;
  margin-bottom: 1.5rem;
  padding-right: 2rem;
}

.footer-heading {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: #333;
  font-weight: 600;
}

.footer-text {
  color: #666;
  line-height: 1.6;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: #666;
  text-decoration: none;
  transition: color 0.2s;
}

.footer-links a:hover {
  color: #007bff;
}

.footer-contact {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-contact li {
  margin-bottom: 0.5rem;
}

.footer-contact a {
  color: #666;
  text-decoration: none;
  transition: color 0.2s;
}

.footer-contact a:hover {
  color: #007bff;
}

.social-icons {
  display: flex;
  margin-top: 1rem;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #e9ecef;
  color: #666;
  margin-right: 0.5rem;
  transition: all 0.2s;
}

.social-icon:hover {
  background-color: #007bff;
  color: white;
}

.footer-bottom {
  border-top: 1px solid #e9ecef;
  padding-top: 1.5rem;
  text-align: center;
}

.copyright {
  color: #666;
  font-size: 0.9rem;
}

/* تخصيص للغة العربية */
[dir="rtl"] .footer-section {
  padding-right: 0;
  padding-left: 2rem;
}

[dir="rtl"] .social-icon {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* أنماط الصفحات */
.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
}

.page-content {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.page-title {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: #333;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 1rem;
}

/* أنماط صفحة الأسئلة الشائعة */
.faq-item {
  margin-bottom: 2rem;
}

.faq-question {
  font-size: 1.3rem;
  color: #333;
  margin-bottom: 1rem;
  font-weight: 600;
}

.faq-answer {
  color: #555;
  line-height: 1.6;
}

.faq-answer ul {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

[dir="rtl"] .faq-answer ul {
  padding-left: 0;
  padding-right: 1.5rem;
}

/* أنماط صفحة سياسة الخصوصية والشروط */
.policy-section, .terms-section {
  margin-bottom: 2rem;
}

.policy-section h2, .terms-section h2 {
  font-size: 1.3rem;
  color: #333;
  margin-bottom: 1rem;
  font-weight: 600;
}

.policy-section ul, .terms-section ul {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

[dir="rtl"] .policy-section ul, [dir="rtl"] .terms-section ul {
  padding-left: 0;
  padding-right: 1.5rem;
}

.policy-section p, .terms-section p {
  color: #555;
  line-height: 1.6;
  margin-bottom: 1rem;
}