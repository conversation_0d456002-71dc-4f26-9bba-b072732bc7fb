"use client";

import { useState, useEffect, useCallback } from "react";
import "./search.css";
import { useTranslation } from "../components/TranslationProvider";

// إنشاء رابط المنتج مع tag ID للتسويق بالعمولة
const createAffiliateLink = (productId) => {
  // الحصول على tag ID من متغيرات البيئة أو من التخزين المحلي
  // في الإنتاج، يجب الحصول على tag ID من لوحة الإدارة أو من متغيرات البيئة
  const tagId = localStorage.getItem("aliexpress_tag_id") || "default_tag_id";

  // إنشاء رابط المنتج مع tag ID
  return `https://www.aliexpress.com/item/${productId}.html?aff_fcid=${tagId}`;
};

export default function SearchPage() {
  const { t, language, direction } = useTranslation();
  // حالة البحث والتصفية
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState("grid"); // 'grid' or 'list'
  const [sortBy, setSortBy] = useState(""); // 'price', 'sales', 'rating'
  const [searchResults, setSearchResults] = useState([]); // نتائج البحث
  const [isLoading, setIsLoading] = useState(false); // حالة التحميل

  // جلب منتجات عشوائية من Redis
  const fetchRandomProductsFromCache = async () => {
    try {
      const response = await fetch("/api/random-products?count=24");

      if (!response.ok) {
        throw new Error(`خطأ في الخادم: ${response.status}`);
      }

      const data = await response.json();
      return data.products || [];
    } catch (error) {
      console.error("خطأ في جلب المنتجات العشوائية:", error);
      return [];
    }
  };

  // طلب API من نقطة النهاية الآمنة في الخادم
  const fetchProductsFromAPI = async (query, currency = "USD") => {
    try {
      // استخدام نقطة نهاية داخلية بدلاً من الاتصال المباشر بـ API علي إكسبرس
      const response = await fetch(
        `/api/search?q=${encodeURIComponent(query)}&currency=${currency}`,
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `خطأ في الخادم: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("خطأ في جلب نتائج البحث:", error);
      throw error;
    }
  };

  // إضافة حالة لرسائل الخطأ
  const [searchError, setSearchError] = useState(null);

  // إضافة حالة للعملة
  const [currency, setCurrency] = useState("USD");

  // وظيفة البحث (تعتمد على التخزين المؤقت في الخادم)
  const searchProducts = useCallback(
    async (query) => {
      // إعادة تعيين حالة الخطأ في كل مرة يتم فيها البحث
      setSearchError(null);

      if (!query.trim()) {
        // جلب منتجات عشوائية من Redis بدلاً من البيانات الوهمية
        setIsLoading(true);
        try {
          const randomProducts = await fetchRandomProductsFromCache();
          setSearchResults(randomProducts);
        } catch (error) {
          console.error("خطأ في جلب المنتجات العشوائية:", error);
          setSearchResults([]);
        }
        setIsLoading(false);
        return;
      }

      setIsLoading(true);

      try {
        // إرسال طلب API (التخزين المؤقت يتم في الخادم باستخدام Redis)
        console.log(`[API] إرسال طلب بحث لـ "${query}" بعملة ${currency}`);
        const results = await fetchProductsFromAPI(query, currency);

        setSearchResults(results);
        setIsLoading(false);
      } catch (error) {
        // تسجيل الخطأ داخليًا فقط (للمطورين)
        console.error("خطأ في البحث (للمطورين فقط):", error);

        setIsLoading(false);

        // عرض رسالة مناسبة للمستخدم
        setSearchError(
          "نواجه مشكلة فنية مؤقتة في خدمة البحث. يرجى المحاولة لاحقًا.",
        );

        // عرض قائمة فارغة
        setSearchResults([]);
      }
    },
    [currency],
  );

  // تم إزالة البحث التلقائي عند تغيير استعلام البحث
  // الآن سيتم البحث فقط عند النقر على زر البحث

  // تحميل tag ID من لوحة الإدارة
  const loadTagId = useCallback(async () => {
    try {
      // في الإنتاج، يجب الحصول على tag ID من لوحة الإدارة أو من API
      // هنا نستخدم قيمة افتراضية للتجربة
      const response = await fetch("/api/settings/affiliate");

      if (response.ok) {
        const data = await response.json();
        if (data.tagId) {
          localStorage.setItem("aliexpress_tag_id", data.tagId);
          console.log(`[Settings] تم تحميل tag ID: ${data.tagId}`);
        }
      }
    } catch (error) {
      console.error("خطأ في تحميل إعدادات التسويق بالعمولة:", error);
      // استخدام قيمة افتراضية في حالة الخطأ
      localStorage.setItem("aliexpress_tag_id", "default_tag_id");
    }
  }, []);

  // تهيئة البيانات عند تحميل الصفحة
  useEffect(() => {
    // جلب منتجات عشوائية من Redis عند تحميل الصفحة بدون تفعيل حالة التحميل
    const loadInitialProducts = async () => {
      try {
        const randomProducts = await fetchRandomProductsFromCache();
        setSearchResults(randomProducts);
      } catch (error) {
        console.error("خطأ في تحميل المنتجات الأولية:", error);
        setSearchResults([]);
      }
    };

    loadInitialProducts();
    loadTagId(); // تحميل tag ID عند تحميل الصفحة
  }, [loadTagId]);

  return (
    <div style={containerStyle}>
      <h1 style={titleStyle}>{t("search.title")}</h1>

      {/* شريط البحث */}
      <form
        onSubmit={(e) => {
          e.preventDefault();
          searchProducts(searchQuery);
        }}
        style={searchBarContainerStyle}
      >
        <input
          type="text"
          placeholder={t("search.placeholder")}
          style={searchInputStyle}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          disabled={isLoading}
        />
        <select
          value={currency}
          onChange={(e) => setCurrency(e.target.value)}
          style={currencySelectStyle}
          disabled={isLoading}
        >
          <option value="USD">$ USD</option>
          <option value="EUR">€ EUR</option>
          <option value="SAR">ر.س SAR</option>
          <option value="UAH">₴ UAH</option>
          <option value="EGP">ج.م EGP</option>
          <option value="GBP">£ GBP</option>
        </select>
        <button type="submit" style={searchButtonStyle} disabled={isLoading}>
          {isLoading ? (
            <span style={{ marginLeft: "5px" }}>⏳</span>
          ) : (
            <span style={{ marginLeft: "5px" }}>🔍</span>
          )}
          {isLoading ? t("search.searching") : t("search.button")}
        </button>
      </form>

      {/* أزرار التصفية وطريقة العرض */}
      <div style={filtersContainerStyle}>
        <div style={filterGroupStyle}>
          <button
            className={`filter-button ${sortBy === "price" ? "active" : ""}`}
            onClick={() => setSortBy(sortBy === "price" ? "" : "price")}
          >
            {t("search.filters.bestPrice")}
          </button>
          <button
            className={`filter-button ${sortBy === "sales" ? "active" : ""}`}
            onClick={() => setSortBy(sortBy === "sales" ? "" : "sales")}
          >
            {t("search.filters.bestSelling")}
          </button>
          <button
            className={`filter-button ${sortBy === "rating" ? "active" : ""}`}
            onClick={() => setSortBy(sortBy === "rating" ? "" : "rating")}
          >
            {t("search.filters.bestRated")}
          </button>
        </div>
        <div style={viewToggleStyle}>
          <button
            className={`view-button ${viewMode !== "grid" ? "inactive" : ""}`}
            onClick={() => setViewMode("grid")}
          >
            <span style={viewIconStyle}>▦</span>{" "}
            {t("search.filters.viewModes.grid")}
          </button>
          <button
            className={`view-button ${viewMode !== "list" ? "inactive" : ""}`}
            onClick={() => setViewMode("list")}
          >
            <span style={viewIconStyle}>☰</span>{" "}
            {t("search.filters.viewModes.list")}
          </button>
        </div>
      </div>

      {/* عرض رسالة الخطأ إذا كانت موجودة */}
      {searchError && (
        <div
          style={{
            padding: "15px",
            margin: "15px 0",
            backgroundColor: "#fff8f8",
            border: "1px solid #ffcdd2",
            borderRadius: "4px",
            color: "#d32f2f",
            textAlign: "center",
            fontSize: "16px",
          }}
        >
          <p>{t("search.error")}</p>
        </div>
      )}

      {/* عرض المنتجات (شبكة أو قائمة) */}
      <div style={viewMode === "grid" ? productsGridStyle : productsListStyle}>
        {isLoading && searchResults.length === 0 && (
          <div style={loadingContainerStyle}>
            <div style={loadingSpinnerStyle} className="loading-spinner"></div>
            <p>{t("search.loading")}</p>
          </div>
        )}

        {!isLoading &&
          !searchError &&
          searchResults.length === 0 &&
          searchQuery.trim() !== "" && (
            <div style={noResultsStyle}>
              <p>
                {t("search.noResults")} "{searchQuery}"
              </p>
              <p>{t("search.tryDifferent")}</p>
            </div>
          )}

        {!isLoading &&
          !searchError &&
          searchResults.length === 0 &&
          searchQuery.trim() === "" && (
            <div style={welcomeMessageStyle}>
              <div style={welcomeIconStyle}>🛍️</div>
              <h2 style={welcomeTitleStyle}>{t("search.welcome.title")}</h2>
              <p style={welcomeTextStyle}>
                {t("search.welcome.text")}
              </p>
              <p style={welcomeSubTextStyle}>
                {t("search.welcome.tip")}
              </p>
            </div>
          )}

        {searchResults
          .sort((a, b) => {
            if (sortBy === "price") return a.price - b.price;
            if (sortBy === "sales") return b.sales - a.sales;
            if (sortBy === "rating") return b.rating - a.rating;
            return 0;
          })
          .map((product) => {
            // إنشاء رابط المنتج مع tag ID
            const affiliateLink = createAffiliateLink(product.id);

            return (
              <a
                key={product.id}
                href={affiliateLink}
                target="_blank"
                rel="noopener noreferrer"
                style={{ textDecoration: "none", color: "inherit" }}
              >
                <div
                  style={
                    viewMode === "grid"
                      ? productCardStyle
                      : productListItemStyle
                  }
                >
                  <div
                    style={
                      viewMode === "grid"
                        ? productImageContainerStyle
                        : productListImageStyle
                    }
                  >
                    <img
                      src={product.image}
                      alt={product.title}
                      style={productImageStyle}
                    />
                  </div>
                  <div
                    style={
                      viewMode === "grid"
                        ? productInfoStyle
                        : productListInfoStyle
                    }
                  >
                    <h3 style={productTitleStyle}>{product.title}</h3>

                    {/* السعر والخصم */}
                    <div style={productPriceContainerStyle}>
                      <span style={productPriceStyle}>{product.price} ر.س</span>
                      {product.price < product.oldPrice && (
                        <span style={productOldPriceStyle}>
                          {product.oldPrice} ر.س
                        </span>
                      )}
                    </div>

                    {/* التقييم والمراجعات */}
                    <div style={productRatingContainerStyle}>
                      <span style={productRatingStyle}>
                        ⭐ {product.rating}
                      </span>
                      <span style={productReviewsStyle}>
                        {product.reviews} {t("search.product.reviews")}
                      </span>
                    </div>

                    {/* المبيعات والشحن */}
                    <div style={productMetaStyle}>
                      <span style={productSalesStyle}>
                        {t("search.product.sales")} {product.sales}+
                      </span>
                      {product.freeShipping && (
                        <span style={freeShippingStyle}>
                          {t("search.product.freeShipping")}
                        </span>
                      )}
                      {product.score && (
                        <span style={scoreStyle}>Score: {product.score}</span>
                      )}
                    </div>

                    {/* زر تسوق الآن */}
                    <button style={shopNowButtonStyle}>
                      {t("search.product.shopNow")}
                    </button>

                    {/* نسبة الخصم */}
                    {product.price < product.oldPrice && (
                      <div style={discountBadgeStyle}>-{product.discount}%</div>
                    )}
                  </div>
                </div>
              </a>
            );
          })}
      </div>

      {/* عدد النتائج */}
      <div style={resultsCountStyle}>
        {searchResults.length > 0 && (
          <>
            {searchResults.length} {t("search.product.viewDetails")}
          </>
        )}
        {/* لا نعرض مؤشر التخزين المؤقت لأن ذلك يتم في الخادم */}
      </div>
    </div>
  );
}

// الألوان
const aliexpressOrange = "#ff4747";
const aliexpressLightOrange = "#ff6e6e";
const aliexpressDarkOrange = "#e53935";

// الأنماط المحسنة بالألوان الجديدة
const containerStyle = {
  maxWidth: "1200px",
  margin: "0 auto",
  padding: "40px 20px",
  background: "linear-gradient(135deg, rgba(227, 242, 253, 0.3) 0%, rgba(255, 229, 212, 0.3) 100%)",
  borderRadius: "20px",
  minHeight: "100vh",
};

const titleStyle = {
  fontSize: "3rem",
  marginBottom: "30px",
  background: "linear-gradient(135deg, #4A90E2, #FF8C42)",
  WebkitBackgroundClip: "text",
  WebkitTextFillColor: "transparent",
  backgroundClip: "text",
  textAlign: "center",
  fontWeight: "bold",
};

// أنماط شريط البحث المحسنة
const searchBarContainerStyle = {
  display: "flex",
  marginBottom: "30px",
  boxShadow: "0 8px 32px rgba(74, 144, 226, 0.15)",
  borderRadius: "25px",
  overflow: "hidden",
  maxWidth: "700px",
  margin: "0 auto 30px",
  background: "linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.9))",
  border: "2px solid rgba(74, 144, 226, 0.1)",
  backdropFilter: "blur(10px)",
};

const searchInputStyle = {
  flex: 1,
  padding: "10px 16px",
  fontSize: "0.95rem",
  border: "none",
  outline: "none",
};

const currencySelectStyle = {
  padding: "0 10px",
  fontSize: "0.9rem",
  border: "none",
  borderLeft: "1px solid #eee",
  borderRight: "1px solid #eee",
  backgroundColor: "#ffffff",
  outline: "none",
  cursor: "pointer",
  position: "relative",
  zIndex: 10,
  appearance: "menulist", // Ensures the dropdown arrow appears
  WebkitAppearance: "menulist", // For Safari/Chrome
  MozAppearance: "menulist", // For Firefox
  color: "#333",
  height: "100%",
};

const searchButtonStyle = {
  backgroundColor: aliexpressOrange,
  color: "white",
  border: "none",
  padding: "0 20px",
  fontSize: "0.95rem",
  cursor: "pointer",
  transition: "background-color 0.3s",
};

// أنماط أزرار التصفية وطريقة العرض
const filtersContainerStyle = {
  display: "flex",
  justifyContent: "space-between",
  flexWrap: "wrap",
  gap: "15px",
  maxWidth: "1000px",
  margin: "0 auto 25px",
};

const filterGroupStyle = {
  display: "flex",
  gap: "8px",
  flexWrap: "wrap",
};

const filterButtonStyle = {
  backgroundColor: "white",
  color: "#333",
  border: "1px solid #ddd",
  borderRadius: "20px",
  padding: "6px 14px",
  fontSize: "0.85rem",
  cursor: "pointer",
  transition: "all 0.2s",
  boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
};

const filterButtonActiveStyle = {
  backgroundColor: aliexpressOrange,
  color: "white",
  borderColor: aliexpressOrange,
  boxShadow: "0 2px 5px rgba(255, 71, 71, 0.3)",
};

const viewToggleStyle = {
  display: "flex",
  gap: "5px",
};

const viewButtonStyle = {
  display: "flex",
  alignItems: "center",
  gap: "5px",
  backgroundColor: aliexpressOrange,
  color: "white",
  border: "none",
  borderRadius: "4px",
  padding: "6px 10px",
  fontSize: "0.85rem",
  cursor: "pointer",
  transition: "all 0.2s",
  boxShadow: "0 2px 5px rgba(255, 71, 71, 0.3)",
};

const viewButtonInactiveStyle = {
  backgroundColor: "white",
  color: "#333",
  border: "1px solid #ddd",
  boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
};

const viewIconStyle = {
  fontSize: "1rem",
};

// أنماط عرض المنتجات
const productsGridStyle = {
  display: "grid",
  gridTemplateColumns: "repeat(auto-fill, minmax(250px, 1fr))",
  gap: "20px",
  marginBottom: "20px",
};

const productsListStyle = {
  display: "flex",
  flexDirection: "column",
  gap: "15px",
  marginBottom: "20px",
};

const productCardStyle = {
  background: "linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95))",
  borderRadius: "20px",
  overflow: "hidden",
  boxShadow: "0 8px 32px rgba(74, 144, 226, 0.12)",
  transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
  position: "relative",
  border: "1px solid rgba(74, 144, 226, 0.08)",
  backdropFilter: "blur(10px)",
};

const productImageContainerStyle = {
  height: "200px",
  overflow: "hidden",
};

const productImageStyle = {
  width: "100%",
  height: "100%",
  objectFit: "cover",
  transition: "transform 0.3s",
};

const productInfoStyle = {
  padding: "15px",
};

// أنماط عنصر المنتج (عرض القائمة)
const productListItemStyle = {
  display: "flex",
  backgroundColor: "white",
  borderRadius: "8px",
  overflow: "hidden",
  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
  transition: "transform 0.3s, box-shadow 0.3s",
  position: "relative",
};

const productListImageStyle = {
  width: "150px",
  height: "150px",
  overflow: "hidden",
  flexShrink: 0,
};

const productListInfoStyle = {
  padding: "15px",
  display: "flex",
  flexDirection: "column",
  justifyContent: "space-between",
  flex: 1,
};

const productTitleStyle = {
  fontSize: "1rem",
  fontWeight: "normal",
  marginBottom: "8px",
  color: "#333",
  height: "40px",
  overflow: "hidden",
  textOverflow: "ellipsis",
  display: "-webkit-box",
  WebkitLineClamp: 2,
  WebkitBoxOrient: "vertical",
};

const productPriceContainerStyle = {
  display: "flex",
  alignItems: "center",
  gap: "8px",
  marginBottom: "6px",
};

const productPriceStyle = {
  fontSize: "1.1rem",
  fontWeight: "bold",
  color: aliexpressOrange,
};

const productOldPriceStyle = {
  fontSize: "0.85rem",
  color: "#999",
  textDecoration: "line-through",
};

const productRatingContainerStyle = {
  display: "flex",
  alignItems: "center",
  gap: "8px",
  marginBottom: "6px",
};

const productRatingStyle = {
  display: "flex",
  alignItems: "center",
  gap: "4px",
  fontSize: "0.8rem",
  color: "#FF9800",
};

const productReviewsStyle = {
  fontSize: "0.8rem",
  color: "#666",
};

const productMetaStyle = {
  display: "flex",
  justifyContent: "space-between",
  flexWrap: "wrap",
  gap: "5px",
  fontSize: "0.8rem",
  color: "#666",
  marginTop: "4px",
};

const productSalesStyle = {
  fontSize: "0.8rem",
  color: "#666",
};

const freeShippingStyle = {
  fontSize: "0.8rem",
  color: "#4CAF50",
  fontWeight: "bold",
};

const scoreStyle = {
  backgroundColor: "#fff3e0",
  color: "#e65100",
  padding: "2px 6px",
  borderRadius: "4px",
  fontSize: "0.7rem",
  fontWeight: "bold",
};

const discountBadgeStyle = {
  position: "absolute",
  top: "10px",
  right: "10px",
  backgroundColor: aliexpressOrange,
  color: "white",
  padding: "3px 6px",
  borderRadius: "4px",
  fontSize: "0.75rem",
  fontWeight: "bold",
};

// نمط عدد النتائج
const resultsCountStyle = {
  textAlign: "center",
  color: "#666",
  marginTop: "10px",
  fontSize: "0.9rem",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  gap: "5px",
};

// نمط مؤشر التخزين المؤقت
const cacheIndicatorStyle = {
  fontSize: "0.8rem",
  color: "#4CAF50",
  fontStyle: "italic",
};

// نمط زر تسوق الآن
const shopNowButtonStyle = {
  backgroundColor: aliexpressOrange,
  color: "white",
  border: "none",
  borderRadius: "4px",
  padding: "8px 15px",
  fontSize: "0.9rem",
  fontWeight: "bold",
  cursor: "pointer",
  marginTop: "10px",
  width: "100%",
  transition: "background-color 0.3s",
  textAlign: "center",
};

// أنماط التحميل
const loadingContainerStyle = {
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  padding: "40px 0",
  gridColumn: "1 / -1",
  color: "#666",
};

const loadingSpinnerStyle = {
  width: "40px",
  height: "40px",
  border: "4px solid rgba(255, 71, 71, 0.1)",
  borderRadius: "50%",
  borderTop: `4px solid ${aliexpressOrange}`,
  animation: "spin 1s linear infinite",
  marginBottom: "15px",
  "@keyframes spin": {
    "0%": { transform: "rotate(0deg)" },
    "100%": { transform: "rotate(360deg)" },
  },
};

// نمط لا توجد نتائج
const noResultsStyle = {
  textAlign: "center",
  padding: "40px 0",
  gridColumn: "1 / -1",
  color: "#666",
};

// أنماط رسالة الترحيب
const welcomeMessageStyle = {
  textAlign: "center",
  padding: "60px 20px",
  gridColumn: "1 / -1",
  color: "#666",
  maxWidth: "500px",
  margin: "0 auto",
};

const welcomeIconStyle = {
  fontSize: "4rem",
  marginBottom: "20px",
};

const welcomeTitleStyle = {
  fontSize: "1.5rem",
  color: "#333",
  marginBottom: "15px",
  fontWeight: "600",
};

const welcomeTextStyle = {
  fontSize: "1.1rem",
  marginBottom: "10px",
  color: "#555",
};

const welcomeSubTextStyle = {
  fontSize: "0.95rem",
  color: "#888",
  fontStyle: "italic",
};
