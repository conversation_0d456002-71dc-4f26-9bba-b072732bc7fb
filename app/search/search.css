@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

/* تحسين أزرار التصفية */
.filter-button {
  background-color: white;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 20px;
  padding: 6px 14px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.filter-button:hover {
  background-color: #f5f5f5;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.filter-button.active {
  background-color: #ff4747;
  color: white;
  border-color: #ff4747;
  box-shadow: 0 2px 5px rgba(255, 71, 71, 0.3);
}

/* تحسين أزرار طريقة العرض */
.view-button {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: #ff4747;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 5px rgba(255, 71, 71, 0.3);
}

.view-button:hover {
  background-color: #e53935;
}

.view-button.inactive {
  background-color: white;
  color: #333;
  border: 1px solid #ddd;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.view-button.inactive:hover {
  background-color: #f5f5f5;
}

/* تحسين قائمة العملات المنسدلة */
select {
  appearance: menulist !important;
  -webkit-appearance: menulist !important;
  -moz-appearance: menulist !important;
  background-color: #ffffff !important;
  color: #333 !important;
}

select option {
  background-color: #ffffff;
  color: #333;
  padding: 8px;
  font-size: 0.9rem;
}

select:focus {
  box-shadow: 0 0 0 2px rgba(255, 71, 71, 0.3);
}