:root {
  /* نظام الألوان الجديد - أزرق سماوي وبرتقالي */
  --primary-orange: #FF8C42;
  --primary-blue: #4A90E2;
  --light-orange: #FFB366;
  --light-blue: #87CEEB;
  --dark-orange: #E67E22;
  --dark-blue: #2E86AB;

  /* ألوان مساعدة */
  --accent-orange: #FFA366;
  --accent-blue: #6BB6FF;
  --soft-orange: #FFE5D4;
  --soft-blue: #E3F2FD;

  /* ألوان النص والخلفية */
  --text-color: #2C3E50;
  --text-light: #5A6C7D;
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8FAFC;
  --bg-gradient: linear-gradient(135deg, var(--soft-blue) 0%, var(--soft-orange) 100%);

  /* ألوان الحدود والظلال */
  --border-color: #E1E8ED;
  --shadow-light: 0 2px 8px rgba(74, 144, 226, 0.1);
  --shadow-medium: 0 4px 16px rgba(74, 144, 226, 0.15);
  --shadow-heavy: 0 8px 32px rgba(74, 144, 226, 0.2);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  color: var(--text-color);
  background: var(--bg-secondary);
  line-height: 1.6;
}

a {
  color: inherit;
  text-decoration: none;
}

a:hover {
  color: var(--primary-orange);
  transition: color 0.3s ease;
}

button {
  font-family: inherit;
}

/* Dropdown hover effect */
header div[style*="position: relative"]:hover > div[style*="display: none"] {
  display: block !important;
}

/* تأثيرات التمرير المحسنة */
button[style*="dropdownToggleStyle"]:hover {
  background: linear-gradient(135deg, var(--soft-blue), var(--soft-orange)) !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
}

a[style*="dropdownItemStyle"]:hover {
  background: linear-gradient(90deg, var(--soft-blue), var(--soft-orange)) !important;
  color: var(--primary-orange) !important;
  transform: translateX(5px);
  transition: all 0.3s ease;
}

a[style*="navLinkStyle"]:hover {
  color: var(--primary-orange) !important;
  background: var(--soft-blue) !important;
  border-radius: 8px;
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
}

button[style*="langButtonStyle"]:hover {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-orange)) !important;
  color: white !important;
  transform: scale(1.05);
  transition: all 0.3s ease;
}

/* RTL/LTR Support */
html[dir="rtl"] button[style*="dropdownToggleStyle"] {
  flex-direction: row-reverse;
}

html[dir="rtl"] div[style*="dropdownMenuStyle"] {
  right: 0;
  left: auto;
}

html[dir="ltr"] div[style*="dropdownMenuStyle"] {
  left: 0;
  right: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  header div[style*="headerContentStyle"] {
    flex-wrap: wrap;
  }
  
  header nav[style*="navStyle"] {
    order: 3;
    width: 100%;
    margin-top: 10px;
  }
}

/* تأثيرات بطاقات المنتجات المحسنة */
div[style*="productCardStyle"]:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-heavy);
  border: 2px solid var(--accent-blue);
  background: linear-gradient(145deg, var(--bg-primary), var(--soft-blue));
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

div[style*="productCardStyle"]:hover img[style*="productImageStyle"] {
  transform: scale(1.08);
  filter: brightness(1.1) saturate(1.2);
  transition: all 0.4s ease;
}

/* تأثيرات بطاقات الفئات */
div[style*="categoryCard"]:hover {
  transform: translateY(-10px) rotate(1deg);
  box-shadow: var(--shadow-heavy);
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-orange));
  color: white;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تأثيرات أزرار التصفية المحسنة */
button[style*="filterButtonStyle"]:hover {
  background: linear-gradient(135deg, var(--primary-orange), var(--primary-blue)) !important;
  color: white !important;
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--shadow-medium);
  border-radius: 25px;
  border-color: var(--primary-blue);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تأثيرات الأزرار العامة */
button {
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
}

/* تأثيرات الإدخال */
input, textarea, select {
  border-radius: 8px;
  border: 2px solid var(--border-color);
  transition: all 0.3s ease;
  padding: 12px 16px;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
  transform: scale(1.02);
}

/* تأثيرات التحميل الجميلة */
@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.loading-shimmer {
  background: linear-gradient(90deg, var(--soft-blue) 25%, var(--soft-orange) 50%, var(--soft-blue) 75%);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

/* تصميم الفوتر المحسن - متصل من الطرف إلى الطرف */
.site-footer {
  background: linear-gradient(135deg, #2C3E50 0%, #4A90E2 50%, #FF8C42 100%);
  color: white;
  padding: 80px 0 40px;
  margin-top: 0;
  position: relative;
  overflow: hidden;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
}

.site-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.06) 0%, transparent 50%);
  pointer-events: none;
  animation: footerGlow 8s ease-in-out infinite alternate;
}

@keyframes footerGlow {
  0% { opacity: 0.5; }
  100% { opacity: 1; }
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.footer-top {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section {
  animation: fadeInUp 0.6s ease-out;
}

.footer-heading {
  font-size: 1.6rem;
  font-weight: bold;
  margin-bottom: 25px;
  color: white;
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
  position: relative;
  letter-spacing: 0.5px;
}

.footer-heading::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, var(--light-orange), var(--light-blue));
  border-radius: 2px;
}

.footer-text {
  line-height: 1.9;
  color: rgba(255, 255, 255, 0.95);
  font-size: 1.1rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  font-weight: 400;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a, .footer-contact li {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-block;
  position: relative;
  padding: 8px 0;
  font-size: 1.05rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-weight: 400;
}

.footer-links a:hover {
  color: white;
  transform: translateX(8px);
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
  font-weight: 500;
}

.footer-links a::before {
  content: '→';
  position: absolute;
  left: -20px;
  opacity: 0;
  transition: all 0.3s ease;
}

.footer-links a:hover::before {
  opacity: 1;
  left: -15px;
}

.footer-contact {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-contact li {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.footer-contact a {
  color: rgba(255, 255, 255, 0.9) !important;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1.05rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.footer-contact a:hover {
  color: white !important;
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
}

/* تصميم أيقونة الفيسبوك */
.social-icons {
  display: flex;
  gap: 15px;
  margin-top: 15px;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  color: white !important;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.social-icon:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
  transform: translateY(-3px) scale(1.1);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.social-icon svg {
  fill: white !important;
  color: white !important;
  width: 20px;
  height: 20px;
}

/* تأكد من أن جميع النصوص في الفوتر بيضاء */
.site-footer * {
  color: white !important;
}

.site-footer .footer-heading {
  color: white !important;
}

.site-footer .footer-text {
  color: rgba(255, 255, 255, 0.95) !important;
}

.site-footer .footer-links a {
  color: rgba(255, 255, 255, 0.9) !important;
}

.site-footer .footer-contact a {
  color: rgba(255, 255, 255, 0.9) !important;
}

.site-footer .footer-bottom p {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* تحسين عرض الفوتر على جميع الشاشات */
.site-footer {
  box-sizing: border-box;
  min-width: 100vw;
}

/* إصلاح مشكلة العرض في RTL */
html[dir="rtl"] .site-footer {
  width: 100vw !important;
  margin-left: calc(-50vw + 50%) !important;
  margin-right: 0 !important;
  left: 0;
  right: 0;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 30px;
  text-align: center;
}

.footer-bottom p {
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  font-size: 1.1rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  font-weight: 400;
  letter-spacing: 0.3px;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تأثيرات hover للأزرار والعناصر التفاعلية */
button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
}

/* تأثيرات hover للبطاقات */
.product-card:hover, .category-card:hover, .offer-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 15px 40px rgba(74, 144, 226, 0.2);
}

/* تأثيرات hover للروابط */
a:hover {
  color: var(--primary-blue);
  transition: all 0.3s ease;
}

/* تأثيرات hover للقوائم المنسدلة */
.dropdown-menu:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(74, 144, 226, 0.25);
}

/* تأثيرات hover لأزرار التصفية */
.filter-button:hover {
  background: linear-gradient(135deg, var(--primary-orange), var(--primary-blue)) !important;
  color: white !important;
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 20px rgba(74, 144, 226, 0.3);
}

/* تأثيرات hover لشريط البحث */
input:hover {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

/* تأثيرات hover للأيقونات */
.icon:hover {
  transform: rotate(10deg) scale(1.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تأثيرات hover للصور */
img:hover {
  transform: scale(1.05);
  transition: all 0.4s ease;
}

/* تأثيرات hover للنصوص التفاعلية */
.interactive-text:hover {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-orange));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transform: scale(1.02);
}

/* تأثيرات الحركة المتقدمة */
@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

@keyframes slideInFromLeft {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInFromRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes fadeInScale {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* فئات مساعدة للحركات */
.animate-pulse { animation: pulse 2s infinite; }
.animate-bounce { animation: bounce 1s infinite; }
.animate-slide-left { animation: slideInFromLeft 0.6s ease-out; }
.animate-slide-right { animation: slideInFromRight 0.6s ease-out; }
.animate-fade-scale { animation: fadeInScale 0.5s ease-out; }

/* تحسينات إضافية للتصميم */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-orange));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-soft {
  box-shadow: 0 8px 32px rgba(74, 144, 226, 0.15);
}

.shadow-medium {
  box-shadow: 0 12px 40px rgba(74, 144, 226, 0.2);
}

.shadow-strong {
  box-shadow: 0 20px 60px rgba(74, 144, 226, 0.3);
}

/* تحسينات خاصة للغة العربية RTL */
[dir="rtl"] .site-footer {
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: 0;
}

[dir="rtl"] .footer-links a::before {
  content: '←';
  right: -20px;
  left: auto;
}

[dir="rtl"] .footer-links a:hover::before {
  right: -15px;
  left: auto;
}

[dir="rtl"] .footer-links a:hover {
  transform: translateX(-8px);
}

[dir="rtl"] .footer-heading::after {
  right: 0;
  left: auto;
}

/* تحسينات للاستجابة */
@media (max-width: 768px) {
  .hero-title { font-size: 2.5rem !important; }
  .section-title { font-size: 1.8rem !important; }
  .container { padding: 0 15px !important; }

  button:hover {
    transform: translateY(-2px) scale(1.02);
  }

  .product-card:hover, .category-card:hover, .offer-card:hover {
    transform: translateY(-4px) scale(1.01);
  }

  .site-footer {
    padding: 60px 0 30px;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
  }

  [dir="rtl"] .site-footer {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    margin-right: 0;
  }

  .footer-heading {
    font-size: 1.4rem;
  }

  .footer-text, .footer-links a, .footer-contact li {
    font-size: 1rem;
  }

  .footer-bottom p {
    font-size: 1rem;
  }
}

/* تحسينات إضافية للألوان والتدرجات */
.gradient-bg-primary {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-orange));
}

.gradient-bg-soft {
  background: linear-gradient(135deg, var(--soft-blue), var(--soft-orange));
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-orange));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* تأثيرات خاصة للعناصر التفاعلية */
.interactive-element {
  position: relative;
  overflow: hidden;
}

.interactive-element::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.interactive-element:hover::before {
  left: 100%;
}

/* تحسينات للنصوص */
.enhanced-text {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Search button hover effect */
button[style*="searchButtonStyle"]:hover {
  background-color: var(--aliexpress-dark-orange);
}

/* View toggle buttons hover effects */
button[style*="viewButtonStyle"]:not([style*="viewButtonInactiveStyle"]):hover {
  background-color: var(--aliexpress-dark-orange);
}

button[style*="viewButtonInactiveStyle"]:hover {
  border-color: var(--aliexpress-orange);
  color: var(--aliexpress-orange);
}