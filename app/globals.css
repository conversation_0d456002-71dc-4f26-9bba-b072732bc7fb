:root {
  --aliexpress-orange: #ff4747;
  --aliexpress-light-orange: #ff6e6e;
  --aliexpress-dark-orange: #e53935;
  --text-color: #333;
  --light-bg: #f9f9f9;
  --border-color: #eee;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  color: var(--text-color);
}

a {
  color: inherit;
  text-decoration: none;
}

a:hover {
  color: var(--aliexpress-orange);
}

button {
  font-family: inherit;
}

/* Dropdown hover effect */
header div[style*="position: relative"]:hover > div[style*="display: none"] {
  display: block !important;
}

/* Hover effects */
button[style*="dropdownToggleStyle"]:hover {
  background-color: var(--light-bg) !important;
}

a[style*="dropdownItemStyle"]:hover {
  background-color: var(--light-bg) !important;
  color: var(--aliexpress-orange) !important;
}

a[style*="navLinkStyle"]:hover {
  color: var(--aliexpress-orange) !important;
  background-color: var(--light-bg) !important;
}

button[style*="langButtonStyle"]:hover {
  background-color: var(--aliexpress-dark-orange) !important;
}

/* RTL/LTR Support */
html[dir="rtl"] button[style*="dropdownToggleStyle"] {
  flex-direction: row-reverse;
}

html[dir="rtl"] div[style*="dropdownMenuStyle"] {
  right: 0;
  left: auto;
}

html[dir="ltr"] div[style*="dropdownMenuStyle"] {
  left: 0;
  right: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  header div[style*="headerContentStyle"] {
    flex-wrap: wrap;
  }
  
  header nav[style*="navStyle"] {
    order: 3;
    width: 100%;
    margin-top: 10px;
  }
}

/* Product card hover effects */
div[style*="productCardStyle"]:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

div[style*="productCardStyle"]:hover img[style*="productImageStyle"] {
  transform: scale(1.05);
}

/* Filter buttons hover effects */
button[style*="filterButtonStyle"]:hover {
  background-color: var(--aliexpress-orange);
  color: white;
  border-color: var(--aliexpress-orange);
}

/* Search button hover effect */
button[style*="searchButtonStyle"]:hover {
  background-color: var(--aliexpress-dark-orange);
}

/* View toggle buttons hover effects */
button[style*="viewButtonStyle"]:not([style*="viewButtonInactiveStyle"]):hover {
  background-color: var(--aliexpress-dark-orange);
}

button[style*="viewButtonInactiveStyle"]:hover {
  border-color: var(--aliexpress-orange);
  color: var(--aliexpress-orange);
}