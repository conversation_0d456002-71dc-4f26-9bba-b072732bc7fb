:root {
  /* نظام الألوان الجديد - أزرق سماوي وبرتقالي */
  --primary-orange: #FF8C42;
  --primary-blue: #4A90E2;
  --light-orange: #FFB366;
  --light-blue: #87CEEB;
  --dark-orange: #E67E22;
  --dark-blue: #2E86AB;

  /* ألوان مساعدة */
  --accent-orange: #FFA366;
  --accent-blue: #6BB6FF;
  --soft-orange: #FFE5D4;
  --soft-blue: #E3F2FD;

  /* ألوان النص والخلفية */
  --text-color: #2C3E50;
  --text-light: #5A6C7D;
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8FAFC;
  --bg-gradient: linear-gradient(135deg, var(--soft-blue) 0%, var(--soft-orange) 100%);

  /* ألوان الحدود والظلال */
  --border-color: #E1E8ED;
  --shadow-light: 0 2px 8px rgba(74, 144, 226, 0.1);
  --shadow-medium: 0 4px 16px rgba(74, 144, 226, 0.15);
  --shadow-heavy: 0 8px 32px rgba(74, 144, 226, 0.2);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  color: var(--text-color);
  background: var(--bg-secondary);
  line-height: 1.6;
}

a {
  color: inherit;
  text-decoration: none;
}

a:hover {
  color: var(--primary-orange);
  transition: color 0.3s ease;
}

button {
  font-family: inherit;
}

/* Dropdown hover effect */
header div[style*="position: relative"]:hover > div[style*="display: none"] {
  display: block !important;
}

/* تأثيرات التمرير المحسنة */
button[style*="dropdownToggleStyle"]:hover {
  background: linear-gradient(135deg, var(--soft-blue), var(--soft-orange)) !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
}

a[style*="dropdownItemStyle"]:hover {
  background: linear-gradient(90deg, var(--soft-blue), var(--soft-orange)) !important;
  color: var(--primary-orange) !important;
  transform: translateX(5px);
  transition: all 0.3s ease;
}

a[style*="navLinkStyle"]:hover {
  color: var(--primary-orange) !important;
  background: var(--soft-blue) !important;
  border-radius: 8px;
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
}

button[style*="langButtonStyle"]:hover {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-orange)) !important;
  color: white !important;
  transform: scale(1.05);
  transition: all 0.3s ease;
}

/* RTL/LTR Support */
html[dir="rtl"] button[style*="dropdownToggleStyle"] {
  flex-direction: row-reverse;
}

html[dir="rtl"] div[style*="dropdownMenuStyle"] {
  right: 0;
  left: auto;
}

html[dir="ltr"] div[style*="dropdownMenuStyle"] {
  left: 0;
  right: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  header div[style*="headerContentStyle"] {
    flex-wrap: wrap;
  }
  
  header nav[style*="navStyle"] {
    order: 3;
    width: 100%;
    margin-top: 10px;
  }
}

/* تأثيرات بطاقات المنتجات المحسنة */
div[style*="productCardStyle"]:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-heavy);
  border: 2px solid var(--accent-blue);
  background: linear-gradient(145deg, var(--bg-primary), var(--soft-blue));
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

div[style*="productCardStyle"]:hover img[style*="productImageStyle"] {
  transform: scale(1.08);
  filter: brightness(1.1) saturate(1.2);
  transition: all 0.4s ease;
}

/* تأثيرات بطاقات الفئات */
div[style*="categoryCard"]:hover {
  transform: translateY(-10px) rotate(1deg);
  box-shadow: var(--shadow-heavy);
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-orange));
  color: white;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تأثيرات أزرار التصفية المحسنة */
button[style*="filterButtonStyle"]:hover {
  background: linear-gradient(135deg, var(--primary-orange), var(--primary-blue)) !important;
  color: white !important;
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--shadow-medium);
  border-radius: 25px;
  border-color: var(--primary-blue);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تأثيرات الأزرار العامة */
button {
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
}

/* تأثيرات الإدخال */
input, textarea, select {
  border-radius: 8px;
  border: 2px solid var(--border-color);
  transition: all 0.3s ease;
  padding: 12px 16px;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
  transform: scale(1.02);
}

/* تأثيرات التحميل الجميلة */
@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.loading-shimmer {
  background: linear-gradient(90deg, var(--soft-blue) 25%, var(--soft-orange) 50%, var(--soft-blue) 75%);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

/* Search button hover effect */
button[style*="searchButtonStyle"]:hover {
  background-color: var(--aliexpress-dark-orange);
}

/* View toggle buttons hover effects */
button[style*="viewButtonStyle"]:not([style*="viewButtonInactiveStyle"]):hover {
  background-color: var(--aliexpress-dark-orange);
}

button[style*="viewButtonInactiveStyle"]:hover {
  border-color: var(--aliexpress-orange);
  color: var(--aliexpress-orange);
}