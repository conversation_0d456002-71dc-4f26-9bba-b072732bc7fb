'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from '../components/TranslationProvider';

/**
 * دالة لتحسين الصور باستخدام خدمة تحسين الصور الداخلية
 * @param {string} imageUrl - رابط الصورة الأصلية
 * @param {number} width - عرض الصورة المطلوب (اختياري)
 * @param {number} height - ارتفاع الصورة المطلوب (اختياري)
 * @param {number} quality - جودة الصورة (اختياري)
 * @returns {string} - رابط الصورة المحسنة
 */
function getOptimizedImageUrl(imageUrl, width = 300, height = 300, quality = 80) {
  if (!imageUrl) return '';
  
  // إذا كانت الصورة من خدمة picsum، نستخدمها مباشرة مع تحديد الأبعاد
  if (imageUrl.includes('picsum.photos')) {
    const urlParts = imageUrl.split('/');
    const seedIndex = urlParts.findIndex(part => part === 'seed');
    
    if (seedIndex !== -1 && seedIndex + 1 < urlParts.length) {
      const seed = urlParts[seedIndex + 1];
      return `https://picsum.photos/seed/${seed}/${width}/${height}`;
    } else {
      return `https://picsum.photos/${width}/${height}`;
    }
  }
  
  // استخدام خدمة تحسين الصور الداخلية
  return `/api/image-optimizer?url=${encodeURIComponent(imageUrl)}&width=${width}&height=${height}&quality=${quality}&format=webp`;
}

export default function OffersPage() {
  // استخدام hook الترجمة
  const { t, language, direction } = useTranslation();
  
  // حالة لتتبع البطاقة التي يحوم عليها المؤشر
  const [hoveredCard, setHoveredCard] = useState(null);
  // حالة لتخزين العروض
  const [offers, setOffers] = useState(null);
  // حالة لتخزين العملة
  const [currency, setCurrency] = useState('USD');
  // حالة التحميل
  const [loading, setLoading] = useState(true);
  // حالة الخطأ
  const [error, setError] = useState(null);

  // بيانات الفئات المعروفة - متوافقة مع تصنيفات الخوارزمية
  const categories = [
    {
      id: 'top_rated',
      name: 'الأعلى تقييماً',
      icon: '⭐',
      color: '#3498db',
      image: 'https://picsum.photos/seed/top_rated/300/200',
    },
    {
      id: 'best_sellers',
      name: 'الأكثر مبيعاً',
      icon: '🔥',
      color: '#e74c3c',
      image: 'https://picsum.photos/seed/best_sellers/300/200',
    },
    {
      id: 'discounted',
      name: 'خصومات كبيرة',
      icon: '💰',
      color: '#2ecc71',
      image: 'https://picsum.photos/seed/discounted/300/200',
    },
    {
      id: 'new_arrivals',
      name: 'وصل حديثاً',
      icon: '🆕',
      color: '#9b59b6',
      image: 'https://picsum.photos/seed/new_arrivals/300/200',
    },
    {
      id: 'premium',
      name: 'منتجات مميزة',
      icon: '👑',
      color: '#f39c12',
      image: 'https://picsum.photos/seed/premium/300/200',
    },
    {
      id: 'electronics',
      name: 'الإلكترونيات',
      icon: '📱',
      color: '#1abc9c',
      image: 'https://picsum.photos/seed/electronics/300/200',
    },
    {
      id: 'fashion',
      name: 'الأزياء والموضة',
      icon: '👕',
      color: '#d35400',
      image: 'https://picsum.photos/seed/fashion/300/200',
    },
    {
      id: 'home',
      name: 'المنزل والحديقة',
      icon: '🏠',
      color: '#7f8c8d',
      image: 'https://picsum.photos/seed/home/<USER>/200',
    },
  ];

  // جلب العروض من نقطة النهاية الآمنة في الخادم
  useEffect(() => {
    async function loadOffers() {
      try {
        setLoading(true);
        setError(null);
        
        // جلب اللغة والعملة من localStorage إذا كانت متوفرة
        const storedLanguage = localStorage.getItem('language') || 'ar';
        const storedCurrency = localStorage.getItem('currency') || 'USD';

        setCurrency(storedCurrency);
        
        // استدعاء نقطة النهاية الآمنة في الخادم
        const response = await fetch(`/api/offers?lang=${storedLanguage}&currency=${storedCurrency}`);
        
        if (!response.ok) {
          throw new Error(`خطأ في الخادم: ${response.status}`);
        }
        
        const offersData = await response.json();
        
        // التحقق من وجود رسالة خطأ في الاستجابة
        if (offersData.error) {
          setError(offersData.error);
          setOffers({
            top_rated: [],
            best_sellers: [],
            discounted: [],
            new_arrivals: [],
            premium: []
          });
        } else {
          setOffers(offersData);
        }
        
        setLoading(false);
      } catch (err) {
        // تسجيل الخطأ داخليًا فقط (للمطورين)
        console.error('خطأ في جلب العروض (للمطورين فقط):', err);
        
        // عرض رسالة مناسبة للمستخدم
        setError('نواجه مشكلة فنية مؤقتة في خدمة العروض. يرجى المحاولة لاحقًا.');
        
        // تعيين قائمة فارغة للعروض
        setOffers({
          top_rated: [],
          best_sellers: [],
          discounted: [],
          new_arrivals: [],
          premium: []
        });
        
        setLoading(false);
      }
    }
    
    loadOffers();
  }, []);
  
  // مستمع لتغييرات اللغة والعملة
  useEffect(() => {
    // دالة لمعالجة تغييرات اللغة
    function handleLanguageChange(event) {
      const newLanguage = event.detail?.language || localStorage.getItem('language') || 'ar';

      // إعادة تحميل العروض إذا كانت هناك عروض مخزنة بالفعل
      if (offers) {
        fetch(`/api/offers?lang=${newLanguage}&currency=${currency}`)
          .then(response => response.json())
          .then(data => {
            if (!data.error) {
              setOffers(data);
            }
          })
          .catch(err => console.error('خطأ في تحديث اللغة:', err));
      }
    }
    
    // دالة لمعالجة تغييرات العملة
    function handleCurrencyChange(event) {
      const newCurrency = event.detail?.currency || localStorage.getItem('currency') || 'USD';
      setCurrency(newCurrency);
      
      // إعادة تحميل العروض إذا كانت هناك عروض مخزنة بالفعل
      if (offers) {
        fetch(`/api/offers?lang=${language}&currency=${newCurrency}`)
          .then(response => response.json())
          .then(data => {
            if (!data.error) {
              setOffers(data);
            }
          })
          .catch(err => console.error('خطأ في تحديث العملة:', err));
      }
    }
    
    // إضافة مستمعي الأحداث
    window.addEventListener('languageChange', handleLanguageChange);
    window.addEventListener('currencyChange', handleCurrencyChange);
    
    // إزالة مستمعي الأحداث عند تفكيك المكون
    return () => {
      window.removeEventListener('languageChange', handleLanguageChange);
      window.removeEventListener('currencyChange', handleCurrencyChange);
    };
  }, [language, currency, offers]);

  // عرض رسالة التحميل
  if (loading) {
    return (
      <div style={containerStyle}>
        <div style={loadingStyle}>جاري تحميل العروض...</div>
      </div>
    );
  }

  // عرض رسالة الخطأ
  if (error) {
    return (
      <div style={containerStyle}>
        <div style={errorStyle}>{error}</div>
      </div>
    );
  }

  return (
    <div style={containerStyle}>
      <h1 style={titleStyle}>العروض</h1>
      <p style={descriptionStyle}>
        تصفح العروض المميزة حسب الفئات
      </p>

      {/* عرض بطاقات الفئات */}
      <div style={categoriesContainerStyle}>
        {categories.map((category) => (
          <div 
            key={category.id} 
            style={{
              ...categoryCardStyle,
              transform: hoveredCard === category.id ? 'translateY(-5px)' : 'translateY(0)',
              boxShadow: hoveredCard === category.id 
                ? '0 8px 25px rgba(0, 0, 0, 0.15)' 
                : '0 4px 15px rgba(0, 0, 0, 0.1)',
            }}
            onMouseEnter={() => setHoveredCard(category.id)}
            onMouseLeave={() => setHoveredCard(null)}
          >
            <div style={categoryImageContainerStyle}>
              <img 
                src={category.image} 
                alt={category.name} 
                style={{
                  ...categoryImageStyle,
                  transform: hoveredCard === category.id ? 'scale(1.1)' : 'scale(1)',
                }} 
              />
              <div style={{...categoryOverlayStyle, backgroundColor: category.color + '80'}}></div>
            </div>
            <div style={categoryContentStyle}>
              <span style={categoryIconStyle}>{category.icon}</span>
              <h3 style={categoryNameStyle}>{category.name}</h3>
            </div>
          </div>
        ))}
      </div>

      {/* عرض العروض المميزة من الخوارزمية */}
      {offers && Object.keys(offers).length > 0 && (
        <div>
          {/* قسم الأعلى تقييماً */}
          {offers.top_rated && offers.top_rated.length > 0 && (
            <div style={offerSectionStyle}>
              <h2 style={sectionTitleStyle}>الأعلى تقييماً</h2>
              <div style={offersGridStyle}>
                {offers.top_rated.map(offer => (
                  <OfferCard key={offer.id} offer={offer} currency={currency} />
                ))}
              </div>
            </div>
          )}

          {/* قسم الأكثر مبيعاً */}
          {offers.best_sellers && offers.best_sellers.length > 0 && (
            <div style={offerSectionStyle}>
              <h2 style={sectionTitleStyle}>الأكثر مبيعاً</h2>
              <div style={offersGridStyle}>
                {offers.best_sellers.map(offer => (
                  <OfferCard key={offer.id} offer={offer} currency={currency} />
                ))}
              </div>
            </div>
          )}

          {/* قسم الخصومات الكبيرة */}
          {offers.discounted && offers.discounted.length > 0 && (
            <div style={offerSectionStyle}>
              <h2 style={sectionTitleStyle}>خصومات كبيرة</h2>
              <div style={offersGridStyle}>
                {offers.discounted.map(offer => (
                  <OfferCard key={offer.id} offer={offer} currency={currency} />
                ))}
              </div>
            </div>
          )}

          {/* قسم وصل حديثاً */}
          {offers.new_arrivals && offers.new_arrivals.length > 0 && (
            <div style={offerSectionStyle}>
              <h2 style={sectionTitleStyle}>وصل حديثاً</h2>
              <div style={offersGridStyle}>
                {offers.new_arrivals.map(offer => (
                  <OfferCard key={offer.id} offer={offer} currency={currency} />
                ))}
              </div>
            </div>
          )}

          {/* قسم المنتجات المميزة */}
          {offers.premium && offers.premium.length > 0 && (
            <div style={offerSectionStyle}>
              <h2 style={sectionTitleStyle}>منتجات مميزة</h2>
              <div style={offersGridStyle}>
                {offers.premium.map(offer => (
                  <OfferCard key={offer.id} offer={offer} currency={currency} />
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

// مكون بطاقة العرض
function OfferCard({ offer, currency }) {
  const [isHovered, setIsHovered] = useState(false);
  
  // تنسيق السعر حسب العملة
  const formatPrice = (price) => {
    return new Intl.NumberFormat('ar-EG', { 
      style: 'currency', 
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2 
    }).format(price);
  };
  
  // حساب السعر بعد الخصم
  const discountedPrice = offer.price * (1 - (offer.discountRate / 100));
  
  return (
    <div 
      style={{
        ...offerCardStyle,
        transform: isHovered ? 'translateY(-5px)' : 'translateY(0)',
        boxShadow: isHovered ? '0 10px 25px rgba(0, 0, 0, 0.15)' : '0 4px 15px rgba(0, 0, 0, 0.1)',
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* صورة المنتج */}
      <div style={offerImageContainerStyle}>
        <img 
          src={getOptimizedImageUrl(offer.imageUrl || 'https://picsum.photos/seed/' + offer.id + '/300/300')} 
          alt={offer.title} 
          style={{
            ...offerImageStyle,
            transform: isHovered ? 'scale(1.05)' : 'scale(1)',
          }} 
        />
        {/* شارة الخصم */}
        {offer.discountRate > 0 && (
          <div style={discountBadgeStyle}>
            {offer.discountRate}% خصم
          </div>
        )}
        {/* شارة جديد */}
        {offer.isNew && (
          <div style={{...newBadgeStyle}}>
            جديد
          </div>
        )}
      </div>
      
      {/* محتوى العرض */}
      <div style={offerContentStyle}>
        <h3 style={offerTitleStyle}>{offer.title}</h3>
        
        {/* التقييم */}
        <div style={ratingContainerStyle}>
          <span style={ratingStarStyle}>⭐</span>
          <span style={ratingTextStyle}>{offer.rating}</span>
          <span style={reviewCountStyle}>({offer.reviewCount} تقييم)</span>
        </div>
        
        {/* السعر */}
        <div style={priceContainerStyle}>
          <span style={currentPriceStyle}>{formatPrice(discountedPrice)}</span>
          {offer.discountRate > 0 && (
            <span style={oldPriceStyle}>{formatPrice(offer.price)}</span>
          )}
        </div>
        
        {/* معلومات البائع */}
        <div style={sellerInfoStyle}>
          <span style={sellerRankStyle}>
            {offer.sellerRank === 'Diamond' ? '💎' : 
             offer.sellerRank === 'Gold' ? '🥇' : '🏆'} 
            {offer.sellerRank === 'Diamond' ? 'بائع ماسي' : 
             offer.sellerRank === 'Gold' ? 'بائع ذهبي' : 'بائع مميز'}
          </span>
          <span style={soldCountStyle}>
            تم بيع {offer.sold}+
          </span>
        </div>
        
        {/* زر الشراء */}
        <a 
          href={offer.affiliateUrl} 
          target="_blank" 
          rel="noopener noreferrer" 
          style={{
            ...buyButtonStyle,
            backgroundColor: isHovered ? '#e74c3c' : '#ff6b6b',
          }}
        >
          تسوق الآن
        </a>
      </div>
    </div>
  );
}

// الأنماط
const containerStyle = {
  maxWidth: '1200px',
  margin: '0 auto',
  padding: '40px 20px',
};

const titleStyle = {
  fontSize: '2rem',
  marginBottom: '10px',
  color: '#ff4747',
  textAlign: 'center',
};

const descriptionStyle = {
  fontSize: '1.2rem',
  lineHeight: '1.6',
  color: '#666',
  textAlign: 'center',
  marginBottom: '40px',
};

// أنماط بطاقات الفئات
const categoriesContainerStyle = {
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))',
  gap: '20px',
  marginBottom: '50px',
};

const categoryCardStyle = {
  borderRadius: '10px',
  overflow: 'hidden',
  boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)',
  transition: 'transform 0.3s, box-shadow 0.3s',
  cursor: 'pointer',
  position: 'relative',
};

const categoryImageContainerStyle = {
  position: 'relative',
  height: '150px',
  overflow: 'hidden',
};

const categoryImageStyle = {
  width: '100%',
  height: '100%',
  objectFit: 'cover',
  transition: 'transform 0.5s',
};

const categoryOverlayStyle = {
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  opacity: 0.7,
};

const categoryContentStyle = {
  padding: '15px',
  display: 'flex',
  alignItems: 'center',
  gap: '10px',
  backgroundColor: 'white',
};

const categoryIconStyle = {
  fontSize: '1.8rem',
};

const categoryNameStyle = {
  margin: 0,
  fontSize: '1.1rem',
  fontWeight: '600',
  color: '#333',
};

// أنماط قسم العروض
const offerSectionStyle = {
  marginTop: '50px',
  marginBottom: '50px',
};

const sectionTitleStyle = {
  fontSize: '1.8rem',
  color: '#ff4747',
  marginBottom: '20px',
  textAlign: 'center',
};

const sectionDescriptionStyle = {
  fontSize: '1.1rem',
  color: '#666',
  maxWidth: '700px',
  margin: '0 auto 30px',
  textAlign: 'center',
};

// أنماط شبكة العروض
const offersGridStyle = {
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
  gap: '25px',
};

// أنماط بطاقة العرض
const offerCardStyle = {
  borderRadius: '10px',
  overflow: 'hidden',
  backgroundColor: 'white',
  boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)',
  transition: 'transform 0.3s, box-shadow 0.3s',
};

const offerImageContainerStyle = {
  position: 'relative',
  height: '200px',
  overflow: 'hidden',
};

const offerImageStyle = {
  width: '100%',
  height: '100%',
  objectFit: 'cover',
  transition: 'transform 0.5s',
};

const discountBadgeStyle = {
  position: 'absolute',
  top: '10px',
  right: '10px',
  backgroundColor: '#ff4747',
  color: 'white',
  padding: '5px 10px',
  borderRadius: '5px',
  fontWeight: 'bold',
  fontSize: '0.9rem',
};

const newBadgeStyle = {
  position: 'absolute',
  top: '10px',
  left: '10px',
  backgroundColor: '#3498db',
  color: 'white',
  padding: '5px 10px',
  borderRadius: '5px',
  fontWeight: 'bold',
  fontSize: '0.9rem',
};

const offerContentStyle = {
  padding: '15px',
};

const offerTitleStyle = {
  margin: '0 0 10px 0',
  fontSize: '1.1rem',
  fontWeight: '600',
  color: '#333',
  height: '40px',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  display: '-webkit-box',
  WebkitLineClamp: 2,
  WebkitBoxOrient: 'vertical',
};

const ratingContainerStyle = {
  display: 'flex',
  alignItems: 'center',
  marginBottom: '10px',
};

const ratingStarStyle = {
  color: '#f39c12',
  marginRight: '5px',
};

const ratingTextStyle = {
  fontWeight: 'bold',
  marginRight: '5px',
};

const reviewCountStyle = {
  color: '#777',
  fontSize: '0.9rem',
};

const priceContainerStyle = {
  display: 'flex',
  alignItems: 'center',
  gap: '10px',
  marginBottom: '15px',
};

const currentPriceStyle = {
  fontSize: '1.3rem',
  fontWeight: 'bold',
  color: '#ff4747',
};

const oldPriceStyle = {
  fontSize: '1rem',
  color: '#999',
  textDecoration: 'line-through',
};

const sellerInfoStyle = {
  display: 'flex',
  justifyContent: 'space-between',
  marginBottom: '15px',
  fontSize: '0.9rem',
};

const sellerRankStyle = {
  display: 'flex',
  alignItems: 'center',
  gap: '5px',
};

const soldCountStyle = {
  color: '#777',
};

const buyButtonStyle = {
  display: 'block',
  width: '100%',
  padding: '10px',
  backgroundColor: '#ff6b6b',
  color: 'white',
  textAlign: 'center',
  borderRadius: '5px',
  fontWeight: 'bold',
  textDecoration: 'none',
  transition: 'background-color 0.3s',
};

// أنماط التحميل والخطأ
const loadingStyle = {
  textAlign: 'center',
  padding: '50px',
  fontSize: '1.2rem',
  color: '#666',
};

const errorStyle = {
  textAlign: 'center',
  padding: '50px',
  fontSize: '1.2rem',
  color: '#e74c3c',
  backgroundColor: '#ffeaea',
  borderRadius: '10px',
};