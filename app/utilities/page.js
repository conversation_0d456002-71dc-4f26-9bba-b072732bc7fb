'use client';

import { useState } from 'react';
import OptimizedImage from '../components/OptimizedImage';
import QueueManager from '../components/QueueManager';

export default function UtilitiesPage() {
  // حالة رابط الصورة
  const [imageUrl, setImageUrl] = useState('https://picsum.photos/seed/demo/800/600');
  // حالة أبعاد الصورة
  const [imageWidth, setImageWidth] = useState(300);
  const [imageHeight, setImageHeight] = useState(300);
  // حالة جودة الصورة
  const [imageQuality, setImageQuality] = useState(80);
  // حالة تنسيق الصورة
  const [imageFormat, setImageFormat] = useState('webp');
  
  // حالة نتيجة معالجة الصورة
  const [imageProcessingResult, setImageProcessingResult] = useState(null);
  
  // معالجة اكتمال مهمة معالجة الصورة
  const handleImageProcessingComplete = (result) => {
    setImageProcessingResult(result);
  };
  
  // معالجة خطأ مهمة معالجة الصورة
  const handleImageProcessingError = (error) => {
    console.error('خطأ في معالجة الصورة:', error);
  };
  
  // مهمة معالجة الصورة
  const imageProcessingTask = {
    imageUrl,
    operations: ['resize', 'compress', 'format']
  };
  
  return (
    <div style={containerStyle}>
      <h1 style={titleStyle}>الأدوات المساعدة</h1>
      
      <div style={sectionStyle}>
        <h2 style={sectionTitleStyle}>نظام ضغط الصور</h2>
        <p style={descriptionStyle}>
          يقوم نظام ضغط الصور بتحسين الصور وتقليل حجمها لتحسين أداء الموقع وتقليل استهلاك البيانات.
        </p>
        
        <div style={formGroupStyle}>
          <label style={labelStyle}>رابط الصورة:</label>
          <input
            type="text"
            value={imageUrl}
            onChange={(e) => setImageUrl(e.target.value)}
            style={inputStyle}
            placeholder="أدخل رابط الصورة"
          />
        </div>
        
        <div style={formRowStyle}>
          <div style={formGroupStyle}>
            <label style={labelStyle}>العرض:</label>
            <input
              type="number"
              value={imageWidth}
              onChange={(e) => setImageWidth(parseInt(e.target.value, 10))}
              style={inputStyle}
              min="50"
              max="1000"
            />
          </div>
          
          <div style={formGroupStyle}>
            <label style={labelStyle}>الارتفاع:</label>
            <input
              type="number"
              value={imageHeight}
              onChange={(e) => setImageHeight(parseInt(e.target.value, 10))}
              style={inputStyle}
              min="50"
              max="1000"
            />
          </div>
        </div>
        
        <div style={formRowStyle}>
          <div style={formGroupStyle}>
            <label style={labelStyle}>الجودة:</label>
            <input
              type="range"
              value={imageQuality}
              onChange={(e) => setImageQuality(parseInt(e.target.value, 10))}
              style={rangeStyle}
              min="10"
              max="100"
              step="5"
            />
            <span style={valueStyle}>{imageQuality}%</span>
          </div>
          
          <div style={formGroupStyle}>
            <label style={labelStyle}>التنسيق:</label>
            <select
              value={imageFormat}
              onChange={(e) => setImageFormat(e.target.value)}
              style={selectStyle}
            >
              <option value="webp">WebP</option>
              <option value="jpeg">JPEG</option>
              <option value="png">PNG</option>
            </select>
          </div>
        </div>
        
        <div style={imagesContainerStyle}>
          <div style={imageBoxStyle}>
            <h3 style={imageBoxTitleStyle}>الصورة الأصلية</h3>
            <img
              src={imageUrl}
              alt="الصورة الأصلية"
              style={regularImageStyle}
            />
          </div>
          
          <div style={imageBoxStyle}>
            <h3 style={imageBoxTitleStyle}>الصورة المحسنة</h3>
            <OptimizedImage
              src={imageUrl}
              alt="الصورة المحسنة"
              width={imageWidth}
              height={imageHeight}
              quality={imageQuality}
              format={imageFormat}
            />
          </div>
        </div>
      </div>
      
      <div style={sectionStyle}>
        <h2 style={sectionTitleStyle}>نظام الطوابير</h2>
        <p style={descriptionStyle}>
          يقوم نظام الطوابير بإدارة المهام الثقيلة مثل معالجة الصور وطلبات API وتصدير البيانات.
        </p>
        
        <div style={queueContainerStyle}>
          <div style={queueBoxStyle}>
            <h3 style={queueBoxTitleStyle}>معالجة الصور</h3>
            <QueueManager
              queueName="image-processing"
              task={imageProcessingTask}
              onTaskComplete={handleImageProcessingComplete}
              onTaskError={handleImageProcessingError}
            />
            
            {imageProcessingResult && (
              <div style={resultBoxStyle}>
                <h4 style={resultTitleStyle}>نتيجة المعالجة:</h4>
                <pre style={resultCodeStyle}>
                  {JSON.stringify(imageProcessingResult, null, 2)}
                </pre>
              </div>
            )}
          </div>
          
          <div style={queueBoxStyle}>
            <h3 style={queueBoxTitleStyle}>طلبات API</h3>
            <QueueManager
              queueName="api-requests"
              task={{
                url: 'https://api.example.com/data',
                method: 'GET',
                body: null
              }}
            />
          </div>
          
          <div style={queueBoxStyle}>
            <h3 style={queueBoxTitleStyle}>تصدير البيانات</h3>
            <QueueManager
              queueName="data-export"
              task={{
                format: 'csv',
                filters: {
                  date: 'last-30-days',
                  category: 'electronics'
                }
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

// الأنماط
const containerStyle = {
  maxWidth: '1200px',
  margin: '0 auto',
  padding: '40px 20px',
};

const titleStyle = {
  fontSize: '2rem',
  marginBottom: '20px',
  color: '#ff4747',
  textAlign: 'center',
};

const sectionStyle = {
  marginBottom: '50px',
  backgroundColor: 'white',
  borderRadius: '8px',
  padding: '20px',
  boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
};

const sectionTitleStyle = {
  fontSize: '1.5rem',
  marginBottom: '15px',
  color: '#333',
  borderBottom: '1px solid #eee',
  paddingBottom: '10px',
};

const descriptionStyle = {
  fontSize: '1rem',
  lineHeight: '1.6',
  color: '#666',
  marginBottom: '20px',
};

const formGroupStyle = {
  marginBottom: '15px',
  flex: 1,
};

const formRowStyle = {
  display: 'flex',
  gap: '20px',
  marginBottom: '15px',
};

const labelStyle = {
  display: 'block',
  marginBottom: '5px',
  fontSize: '0.9rem',
  color: '#555',
};

const inputStyle = {
  width: '100%',
  padding: '8px 12px',
  fontSize: '0.9rem',
  border: '1px solid #ddd',
  borderRadius: '4px',
};

const rangeStyle = {
  width: '80%',
  marginRight: '10px',
};

const valueStyle = {
  fontSize: '0.9rem',
  color: '#666',
};

const selectStyle = {
  width: '100%',
  padding: '8px 12px',
  fontSize: '0.9rem',
  border: '1px solid #ddd',
  borderRadius: '4px',
};

const imagesContainerStyle = {
  display: 'flex',
  flexWrap: 'wrap',
  gap: '20px',
  marginTop: '20px',
};

const imageBoxStyle = {
  flex: '1 1 calc(50% - 10px)',
  minWidth: '300px',
  border: '1px solid #eee',
  borderRadius: '4px',
  padding: '15px',
  boxShadow: '0 1px 5px rgba(0, 0, 0, 0.05)',
};

const imageBoxTitleStyle = {
  fontSize: '1.1rem',
  marginBottom: '10px',
  color: '#333',
  textAlign: 'center',
};

const regularImageStyle = {
  width: '100%',
  height: '300px',
  objectFit: 'cover',
  borderRadius: '4px',
};

const queueContainerStyle = {
  display: 'flex',
  flexWrap: 'wrap',
  gap: '20px',
};

const queueBoxStyle = {
  flex: '1 1 calc(33.333% - 14px)',
  minWidth: '300px',
  border: '1px solid #eee',
  borderRadius: '4px',
  padding: '15px',
  boxShadow: '0 1px 5px rgba(0, 0, 0, 0.05)',
};

const queueBoxTitleStyle = {
  fontSize: '1.1rem',
  marginBottom: '10px',
  color: '#333',
  textAlign: 'center',
};

const resultBoxStyle = {
  marginTop: '20px',
  padding: '15px',
  backgroundColor: '#f9f9f9',
  borderRadius: '4px',
  border: '1px solid #eee',
};

const resultTitleStyle = {
  fontSize: '1rem',
  marginBottom: '10px',
  color: '#333',
};

const resultCodeStyle = {
  fontSize: '0.85rem',
  backgroundColor: '#f5f5f5',
  padding: '10px',
  borderRadius: '4px',
  overflow: 'auto',
  maxHeight: '200px',
  whiteSpace: 'pre-wrap',
  wordBreak: 'break-word',
};