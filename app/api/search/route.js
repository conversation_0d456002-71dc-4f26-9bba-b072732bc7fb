import { NextResponse } from 'next/server';
import { getCache, setCache } from '../../lib/redis';
import { getSiteSettings } from '../../lib/settings';

// وقت انتهاء الصلاحية (5 ساعات بالثواني)
const CACHE_EXPIRY = 5 * 60 * 60;

/**
 * جلب أفضل المنتجات من AliExpress بناءً على معايير متعددة
 * @param {string} query - كلمة البحث أو رابط المنتج
 * @param {string} currency - العملة المحددة من واجهة الموقع (افتراضي: USD)
 * @return {Array} - قائمة بأفضل المنتجات (حتى 30 منتج)
 */
async function fetchTopProducts(query, currency = 'USD') {
  // 1. جلب إعدادات الموقع من لوحة التحكم الإدارية
  const siteSettings = await getSiteSettings();
  const api_key = siteSettings?.ali_api_key || process.env.ALIEXPRESS_APP_KEY;
  const secret_key = siteSettings?.ali_secret_key || process.env.ALIEXPRESS_SECRET_KEY;
  const affiliate_tag = siteSettings?.ali_affiliate_tag || process.env.ALIEXPRESS_TAG_ID;
  
  if (!api_key || !secret_key || !affiliate_tag) {
    console.error('API keys not configured');
    throw new Error('API keys not configured');
  }

  // 2. تحديد لغة البحث تلقائياً من نص الاستعلام
  let language = 'en';
  let product_id = null;
  
  if (query.startsWith('http://') || query.startsWith('https://')) {
    // استخراج ID المنتج من الرابط
    const match = query.match(/\/item\/(\d+)\.html/);
    product_id = match ? match[1] : null;
    
    // تحديد لغة الرابط
    if (query.includes('/ar/')) {
      language = 'ar';
    } else if (query.includes('/es/')) {
      language = 'es';
    } else if (query.includes('/fr/')) {
      language = 'fr';
    } else if (query.includes('/ru/')) {
      language = 'ru';
    }
  } else {
    // اكتشاف لغة النص - في الإنتاج يمكن استخدام مكتبة langdetect
    // هنا نستخدم تقدير بسيط للغة
    const arabicPattern = /[\u0600-\u06FF]/;
    const russianPattern = /[\u0400-\u04FF]/;
    const spanishPattern = /[áéíóúüñ¿¡]/i;
    const frenchPattern = /[àâçéèêëîïôùûüÿœæ]/i;
    
    if (arabicPattern.test(query)) {
      language = 'ar';
    } else if (russianPattern.test(query)) {
      language = 'ru';
    } else if (spanishPattern.test(query)) {
      language = 'es';
    } else if (frenchPattern.test(query)) {
      language = 'fr';
    }
  }

  // 3. جلب البيانات من AliExpress API (100 منتج فقط)
  const endpoint = "https://api.aliexpress.com/items/search";
  const headers = { "Authorization": `Bearer ${api_key}:${secret_key}` };
  const params = {
    "currency": currency,
    "language": language,
    "page_size": 100,  // طلب 100 منتج فقط
    "affiliate_tag": affiliate_tag,
    "fields": "id,title,price,shipping_price,rating,reviews_count,sold_count,image_url"  // تحديد الحقول الأساسية
  };
  
  if (product_id) {
    params["item_id"] = product_id;
  } else {
    params["keywords"] = query;
  }
  
  // في بيئة التطوير، نستخدم بيانات وهمية
  if (process.env.NODE_ENV === 'development') {
    // محاكاة تأخير الشبكة
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // إنشاء بيانات وهمية أكثر للاختبار
    const dummyProducts = Array(100).fill().map((_, index) => ({
      id: index + 1,
      title: `منتج ${query} رقم ${index + 1}`,
      price: Math.floor(Math.random() * 1000) + 50,
      shipping_price: Math.floor(Math.random() * 50),
      rating: (Math.random() * 2 + 3).toFixed(1),
      reviews_count: Math.floor(Math.random() * 5000) + 5,
      sold_count: Math.floor(Math.random() * 10000) + 10,
      image_url: `https://picsum.photos/seed/${index + 1}/300/300`,
    }));
    
    // 4. تطبيق الفلاتر الأساسية
    const filtered_products = [];
    for (const product of dummyProducts) {
      // حساب السعر الإجمالي
      const price = parseFloat(product.price);
      const shipping_price = parseFloat(product.shipping_price);
      const total_price = price + shipping_price;
      
      // شروط الفلترة
      const rating = parseFloat(product.rating);
      const reviews_count = parseInt(product.reviews_count);
      
      if (rating < 3.5) continue;
      if (reviews_count < 10) continue;
      if (shipping_price > price) continue;
      
      // إضافة حقول جديدة للمنتج
      product.total_price = total_price;
      filtered_products.push(product);
    }
    
    // 5. حساب النقاط للمنتجات المتبقية
    if (filtered_products.length === 0) {
      return [];
    }
    
    // استخراج القيم القصوى
    const prices = filtered_products.map(p => p.total_price);
    const reviews = filtered_products.map(p => p.reviews_count);
    const sales = filtered_products.map(p => p.sold_count);
    
    const max_price = Math.max(...prices);
    const min_price = Math.min(...prices);
    const max_reviews = Math.max(...reviews);
    const max_sales = Math.max(...sales);
    
    // حساب النقاط
    for (const product of filtered_products) {
      // نقاط السعر (40%)
      let price_score;
      if (max_price !== min_price) {
        price_score = 1 - ((product.total_price - min_price) / (max_price - min_price));
      } else {
        price_score = 1;
      }
      price_score *= 40;
      
      // نقاط التقييم (20%)
      const rating_score = (parseFloat(product.rating) / 5) * 20;
      
      // نقاط المراجعات (20%)
      const reviews_score = (Math.min(parseFloat(product.reviews_count) / max_reviews, 1)) * 20;
      
      // نقاط المبيعات (20%)
      const sales_score = (Math.min(parseFloat(product.sold_count) / max_sales, 1)) * 20;
      
      // النقاط الإجمالية
      product.score = price_score + rating_score + reviews_score + sales_score;
    }
    
    // 6. فرز المنتجات حسب النقاط
    const sorted_products = filtered_products.sort((a, b) => b.score - a.score);
    
    // 7. تحويل البيانات إلى الشكل المطلوب للواجهة
    const results = sorted_products.slice(0, 30).map(product => ({
      id: product.id,
      title: product.title,
      price: product.price,
      oldPrice: product.price * (1 + Math.random() * 0.5),
      discount: Math.floor(Math.random() * 50) + 10,
      rating: product.rating,
      reviews: product.reviews_count,
      sales: product.sold_count,
      freeShipping: product.shipping_price < 5,
      image: product.image_url,
      score: product.score.toFixed(2)
    }));
    
    return results;
  } else {
    // في بيئة الإنتاج، نتصل بـ API علي إكسبرس
    try {
      const queryParams = new URLSearchParams(params);
      const url = `${endpoint}?${queryParams.toString()}`;
      
      const response = await fetch(url, { headers, timeout: 10000 });
      response.raise_for_status();
      
      // التحقق من حجم الاستجابة
      const contentLength = response.headers.get('content-length');
      if (contentLength && parseInt(contentLength) > 500000) {  // 500KB حد أقصى
        throw new Error("حجم استجابة API كبير جداً");
      }
      
      const data = await response.json();
      const products = data?.result?.items || [];
      
      // 4. تطبيق الفلاتر الأساسية
      const filtered_products = [];
      for (const product of products) {
        // حساب السعر الإجمالي
        const price = parseFloat(product.price || 0);
        const shipping_price = parseFloat(product.shipping_price || 0);
        const total_price = price + shipping_price;
        
        // شروط الفلترة
        const rating = parseFloat(product.rating || 0);
        const reviews_count = parseInt(product.reviews_count || 0);
        
        if (rating < 3.5) continue;
        if (reviews_count < 10) continue;
        if (shipping_price > price) continue;
        
        // إضافة حقول جديدة للمنتج
        product.total_price = total_price;
        filtered_products.push(product);
      }
      
      // 5. حساب النقاط للمنتجات المتبقية
      if (filtered_products.length === 0) {
        return [];
      }
      
      // استخراج القيم القصوى
      const prices = filtered_products.map(p => p.total_price);
      const reviews = filtered_products.map(p => p.reviews_count || 0);
      const sales = filtered_products.map(p => p.sold_count || 0);
      
      const max_price = Math.max(...prices);
      const min_price = Math.min(...prices);
      const max_reviews = Math.max(...reviews);
      const max_sales = Math.max(...sales);
      
      // حساب النقاط
      for (const product of filtered_products) {
        // نقاط السعر (40%)
        let price_score;
        if (max_price !== min_price) {
          price_score = 1 - ((product.total_price - min_price) / (max_price - min_price));
        } else {
          price_score = 1;
        }
        price_score *= 40;
        
        // نقاط التقييم (20%)
        const rating_score = (parseFloat(product.rating || 0) / 5) * 20;
        
        // نقاط المراجعات (20%)
        const reviews_score = (Math.min(parseFloat(product.reviews_count || 0) / max_reviews, 1)) * 20;
        
        // نقاط المبيعات (20%)
        const sales_score = (Math.min(parseFloat(product.sold_count || 0) / max_sales, 1)) * 20;
        
        // النقاط الإجمالية
        product.score = price_score + rating_score + reviews_score + sales_score;
      }
      
      // 6. فرز المنتجات حسب النقاط
      const sorted_products = filtered_products.sort((a, b) => b.score - a.score);
      
      // 7. تحويل البيانات إلى الشكل المطلوب للواجهة
      const results = sorted_products.slice(0, 30).map(product => ({
        id: product.id,
        title: product.title,
        price: product.price,
        oldPrice: product.original_price || product.price * 1.2,
        discount: product.discount_percentage || Math.floor((1 - (product.price / (product.original_price || product.price * 1.2))) * 100),
        rating: product.rating,
        reviews: product.reviews_count,
        sales: product.sold_count,
        freeShipping: product.shipping_price < 1,
        image: product.image_url,
        score: product.score.toFixed(2)
      }));
      
      return results;
    } catch (error) {
      console.error('AliExpress API error:', error);
      throw error;
    }
  }
}

/**
 * معالج طلبات البحث API
 * يقوم بالاتصال بـ API علي إكسبرس بشكل آمن دون كشف مفاتيح API للمتصفح
 * مع تخزين النتائج مؤقتاً في Redis لمدة 5 ساعات
 */
export async function GET(request) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('q');
  const currency = searchParams.get('currency') || 'USD';
  
  // التحقق من وجود استعلام البحث
  if (!query) {
    return NextResponse.json(
      { error: 'يجب توفير استعلام البحث' },
      { status: 400 }
    );
  }
  
  try {
    // إنشاء مفتاح للتخزين المؤقت باستخدام استعلام البحث والعملة
    const cacheKey = `search:${query.toLowerCase().trim()}:${currency}`;
    
    // التحقق من وجود نتائج مخزنة في Redis
    const cachedResults = await getCache(cacheKey);
    
    // إذا وجدت نتائج مخزنة، نعيدها مباشرة
    if (cachedResults) {
      console.log(`[Server API] استرجاع نتائج البحث المخزنة لـ "${query}"`);
      return NextResponse.json(cachedResults);
    }
    
    // إذا لم توجد نتائج مخزنة، نقوم بإرسال طلب جديد
    console.log(`[Server API] إرسال طلب بحث جديد لـ "${query}"`);
    
    // استدعاء دالة جلب أفضل المنتجات
    const results = await fetchTopProducts(query, currency);
    
    // تخزين النتائج في Redis لمدة 5 ساعات
    await setCache(cacheKey, results, CACHE_EXPIRY);
    console.log(`[Server API] تم تخزين نتائج البحث لـ "${query}" في Redis لمدة 5 ساعات`);
    
    return NextResponse.json(results);
  } catch (error) {
    // تسجيل الخطأ داخليًا فقط (لا يتم إرساله للمتصفح)
    console.error('Search API error:', error);
    
    // إعادة استجابة خطأ عامة (بدون تفاصيل تقنية)
    return NextResponse.json(
      { error: 'حدث خطأ أثناء معالجة طلبك، يرجى المحاولة لاحقًا' },
      { status: 500 }
    );
  }
}