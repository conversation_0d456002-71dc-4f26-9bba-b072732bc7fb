import { NextResponse } from 'next/server';
import { initDatabase } from '../../lib/db';

// إجبار المسار على العمل بشكل ديناميكي
export const dynamic = 'force-dynamic';

/**
 * نقطة نهاية API لتهيئة قاعدة البيانات
 * تقوم بإنشاء الجداول وإدخال البيانات الأساسية
 * يجب استدعاؤها مرة واحدة فقط عند بدء التشغيل
 */
export async function GET(request) {
  try {
    // التحقق من رمز الأمان (لمنع الوصول غير المصرح به)
    const { searchParams } = new URL(request.url);
    const secretKey = searchParams.get('secret');
    
    if (secretKey !== process.env.INIT_DB_SECRET_KEY) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // تهيئة قاعدة البيانات
    await initDatabase();
    
    return NextResponse.json({
      status: 'success',
      message: 'Database initialized successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Database initialization error:', error);
    
    return NextResponse.json({
      status: 'error',
      message: 'An error occurred during database initialization',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}