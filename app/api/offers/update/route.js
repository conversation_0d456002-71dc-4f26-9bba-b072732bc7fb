import { NextResponse } from 'next/server';
import { updateProducts } from '../../../services/offersService';

// إجبار المسار على العمل بشكل ديناميكي
export const dynamic = 'force-dynamic';

/**
 * نقطة نهاية API لتحديث المنتجات يدوياً
 * يمكن استخدامها من لوحة الإدارة أو من خلال مهمة مجدولة
 */
export async function GET(request) {
  try {
    // التحقق من المفتاح السري (للأمان)
    const { searchParams } = new URL(request.url);
    const secretKey = searchParams.get('secret');
    
    if (secretKey !== (process.env.UPDATE_SECRET_KEY || 'dev_secret_key')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    console.log('[Server API] بدء تحديث المنتجات يدوياً...');
    
    // تحديث المنتجات
    const updatedOffers = await updateProducts();
    
    if (!updatedOffers) {
      return NextResponse.json({ 
        success: false, 
        message: 'Failed to update products' 
      });
    }
    
    return NextResponse.json({ 
      success: true, 
      message: 'Products updated successfully',
      categories: Object.keys(updatedOffers),
      counts: Object.entries(updatedOffers).reduce((acc, [key, value]) => {
        acc[key] = value.length;
        return acc;
      }, {})
    });
  } catch (error) {
    console.error('[Server API] خطأ في تحديث المنتجات:', error);
    
    return NextResponse.json({ 
      success: false, 
      message: 'An error occurred while updating products' 
    }, { status: 500 });
  }
}