import { NextResponse } from 'next/server';
import { getProducts, updateProducts } from '../../services/offersService';

/**
 * معالج طلبات API للعروض
 * يقوم بجلب العروض المخزنة في Redis
 * إذا لم تكن هناك عروض مخزنة، يقوم بتحديثها
 */
export async function GET(request) {
  try {
    console.log(`[Server API] جلب العروض (50 منتج باللغة الإنجليزية والدولار)`);
    
    // الحصول على المنتجات (من التخزين المؤقت أو تحديثها)
    let offers = await getProducts();
    
    // إذا لم تكن هناك منتجات، نعيد قوائم فارغة (لا توجد عروض متاحة)
    if (!offers) {
      console.log('لم يتم العثور على منتجات مخزنة - مفاتيح API غير متوفرة');

      // إعادة قوائم فارغة بدلاً من البيانات الوهمية
      const emptyOffers = {
        top_rated: [],
        best_sellers: [],
        discounted: [],
        new_arrivals: [],
        premium: []
      };

      return NextResponse.json(emptyOffers);
    }
    
    // إعادة المنتجات المخزنة
    return NextResponse.json(offers);
  } catch (error) {
    // تسجيل الخطأ داخليًا فقط (لا يتم إرساله للمتصفح)
    console.error('Offers API error:', error);
    
    // إعادة استجابة خطأ عامة (بدون تفاصيل تقنية)
    return NextResponse.json({
      top_rated: [],
      best_sellers: [],
      discounted: [],
      new_arrivals: [],
      premium: [],
      error: 'An error occurred while processing your request, please try again later'
    }, { status: 500 });
  }
}

