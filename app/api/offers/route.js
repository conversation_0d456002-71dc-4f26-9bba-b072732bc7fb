import { NextResponse } from 'next/server';
import { getProducts, updateProducts } from '../../services/offersService';

/**
 * معالج طلبات API للعروض
 * يقوم بجلب العروض المخزنة في Redis
 * إذا لم تكن هناك عروض مخزنة، يقوم بتحديثها
 */
export async function GET(request) {
  try {
    console.log(`[Server API] جلب العروض (50 منتج باللغة الإنجليزية والدولار)`);
    
    // الحصول على المنتجات (من التخزين المؤقت أو تحديثها)
    let offers = await getProducts();
    
    // إذا لم تكن هناك منتجات، نستخدم البيانات الوهمية مؤقتًا ونبدأ تحديثًا في الخلفية
    if (!offers) {
      console.log('لم يتم العثور على منتجات مخزنة، جاري استخدام البيانات الوهمية وبدء التحديث...');
      
      // بدء تحديث المنتجات في الخلفية
      updateProducts().catch(err => console.error('خطأ في تحديث المنتجات:', err));
      
      // استخدام البيانات الوهمية مؤقتًا
      const dummyOffers = {
        top_rated: [],
        best_sellers: [],
        discounted: [],
        new_arrivals: [],
        premium: []
      };
      
      return NextResponse.json(dummyOffers);
    }
    
    // إعادة المنتجات المخزنة
    return NextResponse.json(offers);
  } catch (error) {
    // تسجيل الخطأ داخليًا فقط (لا يتم إرساله للمتصفح)
    console.error('Offers API error:', error);
    
    // إعادة استجابة خطأ عامة (بدون تفاصيل تقنية)
    return NextResponse.json({
      top_rated: [],
      best_sellers: [],
      discounted: [],
      new_arrivals: [],
      premium: [],
      error: 'An error occurred while processing your request, please try again later'
    }, { status: 500 });
  }
}

/**
 * دالة لإنشاء بيانات وهمية للعروض (للتطوير فقط)
 * @param {string} lang - رمز اللغة
 * @param {string} currency - رمز العملة
 * @returns {Object} - العروض الوهمية
 */
function generateDummyOffers(lang, currency) {
  return {
    top_rated: [
      {
        id: '1001',
        title: lang === 'ar' ? 'سماعات بلوتوث لاسلكية' : 'Wireless Bluetooth Headphones',
        price: 50.00,
        discountRate: 30,
        rating: 4.8,
        reviewCount: 1200,
        sold: 5000,
        sellerRank: 'Diamond',
        productUrl: 'https://example.com/product/1001',
        affiliateUrl: 'https://example.com/product/1001?tag=affiliate',
        imageUrl: 'https://example.com/images/headphones.jpg',
        trustScore: 0.95
      },
      {
        id: '1006',
        title: lang === 'ar' ? 'مكبر صوت بلوتوث محمول' : 'Portable Bluetooth Speaker',
        price: 35.00,
        discountRate: 20,
        rating: 4.6,
        reviewCount: 800,
        sold: 3500,
        sellerRank: 'Gold',
        productUrl: 'https://example.com/product/1006',
        affiliateUrl: 'https://example.com/product/1006?tag=affiliate',
        imageUrl: 'https://example.com/images/speaker.jpg',
        trustScore: 0.89
      }
    ],
    best_sellers: [
      {
        id: '1002',
        title: lang === 'ar' ? 'ساعة ذكية متعددة الوظائف' : 'Multifunctional Smart Watch',
        price: 80.00,
        discountRate: 25,
        rating: 4.5,
        reviewCount: 850,
        sold: 3200,
        sellerRank: 'Gold',
        productUrl: 'https://example.com/product/1002',
        affiliateUrl: 'https://example.com/product/1002?tag=affiliate',
        imageUrl: 'https://example.com/images/smartwatch.jpg',
        trustScore: 0.88
      },
      {
        id: '1007',
        title: lang === 'ar' ? 'حامل هاتف للسيارة' : 'Car Phone Holder',
        price: 15.00,
        discountRate: 10,
        rating: 4.4,
        reviewCount: 1500,
        sold: 7000,
        sellerRank: 'Gold',
        productUrl: 'https://example.com/product/1007',
        affiliateUrl: 'https://example.com/product/1007?tag=affiliate',
        imageUrl: 'https://example.com/images/phone_holder.jpg',
        trustScore: 0.86
      }
    ],
    discounted: [
      {
        id: '1003',
        title: lang === 'ar' ? 'شاحن سريع USB-C' : 'Fast USB-C Charger',
        price: 25.00,
        discountRate: 40,
        rating: 4.3,
        reviewCount: 600,
        sold: 2500,
        sellerRank: 'Gold',
        productUrl: 'https://example.com/product/1003',
        affiliateUrl: 'https://example.com/product/1003?tag=affiliate',
        imageUrl: 'https://example.com/images/charger.jpg',
        trustScore: 0.82
      },
      {
        id: '1008',
        title: lang === 'ar' ? 'سماعات أذن سلكية' : 'Wired Earphones',
        price: 20.00,
        discountRate: 50,
        rating: 4.2,
        reviewCount: 450,
        sold: 1800,
        sellerRank: 'Gold',
        productUrl: 'https://example.com/product/1008',
        affiliateUrl: 'https://example.com/product/1008?tag=affiliate',
        imageUrl: 'https://example.com/images/earphones.jpg',
        trustScore: 0.80
      }
    ],
    new_arrivals: [
      {
        id: '1004',
        title: lang === 'ar' ? 'حقيبة ظهر مضادة للماء' : 'Waterproof Backpack',
        price: 45.00,
        discountRate: 15,
        rating: 4.2,
        reviewCount: 120,
        sold: 500,
        sellerRank: 'Gold',
        productUrl: 'https://example.com/product/1004',
        affiliateUrl: 'https://example.com/product/1004?tag=affiliate',
        imageUrl: 'https://example.com/images/backpack.jpg',
        trustScore: 0.75,
        isNew: true
      },
      {
        id: '1009',
        title: lang === 'ar' ? 'مصباح LED قابل للشحن' : 'Rechargeable LED Light',
        price: 30.00,
        discountRate: 10,
        rating: 4.0,
        reviewCount: 80,
        sold: 300,
        sellerRank: 'Gold',
        productUrl: 'https://example.com/product/1009',
        affiliateUrl: 'https://example.com/product/1009?tag=affiliate',
        imageUrl: 'https://example.com/images/led_light.jpg',
        trustScore: 0.72,
        isNew: true
      }
    ],
    premium: [
      {
        id: '1005',
        title: lang === 'ar' ? 'سماعات رأس بخاصية إلغاء الضوضاء' : 'Noise Cancelling Headphones',
        price: 120.00,
        discountRate: 20,
        rating: 4.7,
        reviewCount: 950,
        sold: 2800,
        sellerRank: 'Diamond',
        productUrl: 'https://example.com/product/1005',
        affiliateUrl: 'https://example.com/product/1005?tag=affiliate',
        imageUrl: 'https://example.com/images/premium_headphones.jpg',
        trustScore: 0.92
      },
      {
        id: '1010',
        title: lang === 'ar' ? 'كاميرا رقمية احترافية' : 'Professional Digital Camera',
        price: 350.00,
        discountRate: 15,
        rating: 4.9,
        reviewCount: 750,
        sold: 1200,
        sellerRank: 'Diamond',
        productUrl: 'https://example.com/product/1010',
        affiliateUrl: 'https://example.com/product/1010?tag=affiliate',
        imageUrl: 'https://example.com/images/camera.jpg',
        trustScore: 0.95
      }
    ]
  };
}