import { NextResponse } from 'next/server';
import { query } from '../../../lib/db';

// إجبار المسار على العمل بشكل ديناميكي
export const dynamic = 'force-dynamic';

/**
 * نقطة نهاية API لتحديث العروض
 * تقوم بجلب العروض من API علي إكسبرس وتخزينها في قاعدة البيانات
 * يتم استدعاؤها تلقائياً كل 24 ساعة أو عند الحاجة
 */
export async function GET(request) {
  try {
    // التحقق من رمز الأمان (لمنع الوصول غير المصرح به)
    const { searchParams } = new URL(request.url);
    const secretKey = searchParams.get('secret');
    
    if (secretKey !== process.env.REFRESH_SECRET_KEY) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // التحقق مما إذا كان التحديث قيد التنفيذ بالفعل
    const statusResult = await query('SELECT * FROM offers_refresh_status WHERE id = 1');
    
    if (statusResult.length === 0 || statusResult[0].is_refreshing) {
      return NextResponse.json({ 
        status: 'skipped', 
        message: 'Refresh already in progress or status not found' 
      });
    }
    
    // تحديث الحالة إلى "قيد التحديث"
    await query('UPDATE offers_refresh_status SET is_refreshing = TRUE WHERE id = 1');
    
    // الحصول على اللغات والعملات النشطة
    const languages = await query('SELECT code FROM languages WHERE is_active = TRUE');
    const currencies = await query('SELECT code FROM currencies WHERE is_active = TRUE');
    const categories = await query('SELECT category_key FROM offer_categories');
    
    // جلب العروض لكل لغة وعملة
    let successCount = 0;
    let errorCount = 0;
    
    // استخدام نظام طابور بسيط لاحترام حدود API
    for (const lang of languages) {
      for (const currency of currencies) {
        try {
          // تأخير بين الطلبات لاحترام حدود API (5 طلبات/ثانية، 30 طلب/دقيقة)
          await new Promise(resolve => setTimeout(resolve, 300)); // 300ms بين الطلبات
          
          // في الإنتاج، هنا سيتم الاتصال بـ API علي إكسبرس
          const offers = await fetchOffersFromAPI(lang.code, currency.code);
          
          // حذف العروض القديمة لهذه اللغة والعملة
          await query(
            'DELETE FROM cached_offers WHERE language_code = ? AND currency_code = ?',
            [lang.code, currency.code]
          );
          
          // تخزين العروض الجديدة لكل فئة
          for (const category of categories) {
            await query(
              'INSERT INTO cached_offers (category_key, language_code, currency_code, offers_data, expires_at) VALUES (?, ?, ?, ?, NOW() + INTERVAL 24 HOUR)',
              [category.category_key, lang.code, currency.code, JSON.stringify(offers[category.category_key] || [])]
            );
          }
          
          successCount++;
        } catch (error) {
          console.error(`Error fetching offers for ${lang.code}/${currency.code}:`, error);
          errorCount++;
        }
      }
    }
    
    // تحديث حالة التحديث بعد الانتهاء
    await query(`
      UPDATE offers_refresh_status 
      SET last_refresh = NOW(),
          next_refresh = NOW() + INTERVAL 24 HOUR,
          is_refreshing = FALSE,
          refresh_count = refresh_count + 1
      WHERE id = 1
    `);
    
    return NextResponse.json({
      status: 'success',
      refreshed_at: new Date().toISOString(),
      next_refresh: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      stats: {
        success: successCount,
        errors: errorCount,
        languages: languages.length,
        currencies: currencies.length
      }
    });
  } catch (error) {
    console.error('Offers refresh error:', error);
    
    // إعادة تعيين حالة التحديث في حالة حدوث خطأ
    await query('UPDATE offers_refresh_status SET is_refreshing = FALSE WHERE id = 1');
    
    return NextResponse.json({
      status: 'error',
      message: 'An error occurred during offers refresh',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}

/**
 * دالة لجلب العروض من API علي إكسبرس
 * @param {string} lang - رمز اللغة
 * @param {string} currency - رمز العملة
 * @returns {Promise<Object>} - العروض المجلوبة
 */
async function fetchOffersFromAPI(lang, currency) {
  // في بيئة الإنتاج، يجب استرجاع مفاتيح API من متغيرات بيئية أو قاعدة بيانات آمنة
  const appKey = process.env.ALIEXPRESS_APP_KEY;
  const secretKey = process.env.ALIEXPRESS_SECRET_KEY;
  const tagId = process.env.ALIEXPRESS_TAG_ID;
  
  // محاكاة تأخير الشبكة
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // محاكاة البيانات - في الإنتاج، سيتم استبدال هذا بطلب حقيقي إلى API علي إكسبرس
  return {
    top_rated: [
      {
        id: '1001',
        title: lang === 'ar' ? 'سماعات بلوتوث لاسلكية' : 'Wireless Bluetooth Headphones',
        price: 50.00,
        discountRate: 30,
        rating: 4.8,
        reviewCount: 1200,
        sold: 5000,
        sellerRank: 'Diamond',
        productUrl: 'https://example.com/product/1001',
        affiliateUrl: 'https://example.com/product/1001?tag=affiliate',
        imageUrl: 'https://example.com/images/headphones.jpg',
        trustScore: 0.95
      },
      // ... باقي العروض
    ],
    best_sellers: [
      {
        id: '1002',
        title: lang === 'ar' ? 'ساعة ذكية متعددة الوظائف' : 'Multifunctional Smart Watch',
        price: 80.00,
        discountRate: 25,
        rating: 4.5,
        reviewCount: 850,
        sold: 3200,
        sellerRank: 'Gold',
        productUrl: 'https://example.com/product/1002',
        affiliateUrl: 'https://example.com/product/1002?tag=affiliate',
        imageUrl: 'https://example.com/images/smartwatch.jpg',
        trustScore: 0.88
      },
      // ... باقي العروض
    ],
    discounted: [
      {
        id: '1003',
        title: lang === 'ar' ? 'شاحن سريع USB-C' : 'Fast USB-C Charger',
        price: 25.00,
        discountRate: 40,
        rating: 4.3,
        reviewCount: 600,
        sold: 2500,
        sellerRank: 'Gold',
        productUrl: 'https://example.com/product/1003',
        affiliateUrl: 'https://example.com/product/1003?tag=affiliate',
        imageUrl: 'https://example.com/images/charger.jpg',
        trustScore: 0.82
      },
      // ... باقي العروض
    ],
    new_arrivals: [
      {
        id: '1004',
        title: lang === 'ar' ? 'حقيبة ظهر مضادة للماء' : 'Waterproof Backpack',
        price: 45.00,
        discountRate: 15,
        rating: 4.2,
        reviewCount: 120,
        sold: 500,
        sellerRank: 'Gold',
        productUrl: 'https://example.com/product/1004',
        affiliateUrl: 'https://example.com/product/1004?tag=affiliate',
        imageUrl: 'https://example.com/images/backpack.jpg',
        trustScore: 0.75,
        isNew: true
      },
      // ... باقي العروض
    ],
    premium: [
      {
        id: '1005',
        title: lang === 'ar' ? 'سماعات رأس بخاصية إلغاء الضوضاء' : 'Noise Cancelling Headphones',
        price: 120.00,
        discountRate: 20,
        rating: 4.7,
        reviewCount: 950,
        sold: 2800,
        sellerRank: 'Diamond',
        productUrl: 'https://example.com/product/1005',
        affiliateUrl: 'https://example.com/product/1005?tag=affiliate',
        imageUrl: 'https://example.com/images/premium_headphones.jpg',
        trustScore: 0.92
      },
      // ... باقي العروض
    ]
  };
}