import { NextResponse } from 'next/server';
import { clearOffersCache } from '../../../services/offersService';

// إجبار المسار على العمل بشكل ديناميكي
export const dynamic = 'force-dynamic';

/**
 * نقطة نهاية API لمسح كاش العروض
 * يمكن استخدامها لإزالة البيانات المخزنة مؤقتاً
 */
export async function POST(request) {
  try {
    // التحقق من المفتاح السري (للأمان)
    const { searchParams } = new URL(request.url);
    const secretKey = searchParams.get('secret');
    
    if (secretKey !== (process.env.CLEAR_CACHE_SECRET_KEY || 'dev_clear_secret')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    console.log('[Server API] مسح كاش العروض...');
    
    // مسح الكاش
    const success = await clearOffersCache();
    
    if (!success) {
      return NextResponse.json({ 
        success: false, 
        message: 'Failed to clear offers cache' 
      });
    }
    
    return NextResponse.json({ 
      success: true, 
      message: 'Offers cache cleared successfully'
    });
  } catch (error) {
    console.error('[Server API] خطأ في مسح كاش العروض:', error);
    
    return NextResponse.json({ 
      success: false, 
      message: 'An error occurred while clearing cache' 
    }, { status: 500 });
  }
}
