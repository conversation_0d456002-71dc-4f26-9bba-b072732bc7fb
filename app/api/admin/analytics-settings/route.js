import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const SETTINGS_FILE = path.join(process.cwd(), 'analytics-settings.json');

// GET - جلب إعدادات Google Analytics
export async function GET() {
  try {
    // محاولة قراءة الإعدادات من الملف
    if (fs.existsSync(SETTINGS_FILE)) {
      const data = fs.readFileSync(SETTINGS_FILE, 'utf8');
      const settings = JSON.parse(data);
      return NextResponse.json(settings);
    }
    
    // إرجاع إعدادات افتراضية إذا لم يوجد الملف
    const defaultSettings = {
      enabled: false,
      ga4MeasurementId: '',
      universalTrackingId: '',
      respectDoNotTrack: true,
      anonymizeIp: true
    };
    
    return NextResponse.json(defaultSettings);
  } catch (error) {
    console.error('Error loading analytics settings:', error);
    return NextResponse.json(
      { error: 'Failed to load settings' },
      { status: 500 }
    );
  }
}

// POST - حفظ إعدادات Google Analytics
export async function POST(request) {
  try {
    const settings = await request.json();
    
    // التحقق من صحة البيانات
    if (settings.enabled) {
      if (!settings.ga4MeasurementId && !settings.universalTrackingId) {
        return NextResponse.json(
          { error: 'يجب إدخال معرف GA4 أو Universal Analytics على الأقل' },
          { status: 400 }
        );
      }
      
      if (settings.ga4MeasurementId && !settings.ga4MeasurementId.match(/^G-[A-Z0-9]+$/)) {
        return NextResponse.json(
          { error: 'معرف GA4 غير صحيح' },
          { status: 400 }
        );
      }
      
      if (settings.universalTrackingId && !settings.universalTrackingId.match(/^UA-\d+-\d+$/)) {
        return NextResponse.json(
          { error: 'معرف Universal Analytics غير صحيح' },
          { status: 400 }
        );
      }
    }
    
    // حفظ الإعدادات في ملف JSON
    fs.writeFileSync(SETTINGS_FILE, JSON.stringify(settings, null, 2));
    
    // إنشاء ملف JavaScript للتضمين في الموقع
    generateAnalyticsScript(settings);
    
    return NextResponse.json({ 
      success: true, 
      message: 'تم حفظ إعدادات Google Analytics بنجاح' 
    });
  } catch (error) {
    console.error('Error saving analytics settings:', error);
    return NextResponse.json(
      { error: 'Failed to save settings' },
      { status: 500 }
    );
  }
}

// دالة لإنشاء ملف JavaScript للتضمين في الموقع
function generateAnalyticsScript(settings) {
  if (!settings.enabled) {
    // إذا كان التتبع معطل، إنشاء ملف فارغ
    const emptyScript = '// Google Analytics is disabled\n';
    fs.writeFileSync(
      path.join(process.cwd(), 'public', 'analytics.js'),
      emptyScript
    );
    return;
  }
  
  let script = '// Google Analytics Script - Auto Generated\n\n';
  
  // إضافة GA4 إذا كان موجود
  if (settings.ga4MeasurementId) {
    script += `// Google Analytics 4
(function() {
  // التحقق من إعداد عدم التتبع
  if (${settings.respectDoNotTrack} && navigator.doNotTrack === '1') {
    return;
  }
  
  // إنشاء وإدراج script tag
  var script = document.createElement('script');
  script.async = true;
  script.src = 'https://www.googletagmanager.com/gtag/js?id=${settings.ga4MeasurementId}';
  document.head.appendChild(script);
  
  // إعداد gtag
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', '${settings.ga4MeasurementId}'${settings.anonymizeIp ? ", { 'anonymize_ip': true }" : ''});
})();

`;
  }
  
  // إضافة Universal Analytics إذا كان موجود
  if (settings.universalTrackingId) {
    script += `// Universal Analytics
(function() {
  // التحقق من إعداد عدم التتبع
  if (${settings.respectDoNotTrack} && navigator.doNotTrack === '1') {
    return;
  }
  
  // إنشاء وإدراج script tag
  var script = document.createElement('script');
  script.async = true;
  script.src = 'https://www.googletagmanager.com/gtag/js?id=${settings.universalTrackingId}';
  document.head.appendChild(script);
  
  // إعداد gtag
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', '${settings.universalTrackingId}'${settings.anonymizeIp ? ", { 'anonymize_ip': true }" : ''});
})();

`;
  }
  
  // حفظ الملف
  fs.writeFileSync(
    path.join(process.cwd(), 'public', 'analytics.js'),
    script
  );
}