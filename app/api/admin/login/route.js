import { NextResponse } from 'next/server';
import prisma from '../../../../lib/prisma';
import bcrypt from 'bcryptjs';

export async function POST(request) {
  try {
    const { email, password, token } = await request.json();

    // التحقق من وجود البيانات المطلوبة
    if (!email || !password || !token) {
      return NextResponse.json(
        { error: 'جميع الحقول مطلوبة' },
        { status: 400 }
      );
    }

    // البحث عن المسؤول بواسطة البريد الإلكتروني
    const admin = await prisma.admin.findUnique({
      where: { email }
    });

    // التحقق من وجود المسؤول
    if (!admin) {
      return NextResponse.json(
        { error: 'بيانات الاعتماد غير صحيحة' },
        { status: 401 }
      );
    }

    // التحقق من كلمة المرور
    const isPasswordValid = await bcrypt.compare(password, admin.password);
    if (!isPasswordValid) {
      return NextResponse.json(
        { error: 'بيانات الاعتماد غير صحيحة' },
        { status: 401 }
      );
    }

    // التحقق من التوكن
    if (token !== admin.token) {
      return NextResponse.json(
        { error: 'رمز الأمان غير صحيح' },
        { status: 401 }
      );
    }

    // تسجيل الدخول ناجح
    return NextResponse.json({
      success: true,
      admin: {
        id: admin.id,
        email: admin.email,
        name: admin.name
      }
    });
  } catch (error) {
    console.error('خطأ في تسجيل دخول المسؤول:', error);
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}