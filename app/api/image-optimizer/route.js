import { NextResponse } from 'next/server';

// إجبار المسار على العمل بشكل ديناميكي
export const dynamic = 'force-dynamic';

/**
 * نقطة نهاية API لضغط وتحسين الصور
 * تقوم بتحسين الصور وتخزينها مؤقتًا لتحسين الأداء
 */
export async function GET(request) {
  try {
    // استخراج معلمات الاستعلام
    const { searchParams } = new URL(request.url);
    const imageUrl = searchParams.get('url');
    const width = parseInt(searchParams.get('width') || '300', 10);
    const height = parseInt(searchParams.get('height') || '300', 10);
    const quality = parseInt(searchParams.get('quality') || '80', 10);
    const format = searchParams.get('format') || 'webp'; // webp, jpeg, png
    
    // التحقق من وجود رابط الصورة
    if (!imageUrl) {
      return NextResponse.json(
        { error: 'يجب توفير رابط الصورة (url)' },
        { status: 400 }
      );
    }
    
    // التحقق من صحة المعلمات
    if (isNaN(width) || width <= 0 || width > 2000) {
      return NextResponse.json(
        { error: 'عرض الصورة غير صالح (يجب أن يكون بين 1 و 2000)' },
        { status: 400 }
      );
    }
    
    if (isNaN(height) || height <= 0 || height > 2000) {
      return NextResponse.json(
        { error: 'ارتفاع الصورة غير صالح (يجب أن يكون بين 1 و 2000)' },
        { status: 400 }
      );
    }
    
    if (isNaN(quality) || quality < 10 || quality > 100) {
      return NextResponse.json(
        { error: 'جودة الصورة غير صالحة (يجب أن تكون بين 10 و 100)' },
        { status: 400 }
      );
    }
    
    if (!['webp', 'jpeg', 'png'].includes(format)) {
      return NextResponse.json(
        { error: 'تنسيق الصورة غير صالح (يجب أن يكون webp أو jpeg أو png)' },
        { status: 400 }
      );
    }
    
    // في بيئة الإنتاج، سنستخدم مكتبة مثل Sharp لضغط وتحسين الصور
    // هنا نقوم بإعادة توجيه الطلب إلى خدمة تحسين الصور المجانية (Imgix أو Cloudinary)
    
    // مثال باستخدام خدمة تحويل الصور من Imgix-like URL
    // في الإنتاج، يمكن استخدام Sharp أو Cloudinary أو أي خدمة أخرى
    
    // تحويل URL الصورة الأصلي إلى URL محسن
    // هنا نستخدم خدمة picsum.photos كمثال لأنها تدعم معلمات التحجيم
    let optimizedUrl;
    
    if (imageUrl.includes('picsum.photos')) {
      // إذا كانت الصورة من picsum.photos، نستخدم معلماتها المدمجة
      const urlParts = imageUrl.split('/');
      const seedIndex = urlParts.findIndex(part => part === 'seed');
      
      if (seedIndex !== -1 && seedIndex + 1 < urlParts.length) {
        const seed = urlParts[seedIndex + 1];
        optimizedUrl = `https://picsum.photos/seed/${seed}/${width}/${height}`;
      } else {
        // إذا لم نجد "seed"، نفترض أن الصورة بتنسيق آخر
        optimizedUrl = `https://picsum.photos/${width}/${height}`;
      }
    } else if (imageUrl.startsWith('https://example.com/')) {
      // للصور الوهمية من example.com، نستخدم picsum كبديل
      const randomSeed = Math.floor(Math.random() * 1000);
      optimizedUrl = `https://picsum.photos/seed/${randomSeed}/${width}/${height}`;
    } else {
      // للصور الأخرى، نستخدم خدمة تحويل الصور
      // في الإنتاج، يمكن استخدام Sharp أو Cloudinary
      optimizedUrl = `https://images.weserv.nl/?url=${encodeURIComponent(imageUrl)}&w=${width}&h=${height}&q=${quality}&output=${format}`;
    }
    
    // إعادة توجيه إلى الصورة المحسنة
    return NextResponse.json({
      original_url: imageUrl,
      optimized_url: optimizedUrl,
      width,
      height,
      quality,
      format
    });
  } catch (error) {
    // تسجيل الخطأ داخليًا فقط
    console.error('Image optimization error:', error);
    
    // إعادة استجابة خطأ عامة
    return NextResponse.json(
      { error: 'حدث خطأ أثناء معالجة الصورة، يرجى المحاولة لاحقًا' },
      { status: 500 }
    );
  }
}