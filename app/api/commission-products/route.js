import { NextResponse } from "next/server";
import { getCache, setCache } from "../../lib/redis";

// وقت انتهاء الصلاحية (5 ساعات بالثواني)
const CACHE_EXPIRY = 5 * 60 * 60;

/**
 * معالج طلبات API لمنتجات التخفيض بالعمولات
 * يقوم بالاتصال بـ API علي إكسبرس بشكل آمن دون كشف مفاتيح API للمتصفح
 * مع تخزين النتائج مؤقتاً في Redis لمدة 5 ساعات
 */
export async function POST(request) {
  try {
    // استخراج بيانات الطلب
    const requestData = await request.json();
    const { query, currency = "USD", language = "ar" } = requestData;

    // في بيئة الإنتاج، يجب استرجاع مفاتيح API من متغيرات بيئية أو قاعدة بيانات آمنة
    // للتطوير المحلي، نستخدم مفاتيح وهمية
    const appKey = process.env.ALIEXPRESS_APP_KEY || "dev_app_key_123";
    const secretKey = process.env.ALIEXPRESS_SECRET_KEY || "dev_secret_key_456";
    const tagId = process.env.ALIEXPRESS_TAG_ID || "dev_tag_id_789";

    // في بيئة الإنتاج، يجب التحقق من وجود المفاتيح الحقيقية
    // لكن في بيئة التطوير، نستمر باستخدام البيانات الوهمية
    if (
      process.env.NODE_ENV === "production" &&
      (!appKey || !secretKey || !tagId)
    ) {
      // تسجيل الخطأ داخليًا
      console.error("API keys not configured in production environment");

      // إعادة استجابة خطأ عامة (بدون تفاصيل تقنية)
      return NextResponse.json(
        {
          success: false,
          error:
            language === "ar"
              ? "الخدمة غير متاحة حاليًا، يرجى المحاولة لاحقًا"
              : "Service temporarily unavailable, please try again later",
          products: [],
          recommendation: "",
          language,
          currency,
        },
        { status: 503 },
      );
    }

    // إنشاء مفتاح للتخزين المؤقت باستخدام استعلام البحث والعملة واللغة
    const cacheKey = `commission:${query.toLowerCase().trim()}:${currency}:${language}`;
    
    // التحقق من وجود نتائج مخزنة في Redis
    const cachedResults = await getCache(cacheKey);
    
    // إذا وجدت نتائج مخزنة، نعيدها مباشرة
    if (cachedResults) {
      console.log(`[Server API] استرجاع نتائج منتجات التخفيض المخزنة لـ "${query}"`);
      return NextResponse.json(cachedResults);
    }
    
    console.log(`[Server API] إرسال طلب لمنتجات التخفيض بالعمولات: "${query}"`);

    // في بيئة التطوير، نعيد قائمة فارغة
    // في بيئة الإنتاج، سيتم الاتصال بـ API علي إكسبرس الحقيقي
    const filteredProducts = [];

    // في الإنتاج، هنا سيتم الاتصال بـ API علي إكسبرس:
    /*
    // إعداد بيانات الطلب لـ API علي إكسبرس
    const apiRequestData = {
      appKey,
      secretKey,
      tagId,
      query,
      currency,
      language
    };

    // إرسال طلب إلى API علي إكسبرس
    const response = await fetch('https://api.aliexpress.com/commission-products', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(apiRequestData)
    });

    if (!response.ok) {
      throw new Error(`AliExpress API error: ${response.status}`);
    }

    const data = await response.json();

    // معالجة البيانات
    const filteredProducts = data.products.map(product => ({
      id: product.id,
      title: product.title,
      price: product.price,
      commission_price: product.commission_price,
      shipping_price: product.shipping_price,
      total_price: product.total_price,
      commission_discount: product.commission_discount,
      points_required: product.points_required,
      rating: product.rating,
      reviews_count: product.reviews_count,
      sold_count: product.sold_count,
      image_url: product.image_url,
      score: product.score,
      recommended: product.recommended,
      savings: product.savings
    }));
    */

    // إعداد التوصية
    let recommendation = "";
    if (filteredProducts.length > 0) {
      // البحث عن المنتج الموصى به
      const recommendedProduct =
        filteredProducts.find((p) => p.recommended) || filteredProducts[0];

      recommendation =
        language === "ar"
          ? `🏆 العرض المميز: ${recommendedProduct.title} بسعر ${recommendedProduct.total_price.toFixed(2)} ${currency} بعد التخفيض (توفير ${recommendedProduct.savings.toFixed(2)} ${currency}) - يتطلب ${recommendedProduct.points_required} نقطة`
          : `🏆 Top Pick: ${recommendedProduct.title} for ${recommendedProduct.total_price.toFixed(2)} ${currency} after discount (Save ${recommendedProduct.savings.toFixed(2)} ${currency}) - Requires ${recommendedProduct.points_required} points`;
    }

    // إعداد النتائج
    const results = {
      success: true,
      products: filteredProducts,
      recommendation,
      language,
      currency,
    };
    
    // تخزين النتائج في Redis لمدة 5 ساعات
    await setCache(cacheKey, results, CACHE_EXPIRY);
    console.log(`[Server API] تم تخزين نتائج منتجات التخفيض لـ "${query}" في Redis لمدة 5 ساعات`);
    
    return NextResponse.json(results);
  } catch (error) {
    // تسجيل الخطأ داخليًا فقط (لا يتم إرساله للمتصفح)
    console.error("Commission products API error:", error);

    // استخراج اللغة والعملة من الطلب إذا أمكن
    let language = "ar";
    let currency = "USD";
    try {
      const requestData = await request.json();
      language = requestData.language || "ar";
      currency = requestData.currency || "USD";
    } catch (e) {
      // استخدام القيم الافتراضية إذا فشل استخراج البيانات
    }

    // إعادة استجابة خطأ عامة (بدون تفاصيل تقنية)
    return NextResponse.json(
      {
        success: false,
        error:
          language === "ar"
            ? "حدث خطأ أثناء معالجة طلبك، يرجى المحاولة لاحقًا"
            : "An error occurred while processing your request, please try again later",
        products: [],
        recommendation: "",
        language,
        currency,
      },
      { status: 500 },
    );
  }
}
