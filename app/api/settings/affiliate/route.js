import { NextResponse } from 'next/server';
import { getCache, setCache } from '../../../lib/redis';

// مفتاح التخزين المؤقت لإعدادات التسويق بالعمولة
const AFFILIATE_SETTINGS_CACHE_KEY = 'settings:affiliate';

// وقت انتهاء الصلاحية (24 ساعة بالثواني)
const CACHE_EXPIRY = 24 * 60 * 60;

/**
 * الحصول على إعدادات التسويق بالعمولة
 */
export async function GET() {
  try {
    // محاولة استرجاع الإعدادات من التخزين المؤقت
    const cachedSettings = await getCache(AFFILIATE_SETTINGS_CACHE_KEY);
    
    if (cachedSettings) {
      console.log('[Settings API] استرجاع إعدادات التسويق بالعمولة من التخزين المؤقت');
      return NextResponse.json(cachedSettings);
    }
    
    // في الإنتاج، يجب استرجاع الإعدادات من قاعدة البيانات
    // هنا نستخدم قيمة افتراضية للتجربة
    const defaultSettings = {
      tagId: process.env.ALIEXPRESS_TAG_ID || 'default_tag_id',
      commissionRate: '8%',
      lastUpdated: new Date().toISOString()
    };
    
    // تخزين الإعدادات في التخزين المؤقت
    await setCache(AFFILIATE_SETTINGS_CACHE_KEY, defaultSettings, CACHE_EXPIRY);
    
    return NextResponse.json(defaultSettings);
  } catch (error) {
    console.error('خطأ في استرجاع إعدادات التسويق بالعمولة:', error);
    
    return NextResponse.json(
      { error: 'حدث خطأ أثناء استرجاع الإعدادات' },
      { status: 500 }
    );
  }
}

/**
 * تحديث إعدادات التسويق بالعمولة (متاح فقط للمسؤولين)
 */
export async function POST(request) {
  try {
    // في الإنتاج، يجب التحقق من صلاحيات المستخدم هنا
    
    const data = await request.json();
    
    // التحقق من صحة البيانات
    if (!data.tagId) {
      return NextResponse.json(
        { error: 'يجب توفير معرف التتبع (Tag ID)' },
        { status: 400 }
      );
    }
    
    // تحديث الإعدادات
    const updatedSettings = {
      tagId: data.tagId,
      commissionRate: data.commissionRate || '8%',
      lastUpdated: new Date().toISOString()
    };
    
    // تخزين الإعدادات المحدثة في التخزين المؤقت
    await setCache(AFFILIATE_SETTINGS_CACHE_KEY, updatedSettings, CACHE_EXPIRY);
    
    console.log(`[Settings API] تم تحديث إعدادات التسويق بالعمولة: ${JSON.stringify(updatedSettings)}`);
    
    return NextResponse.json({
      message: 'تم تحديث إعدادات التسويق بالعمولة بنجاح',
      settings: updatedSettings
    });
  } catch (error) {
    console.error('خطأ في تحديث إعدادات التسويق بالعمولة:', error);
    
    return NextResponse.json(
      { error: 'حدث خطأ أثناء تحديث الإعدادات' },
      { status: 500 }
    );
  }
}