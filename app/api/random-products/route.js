import { NextResponse } from "next/server";
import { getRedisClient } from "../../lib/redis";

// إجبار المسار على العمل بشكل ديناميكي
export const dynamic = 'force-dynamic';

/**
 * جلب منتجات عشوائية من جميع نتائج البحث المخزنة في Redis
 * @param {number} count - عدد المنتجات المطلوبة
 * @returns {Array} قائمة منتجات عشوائية
 */
async function getRandomProductsFromCache(count = 24) {
  try {
    const client = await getRedisClient();
    if (!client) {
      console.log("Redis غير متاح");
      return [];
    }

    // جلب جميع مفاتيح البحث المخزنة
    const searchKeys = await client.keys("search:*");

    if (searchKeys.length === 0) {
      console.log("لا توجد منتجات مخزنة في Redis بعد");
      return [];
    }

    console.log(`تم العثور على ${searchKeys.length} مفتاح بحث في Redis`);

    // جمع المنتجات من جميع المفاتيح
    let allProducts = [];

    for (const key of searchKeys) {
      try {
        const data = await client.get(key);
        if (data) {
          const products = JSON.parse(data);
          if (Array.isArray(products) && products.length > 0) {
            allProducts = allProducts.concat(products);
          }
        }
      } catch (error) {
        console.error(`خطأ في قراءة المفتاح ${key}:`, error);
      }
    }

    if (allProducts.length === 0) {
      console.log("لم يتم العثور على منتجات صالحة في Redis");
      return [];
    }

    // إزالة المنتجات المكررة بناءً على ID
    const uniqueProducts = allProducts.filter(
      (product, index, self) =>
        index === self.findIndex((p) => p.id === product.id),
    );

    console.log(`تم جمع ${uniqueProducts.length} منتج فريد من Redis`);

    // خلط المنتجات عشوائياً
    const shuffledProducts = uniqueProducts.sort(() => Math.random() - 0.5);

    // إرجاع العدد المطلوب
    return shuffledProducts.slice(0, count);
  } catch (error) {
    console.error("خطأ في جلب المنتجات العشوائية من Redis:", error);
    return [];
  }
}

/**
 * معالج طلبات API للمنتجات العشوائية
 * يجلب منتجات عشوائية من جميع نتائج البحث المخزنة في Redis
 */
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const count = parseInt(searchParams.get("count")) || 24;

    console.log(`[Random Products API] طلب ${count} منتج عشوائي`);

    // جلب المنتجات العشوائية
    const randomProducts = await getRandomProductsFromCache(count);

    if (randomProducts.length === 0) {
      return NextResponse.json({
        products: [],
        message: "لا توجد منتجات مخزنة بعد. قم بالبحث أولاً!",
        isEmpty: true,
      });
    }

    console.log(
      `[Random Products API] تم إرجاع ${randomProducts.length} منتج عشوائي`,
    );

    return NextResponse.json({
      products: randomProducts,
      message: `تم جلب ${randomProducts.length} منتج عشوائي من نتائج البحث المخزنة`,
      isEmpty: false,
      total: randomProducts.length,
    });
  } catch (error) {
    console.error("Random Products API error:", error);

    return NextResponse.json(
      {
        products: [],
        error: "حدث خطأ أثناء جلب المنتجات العشوائية",
        isEmpty: true,
      },
      { status: 500 },
    );
  }
}
