'use client';

import { useTranslation } from '../components/TranslationProvider';

export default function TermsPage() {
  const { t, language, direction } = useTranslation();

  return (
    <div className="page-container" dir={direction}>
      <div className="page-content">
        <h1 className="page-title">
          {language === 'ar' ? 'الشروط والأحكام' : 
           language === 'fr' ? 'Termes et conditions' : 
           'Terms & Conditions'}
        </h1>

        {language === 'ar' ? (
          <div className="terms-content">
            <section className="terms-section">
              <h2>استخدام الموقع</h2>
              <p>باستخدامك لموقعنا، فإنك توافق على الالتزام بهذه الشروط والأحكام. نحتفظ بالحق في تغيير هذه الشروط في أي وقت، وستكون التغييرات سارية فور نشرها على الموقع.</p>
            </section>

            <section className="terms-section">
              <h2>دور الموقع</h2>
              <p>موقعنا هو منصة توصيات تساعدك في العثور على منتجات عالية الجودة. نحن لا نبيع المنتجات مباشرة ولا نتحمل مسؤولية المعاملات بين المشترين والبائعين.</p>
            </section>

            <section className="terms-section">
              <h2>الروابط الخارجية</h2>
              <p>قد يحتوي موقعنا على روابط لمواقع خارجية. نحن لسنا مسؤولين عن محتوى أو ممارسات الخصوصية لهذه المواقع.</p>
            </section>

            <section className="terms-section">
              <h2>حقوق الملكية الفكرية</h2>
              <p>جميع المحتويات المنشورة على هذا الموقع هي ملك لنا وتخضع لحماية حقوق النشر والعلامات التجارية.</p>
            </section>
          </div>
        ) : language === 'fr' ? (
          <div className="terms-content">
            <section className="terms-section">
              <h2>Utilisation du site</h2>
              <p>En utilisant notre site, vous acceptez de vous conformer à ces termes et conditions. Nous nous réservons le droit de modifier ces conditions à tout moment, et les modifications prendront effet dès leur publication sur le site.</p>
            </section>

            <section className="terms-section">
              <h2>Rôle du site</h2>
              <p>Notre site est une plateforme de recommandation qui vous aide à trouver des produits de haute qualité. Nous ne vendons pas directement les produits et ne sommes pas responsables des transactions entre acheteurs et vendeurs.</p>
            </section>

            <section className="terms-section">
              <h2>Liens externes</h2>
              <p>Notre site peut contenir des liens vers des sites externes. Nous ne sommes pas responsables du contenu ou des pratiques de confidentialité de ces sites.</p>
            </section>

            <section className="terms-section">
              <h2>Propriété intellectuelle</h2>
              <p>Tous les contenus publiés sur ce site nous appartiennent et sont protégés par les droits d'auteur et les marques déposées.</p>
            </section>
          </div>
        ) : (
          <div className="terms-content">
            <section className="terms-section">
              <h2>Use of the Site</h2>
              <p>By using our site, you agree to comply with these terms and conditions. We reserve the right to change these terms at any time, and changes will be effective immediately upon posting to the site.</p>
            </section>

            <section className="terms-section">
              <h2>Role of the Site</h2>
              <p>Our site is a recommendation platform that helps you find high-quality products. We do not directly sell products and are not responsible for transactions between buyers and sellers.</p>
            </section>

            <section className="terms-section">
              <h2>External Links</h2>
              <p>Our site may contain links to external websites. We are not responsible for the content or privacy practices of these sites.</p>
            </section>

            <section className="terms-section">
              <h2>Intellectual Property</h2>
              <p>All content published on this site is owned by us and is protected by copyright and trademark laws.</p>
            </section>
          </div>
        )}
      </div>
    </div>
  );
}