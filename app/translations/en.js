// English translation file

const en = {
  footer: {
    about: "About",
    aboutText:
      "Shopping Assistant is a platform that helps you find the best products at the best prices.",
    links: "Useful Links",
    contact: "Contact Us",
    faq: "FAQ",
    privacyPolicy: "Privacy Policy",
    terms: "Terms & Conditions",
    allRightsReserved: "All Rights Reserved",
  },
  time: {
    sunday: "Sunday",
    monday: "Monday",
    tuesday: "Tuesday",
    wednesday: "Wednesday",
    thursday: "Thursday",
    friday: "Friday",
    saturday: "Saturday",
  },
  home: {
    heroTitle: "Your Personal Shopping Assistant",
    heroDescription:
      "Get the best items at the best prices with our smart shopping assistant",
    startShoppingButton: "Start Shopping Now",
    learnMoreButton: "Learn More",
    whyChooseUs: "Why Choose Our Shopping Assistant?",
    features: {
      smartSearch: {
        title: "Smart Search",
        description: "Find items easily and quickly",
      },
      bestDeals: {
        title: "Best Deals",
        description: "Get the best discounts and offers",
      },
      fastShopping: {
        title: "Fast Shopping",
        description: "Complete your purchase in simple steps",
      },
    },
  },
  common: {
    siteTitle: "Shopping Assistant",
    siteDescription: "Shopping Assistant - Best deals and discounts",
    commissionDiscounts: "Commission Discount Items",
    currency: "SAR",
    buttons: {
      submit: "Submit",
      cancel: "Cancel",
      save: "Save",
      edit: "Edit",
      delete: "Delete",
      search: "Search",
      filter: "Filter",
      apply: "Apply",
      reset: "Reset",
      back: "Back",
      next: "Next",
      previous: "Previous",
      close: "Close",
      confirm: "Confirm",
      add: "Add",
      view: "View",
      login: "Login",
      register: "Register",
      logout: "Logout",
      help: "Help",
    },
    labels: {
      name: "Name",
      email: "Email",
      phone: "Phone Number",
      address: "Address",
      password: "Password",
      confirmPassword: "Confirm Password",
      currentPassword: "Current Password",
      newPassword: "New Password",
      username: "Username",
      search: "Search",
      filter: "Filter",
      sort: "Sort",
      date: "Date",
      time: "Time",
      status: "Status",
      category: "Category",
      price: "Price",
      quantity: "Quantity",
      total: "Total",
      description: "Description",
      details: "Details",
      from: "From",
      to: "To",
      all: "All",
      active: "Active",
      inactive: "Inactive",
      enabled: "Enabled",
      disabled: "Disabled",
      yes: "Yes",
      no: "No",
      loading: "Loading...",
      noResults: "No results found",
      noData: "No data available",
      error: "Error",
      success: "Success",
      warning: "Warning",
      info: "Information",
      version: "Version",
      optional: "Optional",
      saving: "Saving...",
      note: "Note",
    },
    errors: {
      required: "This field is required",
      invalidEmail: "Invalid email address",
      invalidPhone: "Invalid phone number",
      passwordMismatch: "Passwords do not match",
      minLength: "Must be at least {{length}} characters",
      maxLength: "Must not exceed {{length}} characters",
      serverError: "Server error occurred, please try again",
      networkError: "Network error, please check your internet connection",
      unauthorized: "You are not authorized to access this page",
      forbidden: "You do not have permission to access this page",
      notFound: "Page not found",
      unknown: "An unknown error occurred",
      currentPasswordRequired: "Current password is required",
      newPasswordRequired: "New password is required",
      confirmPasswordRequired: "Password confirmation is required",
      updateFailed: "Failed to update data. Please try again.",
      passwordChangeFailed: "Failed to change password. Please try again.",
      securityCodeSendFailed: "Failed to send security code. Please try again.",
    },
    messages: {
      welcome: "Welcome",
      goodbye: "Goodbye",
      confirmDelete: "Are you sure you want to delete this?",
      confirmLogout: "Are you sure you want to log out?",
      saved: "Successfully saved",
      deleted: "Successfully deleted",
      updated: "Successfully updated",
      created: "Successfully created",
      sent: "Successfully sent",
      received: "Successfully received",
      completed: "Successfully completed",
      processing: "Processing...",
      loading: "Loading...",
      waitMoment: "Please wait a moment...",
    },
    time: {
      now: "Now",
      today: "Today",
      yesterday: "Yesterday",
      tomorrow: "Tomorrow",
      days: "days",
      weeks: "weeks",
      months: "months",
      years: "years",
      ago: "ago",
      later: "later",
      second: "second",
      seconds: "seconds",
      minute: "minute",
      minutes: "minutes",
      hour: "hour",
      hours: "hours",
      day: "day",
      week: "week",
      month: "month",
      year: "year",
    },
  },
  navigation: {
    home: "Home",
    dashboard: "Dashboard",
    profile: "Profile",
    settings: "Settings",
    logout: "Logout",
    login: "Login",
    register: "Register",
    search: "Search",
    menu: "Menu",
    account: "Account",
  },
  profile: {
    title: "Profile",
    registrationDate: "Registration Date",
    goToSettings: "Go to Settings",
    personalInfo: "Personal Information",
    accountDetails: "Account Details",
    userAndAdminAlert:
      "You are currently logged in as both a regular user and an administrator. You can access both accounts through their dedicated pages.",
    goToUserProfile: "Go to User Profile",
    goToAdminProfile: "Go to Admin Profile",
  },
  settings: {
    title: "Settings",
    changePassword: "Change Password",
    currentPassword: "Current Password",
    newPassword: "New Password",
    confirmNewPassword: "Confirm New Password",
    currentPasswordPlaceholder: "Enter your current password",
    newPasswordPlaceholder: "Enter your new password",
    confirmNewPasswordPlaceholder: "Re-enter your new password",
    saveChanges: "Save Changes",
    passwordChanged: "Password changed successfully",
    backToProfile: "Back to Profile",
    passwordLength: "Password must be at least 6 characters",
    passwordMatch: "Passwords do not match",
    processing: "Processing...",
  },
  auth: {
    login: {
      title: "Login",
      subtitle: "Welcome back",
      emailPlaceholder: "Enter your email",
      passwordPlaceholder: "Enter your password",
      rememberMe: "Remember me",
      forgotPassword: "Forgot password?",
      noAccount: "Don't have an account?",
      createAccount: "Create a new account",
      loginButton: "Login",
      orContinueWith: "Or continue with",
    },
    register: {
      title: "Create Account",
      subtitle: "Join us today",
      namePlaceholder: "Enter your name",
      emailPlaceholder: "Enter your email",
      passwordPlaceholder: "Enter your password",
      confirmPasswordPlaceholder: "Confirm your password",
      agreeTerms: "I agree to the Terms and Conditions",
      hasAccount: "Already have an account?",
      loginHere: "Login here",
      registerButton: "Create Account",
      orContinueWith: "Or continue with",
    },
    forgotPassword: {
      title: "Forgot Password",
      subtitle: "Enter your email to reset your password",
      emailPlaceholder: "Enter your email",
      resetButton: "Reset Password",
      backToLogin: "Back to login",
      instructions: "We will send you a link to reset your password",
    },
    resetPassword: {
      title: "Reset Password",
      subtitle: "Enter your new password",
      newPasswordPlaceholder: "Enter new password",
      confirmPasswordPlaceholder: "Confirm new password",
      resetButton: "Change Password",
      backToLogin: "Back to login",
    },
    logout: {
      title: "Logout",
      confirmMessage: "Are you sure you want to log out?",
      confirmButton: "Yes, log out",
      cancelButton: "Cancel",
    },
  },
  dashboard: {
    title: "Customer Dashboard",
    accountInfo: "Account Information",
    overview: "Overview",
    analytics: "Analytics",
    statistics: "Statistics",
    recentActivity: "Recent Activity",
    notifications: "Notifications",
    quickActions: "Quick Actions",
    summary: "Summary",
    welcome: "Welcome, {{name}}!",
    todayDate: "Today: {{date}}",
    lastLogin: "Last login: {{date}}",
    viewAll: "View all",
    seeMore: "See more",
    noNotifications: "No notifications",
    noActivity: "No recent activity",
  },
  analytics: {
    settings: {
      title: "Google Analytics Setup",
      subtitle: "Connect your website with Google Analytics to track visits and statistics",
      basicSettings: "Enable Tracking",
      enableAnalytics: "Enable Google Analytics",
      enableAnalyticsHelp: "Enable visit and statistics tracking using Google Analytics",
      trackingIds: "Tracking IDs",
      ga4MeasurementId: "Google Analytics 4 (GA4) ID",
      ga4Help: "Google Analytics 4 measurement ID (recommended new version)",
      universalTrackingId: "Universal Analytics ID",
      universalHelp: "Universal Analytics tracking ID (legacy version)",
      privacySettings: "Privacy Settings",
      respectDoNotTrack: "Respect 'Do Not Track' setting",
      respectDoNotTrackHelp: "Don't track users who have enabled 'Do Not Track' in their browser",
      anonymizeIp: "Anonymize IP addresses",
      anonymizeIpHelp: "Hide the last part of IP addresses to protect user privacy",
      importantInfo: "Important Information",
      howToGetId: "How to get Google Analytics ID:",
      step1: "Go to",
      step2: "Create a new account or sign in",
      step3: "Add your website as a new property",
      step4: "Copy the measurement ID (GA4) or tracking ID (Universal Analytics)",
      step5: "Paste the ID in the appropriate field above",
      finalNote: "After saving the settings, you'll be able to view your website statistics in Google Analytics dashboard",
      testConnection: "Test Connection",
      testing: "Testing...",
      saveSettings: "Save Settings",
      saving: "Saving...",
      messages: {
        saveSuccess: "Google Analytics settings saved successfully",
        saveLocalSuccess: "Settings saved locally",
        connectionSuccess: "Tracking ID verified successfully",
        connectionFailed: "Failed to verify tracking ID",
        connectionError: "Error occurred while testing connection",
      },
      errors: {
        noTrackingId: "Must enter at least GA4 or Universal Analytics ID",
        invalidGA4Id: "Invalid GA4 ID. Must be in format: G-XXXXXXXXXX",
        invalidUniversalId: "Invalid Universal Analytics ID. Must be in format: UA-XXXXXXXXX-X",
        noTrackingIdForTest: "Must enter a tracking ID to test connection",
      },
    },
  },
  admin: {
    dashboard: {
      title: "Dashboard",
      welcome: "Welcome to the Admin Dashboard",
      summary: "Summary",
      recentOrders: "Recent Orders",
      recentUsers: "New Users",
      statistics: "Statistics",
      quickActions: "Quick Actions",
      clicks: "Clicks",
      totalClicks: "Total clicks on affiliate links",
      estimatedRevenue: "Estimated Revenue",
      commissionRevenue: "Estimated revenue from commissions",
      totalVisits: "Total website visits",
      clickToVisitRatio: "Click to visit ratio",
      comparedToPrevious: "Compared to previous period",
      performanceOverview: "Performance Overview",
      latestActivities: "Latest Activities",
      mostVisitedProducts: "Most Visited Products",
      viewAll: "View All",
      bags: "Bags",
      totalRevenue: "Total Revenue",
    },
    users: {
      title: "User Management",
      addUser: "Add New User",
      editUser: "Edit User",
      deleteUser: "Delete User",
      userDetails: "User Details",
      totalUsers: "Total Users",
      activeUsers: "Active Users",
      newUsers: "New Users",
      searchPlaceholder: "Search for a user...",
      name: "Name",
      email: "Email",
      phone: "Phone",
      role: "Role",
      status: "Status",
      registrationDate: "Registration Date",
      lastLogin: "Last Login",
      actions: "Actions",
      statuses: {
        active: "Active",
        inactive: "Inactive",
        banned: "Banned",
      },
      admin: "Admin",
      user: "User",
      editor: "Editor",
      moderator: "Moderator",
      confirmDelete: "Are you sure you want to delete this user?",
      userDeleted: "User successfully deleted",
      userAdded: "User successfully added",
      userUpdated: "User successfully updated",
      blockUser: "Block User",
      unblockUser: "Unblock User",
      activateUser: "Activate User",
      resetPassword: "Reset Password",
      basicInfo: "Basic Information",
      registrationInfo: "Registration Information",
      userStats: "User Statistics",
      ordersCount: "Orders Count",
      totalSpent: "Total Spent",
      points: "Points Earned",
    },
    profile: {
      title: "Profile",
      personalInfo: "Personal Information",
      changePassword: "Change Password",
      namePlaceholder: "Enter name",
      emailPlaceholder: "Enter email",
      phonePlaceholder: "Enter phone number",
      currentPasswordPlaceholder: "Enter current password",
      newPasswordPlaceholder: "Enter new password",
      confirmPasswordPlaceholder: "Confirm new password",
      saveChanges: "Save Changes",
      changesSaved: "Changes saved successfully",
      passwordChanged: "Password changed successfully",
      securityCode: "Security Code",
      securityCodePlaceholder: "Enter 6-digit security code",
      sendSecurityCode: "Send Security Code",
      securityCodeSent: "Security code sent to your email",
      confirmPasswordChange: "Confirm Password Change",
      passwordRequirements: "Password must be at least 8 characters long",
    },
    analytics: {
      title: "Analytics",
      period: "Period",
      today: "Today",
      thisWeek: "This Week",
      thisMonth: "This Month",
      thisYear: "This Year",
      traffic: "Traffic",
      sales: "Sales",
      users: "Users",
      visits: "Visits",
      pageViews: "Page Views",
      uniqueVisitors: "Unique Visitors",
      bounceRate: "Bounce Rate",
      avgSessionDuration: "Avg. Session Duration",
      sources: "Traffic Sources",
      totalSales: "Total Sales",
      ordersCount: "Orders Count",
      avgOrderValue: "Avg. Order Value",
      conversionRate: "Conversion Rate",
      categories: "Categories",
      totalUsers: "Total Users",
      activeUsers: "Active Users",
      newUsers: "New Users",
      retentionRate: "Retention Rate",
      demographics: "Demographics",
      dailyTraffic: "Daily Traffic",
      dailySales: "Daily Sales",
      newUsersDaily: "New Users Daily",
      revenue: "Revenue",
      loadingData: "Loading data...",
      trafficOverTime: "Traffic Over Time",
      trafficSources: "Traffic Sources",
      comparedToPrevious: "Compared to previous period",
    },
    settings: {
      title: "Settings",
      general: "General",
      appearance: "Appearance",
      notifications: "Notifications",
      security: "Security",
      language: "Language",
      theme: "Theme",
      emailNotifications: "Email Notifications",
      pushNotifications: "Push Notifications",
      twoFactorAuth: "Two-Factor Authentication",
      changePassword: "Change Password",
      saveChanges: "Save Changes",
      changesSaved: "Changes saved successfully",
      light: "Light",
      dark: "Dark",
      system: "System",
      on: "On",
      off: "Off",
      apiSettings: "API Settings",
      aliexpressApiKeys: "AliExpress API Keys",
      enterYourAppKey: "Enter your App Key",
      enterYourSecretKey: "Enter your Secret Key",
      enterYourTagId: "Enter your Tag ID",
      appKeyHint:
        "You can get your App Key from the AliExpress Developer Console",
      secretKeyHint: "Used to sign requests sent to the AliExpress API",
      tagIdHint: "Your tracking ID for earning commissions",
      saveSettings: "Save Settings",
      saving: "Saving...",
      settingsSavedAndSystemUpdated:
        "Settings saved and offers system updated successfully",
      settingsSavedButSystemUpdateFailed:
        "Settings saved successfully, but offers system update failed",
      errorSavingSettings: "Error saving settings",
      connectionTest: "Connection Test",
      testConnection: "Test Connection",
      testConnectionDescription:
        "Test your connection to the AliExpress API to verify your keys are correct.",
      testingConnection: "Testing connection...",
      connectionSuccessful: "Successfully connected to AliExpress API",
      connectionFailed: "Connection failed: Please enter all required API keys",
      generalSettingsFuture:
        "General settings will be added here in the future.",
      notificationSettingsFuture:
        "Notification settings will be added here in the future.",
    },
  },
  offers: {
    title: "Offers",
    allOffers: "All Offers",
    featuredOffers: "Featured Offers",
    newOffers: "New Offers",
    popularOffers: "Popular Offers",
    endingSoon: "Ending Soon",
    expired: "Expired",
    daysLeft: "{{days}} days left",
    hoursLeft: "{{hours}} hours left",
    minutesLeft: "{{minutes}} minutes left",
    startDate: "Start Date",
    endDate: "End Date",
    discount: "Discount",
    originalPrice: "Original Price",
    discountedPrice: "Discounted Price",
    useCode: "Use Code",
    copyCode: "Copy Code",
    codeCopied: "Code Copied",
    viewDetails: "View Details",
    shopNow: "Shop Now",
    filterBy: "Filter By",
    sortBy: "Sort By",
    category: "Category",
    store: "Store",
    discountAmount: "Discount Amount",
    expiryDate: "Expiry Date",
    newest: "Newest",
    oldest: "Oldest",
    highestDiscount: "Highest Discount",
    lowestDiscount: "Lowest Discount",
    noOffers: "No offers available",
    loadMore: "Load More",
    showLess: "Show Less",
    all: "All",
    electronics: "Electronics",
    fashion: "Fashion",
    health: "Health & Beauty",
    home: "Home & Garden",
    travel: "Travel",
    food: "Food & Drinks",
    entertainment: "Entertainment",
  },
  commissionDiscounts: {
    title: "Commission Discounts",
    search: "Search Items",
    searchPlaceholder: "Search for items to earn commission...",
    searchButton: "Search",
    searching: "Searching...",
    loading: "Loading items...",
    error: "An error occurred while loading items. Please try again.",
    noResults: "No items found",
    filters: {
      bestPrice: "Best Price",
      bestSelling: "Best Selling",
      bestRated: "Best Rated",
      highestCommission: "Highest Commission",
      viewModes: {
        grid: "Grid",
        list: "List",
      },
    },
    category: "Category",
    allCategories: "All Categories",
    sortBy: "Sort By",
    newest: "Newest",
    priceHighToLow: "Price: High to Low",
    priceLowToHigh: "Price: Low to High",
    popularity: "Popularity",
    commission: "Commission",
    highestCommission: "Highest Commission",
    price: "Price",
    originalPrice: "Original Price",
    discountedPrice: "Discounted Price",
    commissionRate: "Commission Rate",
    commissionValue: "Commission Value",
    itemDetails: "Item Details",
    description: "Description",
    specifications: "Specifications",
    reviews: "Reviews",
    relatedItems: "Related Items",
    getLink: "Get Link",
    copyLink: "Copy Link",
    linkCopied: "Link Copied",
    share: "Share",
    noItems: "No items available",
    loadMore: "Load More",
    showLess: "Show Less",
    electronics: "Electronics",
    fashion: "Fashion",
    health: "Health & Beauty",
    home: "Home & Garden",
    sports: "Sports",
    toys: "Toys",
    automotive: "Automotive",
    books: "Books",
  },
  search: {
    title: "Search Items",
    placeholder: "Search for items or enter item link...",
    button: "Search",
    searching: "Searching...",
    noResults: "No items found",
    tryDifferent: "Try using different search terms or browse categories",
    loading: "Searching for items...",
    error:
      "We are experiencing a temporary technical issue with the search service. Please try again later.",
    welcome: {
      title: "Smart Shopping Assistant",
      text: "Search for any product and I'll help you find the best deals",
      tip: "💡 Tip: Try searching for \"phone\", \"laptop\", or \"headphones\""
    },
    filters: {
      bestPrice: "Best Price",
      bestSelling: "Best Selling",
      bestRated: "Best Rated",
      viewModes: {
        grid: "Grid",
        list: "List",
      },
    },
    item: {
      freeShipping: "Free Shipping",
      reviews: "Reviews",
      sales: "Sales",
      discount: "Discount",
    },
    product: {
      freeShipping: "Free Shipping",
      reviews: "Reviews",
      sales: "Sales",
      discount: "Discount",
      shopNow: "Shop Now",
      viewDetails: "items",
    },
  },
  adminProfile: {
    title: "Profile",
    personalInfo: "Personal Information",
    name: "Name",
    email: "Email",
    phone: "Phone",
    role: "Role",
    lastLogin: "Last Login",
    saveChanges: "Save Changes",
    changesSaved: "Changes saved successfully",
    changePassword: "Change Password",
    currentPassword: "Current Password",
    newPassword: "New Password",
    confirmNewPassword: "Confirm New Password",
    securityCode: "Security Code",
    sendSecurityCode: "Send Security Code",
    securityCodeSent: "Security code sent to your email",
    verifyAndChange: "Verify and Change Password",
    passwordChanged: "Password changed successfully",
    namePlaceholder: "Enter your name",
    emailPlaceholder: "Enter your email",
    phonePlaceholder: "Enter your phone number",
    currentPasswordPlaceholder: "Enter current password",
    newPasswordPlaceholder: "Enter new password",
    confirmPasswordPlaceholder: "Confirm new password",
    securityCodePlaceholder: "Enter 6-digit security code",
    confirmPasswordChange: "Confirm Password Change",
    passwordRequirements: "Password must be at least 8 characters long",
    errors: {
      currentPasswordRequired: "Current password is required",
      newPasswordRequired: "New password is required",
      confirmPasswordRequired: "Confirm password is required",
      passwordMismatch: "Passwords do not match",
      securityCodeRequired: "Security code is required",
      invalidSecurityCode: "Invalid security code",
      serverError:
        "An error occurred while changing password. Please try again.",
    },
  },
  adminPanel: {
    adminLogin: {
      title: "Admin Login",
      subtitle: "Login to access the admin panel",
      loginButton: "Login",
      backToSite: "Back to Site",
      token: "Security Token",
      tokenPlaceholder: "Enter security token",
      tokenRequired: "Security token is required",
    },
    assistants: {
      title: "Assistant Management",
      addAssistant: "Add New Assistant",
      editAssistant: "Edit Assistant",
      deleteAssistant: "Delete Assistant",
      searchPlaceholder: "Search for assistant...",
      name: "Name",
      email: "Email",
      password: "Password",
      newPassword: "New Password",
      status: "Status",
      createdAt: "Created At",
      lastLogin: "Last Login",
      neverLoggedIn: "Never logged in",
      permissions: "Permissions",
      actions: "Actions",
      confirmDelete: "Are you sure you want to delete this assistant?",
      noAssistants: "No assistants found",
      leaveEmptyToKeep: "Leave empty to keep current password",
      status: {
        active: "Active",
        inactive: "Inactive",
        suspended: "Suspended",
      },
      permissions: {
        title: "Permissions",
        viewDashboard: "View Dashboard",
        viewAnalytics: "View Analytics",
        manageUsers: "Manage Users",
        manageSettings: "Manage Settings",
      },
    },
    sidebar: {
      dashboard: "Dashboard",
      settings: "Settings",
      analytics: "Analytics",
      users: "User Management",
      assistants: "Assistant Management",
      profile: "Profile",
      home: "Home",
      marketing: "Marketing",
      affiliates: "Affiliate Program",
      reports: "Reports",
      system: "System",
      logout: "Logout",
    },
  },
};

export default en;