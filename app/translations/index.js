'use client';

import { useEffect, useState } from 'react';
import ar from './ar';
import en from './en';
import fr from './fr';
import es from './es';
import uk from './uk';

// قاموس الترجمات
const translations = {
  ar,
  en,
  fr,
  es,
  uk
};

// Hook لاستخدام الترجمات
export function useTranslation() {
  const [language, setLanguage] = useState('ar');
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    const savedLang = localStorage.getItem('language') || 'ar';
    setLanguage(savedLang);

    // الاستماع لتغييرات اللغة
    const handleLanguageChange = (event) => {
      const newLanguage = event.detail?.language || localStorage.getItem('language') || 'ar';
      setLanguage(newLanguage);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => {
      window.removeEventListener('languageChange', handleLanguageChange);
    };
  }, []);

  // وظيفة الترجمة
  const t = (key) => {
    if (!isClient) return '';
    
    // تقسيم المفتاح إلى أجزاء (مثال: "common.buttons.submit")
    const parts = key.split('.');
    let translation = translations[language];
    
    // البحث عن الترجمة في الهيكل المتداخل
    for (const part of parts) {
      if (translation && translation[part] !== undefined) {
        translation = translation[part];
      } else {
        // إذا لم يتم العثور على الترجمة، نعود إلى المفتاح الأصلي
        return key;
      }
    }
    
    return translation;
  };

  // وظيفة للحصول على اتجاه النص الحالي
  const getDirection = () => {
    return language === 'ar' ? 'rtl' : 'ltr';
  };

  return { t, language, getDirection };
}

// وظيفة مساعدة للتنسيق
export function formatWithValues(text, values) {
  if (!text) return '';
  if (!values) return text;

  return Object.entries(values).reduce((result, [key, value]) => {
    const regex = new RegExp(`{{${key}}}`, 'g');
    return result.replace(regex, value);
  }, text);
}