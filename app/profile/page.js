'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from '../components/TranslationProvider';

export default function ProfilePage() {
  const router = useRouter();
  const { t, language, direction } = useTranslation();
  const [userEmail, setUserEmail] = useState('');
  const [registrationDate, setRegistrationDate] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // التحقق من حالة تسجيل الدخول
    const isAuthenticated = localStorage.getItem('is_authenticated');
    if (!isAuthenticated || isAuthenticated !== 'true') {
      router.push('/login');
      return;
    }

    // نتحقق من المسار الحالي للتأكد من أن المستخدم يريد الوصول إلى صفحة الملف الشخصي للمستخدم العادي
    // ولا نقوم بالتوجيه التلقائي إلى صفحة المسؤول حتى لو كان مسجل دخول كمسؤول أيضاً
    // هذا يسمح للمستخدم بالوصول إلى كلا الصفحتين إذا كان مسجل دخول بكلا الحسابين

    // استرجاع بيانات المستخدم
    const email = localStorage.getItem('user_email');
    
    // استرجاع تاريخ التسجيل (في حالة عدم وجوده، نستخدم التاريخ الحالي)
    let regDate = localStorage.getItem('registration_date');
    if (!regDate) {
      regDate = new Date().toISOString();
      localStorage.setItem('registration_date', regDate);
    }
    
    setUserEmail(email || '');
    
    // تنسيق التاريخ
    const formattedDate = new Date(regDate).toLocaleDateString(
      language === 'ar' ? 'ar-SA' : language, 
      { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      }
    );
    
    setRegistrationDate(formattedDate);
    setIsLoading(false);
  }, [router, language]);

  return (
    <div style={containerStyle} dir={direction}>
      <div style={profileContainerStyle}>
        <h1 style={titleStyle}>{t('profile.title', 'الملف الشخصي')}</h1>
        
        {isLoading ? (
          <div style={loadingStyle}>
            {t('common.messages.loading', 'جاري التحميل...')}
          </div>
        ) : (
          <div style={profileContentStyle}>
            <div style={profileItemStyle}>
              <div style={labelStyle}>{t('common.labels.email', 'البريد الإلكتروني')}</div>
              <div style={valueStyle}>{userEmail}</div>
            </div>
            
            <div style={profileItemStyle}>
              <div style={labelStyle}>{t('profile.registrationDate', 'تاريخ التسجيل')}</div>
              <div style={valueStyle}>{registrationDate}</div>
            </div>
            
            <div style={actionsContainerStyle}>
              <button 
                onClick={() => router.push('/settings')}
                style={buttonStyle}
              >
                {t('profile.goToSettings', 'الذهاب إلى الإعدادات')}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// الألوان
const aliexpressOrange = '#ff4747';
const aliexpressLightOrange = '#ff6e6e';
const aliexpressDarkOrange = '#e53935';

// الأنماط
const containerStyle = {
  maxWidth: '1200px',
  margin: '0 auto',
  padding: '20px',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'flex-start',
  minHeight: '80vh',
};

const profileContainerStyle = {
  backgroundColor: 'white',
  borderRadius: '8px',
  boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
  padding: '30px',
  width: '100%',
  maxWidth: '700px',
};

const titleStyle = {
  fontSize: '1.8rem',
  color: '#333',
  marginBottom: '25px',
  textAlign: 'center',
};

const profileContentStyle = {
  width: '100%',
};

const profileItemStyle = {
  marginBottom: '20px',
  padding: '15px',
  borderRadius: '6px',
  backgroundColor: '#f9f9f9',
};

const labelStyle = {
  fontSize: '0.9rem',
  color: '#666',
  marginBottom: '5px',
};

const valueStyle = {
  fontSize: '1.1rem',
  color: '#333',
  fontWeight: '500',
};

const actionsContainerStyle = {
  marginTop: '30px',
  display: 'flex',
  justifyContent: 'center',
};

const buttonStyle = {
  padding: '12px 20px',
  backgroundColor: aliexpressOrange,
  color: 'white',
  border: 'none',
  borderRadius: '4px',
  fontSize: '1rem',
  fontWeight: 'bold',
  cursor: 'pointer',
  transition: 'background-color 0.3s',
};

const loadingStyle = {
  textAlign: 'center',
  padding: '20px',
  color: '#666',
};