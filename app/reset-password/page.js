'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from '../components/TranslationProvider';

export default function ResetPasswordPage() {
  const router = useRouter();
  const { t, language, direction } = useTranslation();
  const [step, setStep] = useState(1); // 1: إدخال البريد، 2: إدخال الرمز، 3: تعيين كلمة السر الجديدة
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // التحقق من البريد الإلكتروني
  const validateEmail = () => {
    if (!email) {
      setErrors({ email: 'البريد الإلكتروني مطلوب' });
      return false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setErrors({ email: 'البريد الإلكتروني غير صالح' });
      return false;
    }
    return true;
  };

  // التحقق من رمز التحقق
  const validateVerificationCode = () => {
    if (!verificationCode) {
      setErrors({ verificationCode: 'رمز التحقق مطلوب' });
      return false;
    } else if (verificationCode.length !== 6) {
      setErrors({ verificationCode: 'رمز التحقق يجب أن يكون 6 أرقام' });
      return false;
    }
    return true;
  };

  // التحقق من كلمة المرور الجديدة
  const validateNewPassword = () => {
    if (!newPassword) {
      setErrors({ newPassword: 'كلمة المرور الجديدة مطلوبة' });
      return false;
    } else if (newPassword.length < 6) {
      setErrors({ newPassword: 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل' });
      return false;
    } else if (newPassword !== confirmNewPassword) {
      setErrors({ confirmNewPassword: 'كلمة المرور غير متطابقة' });
      return false;
    }
    return true;
  };

  // إرسال البريد الإلكتروني للحصول على رمز التحقق
  const handleSendVerificationCode = async (e) => {
    e.preventDefault();
    
    if (!validateEmail()) return;
    
    setIsSubmitting(true);
    setErrors({});
    
    try {
      // محاكاة إرسال رمز التحقق
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // في بيئة الإنتاج، سيتم إرسال رمز التحقق إلى البريد الإلكتروني
      // هنا نقوم بمحاكاة العملية
      
      setSuccessMessage(`تم إرسال رمز التحقق إلى ${email}`);
      setStep(2);
      
    } catch (error) {
      console.error('خطأ في إرسال رمز التحقق:', error);
      setErrors({ submit: 'حدث خطأ أثناء إرسال رمز التحقق. يرجى المحاولة مرة أخرى.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // التحقق من رمز التحقق
  const handleVerifyCode = async (e) => {
    e.preventDefault();
    
    if (!validateVerificationCode()) return;
    
    setIsSubmitting(true);
    setErrors({});
    
    try {
      // محاكاة التحقق من الرمز
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // في بيئة الإنتاج، سيتم التحقق من صحة الرمز
      // هنا نقوم بمحاكاة العملية
      
      setSuccessMessage('تم التحقق من الرمز بنجاح');
      setStep(3);
      
    } catch (error) {
      console.error('خطأ في التحقق من الرمز:', error);
      setErrors({ submit: 'حدث خطأ أثناء التحقق من الرمز. يرجى المحاولة مرة أخرى.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // تعيين كلمة المرور الجديدة
  const handleResetPassword = async (e) => {
    e.preventDefault();
    
    if (!validateNewPassword()) return;
    
    setIsSubmitting(true);
    setErrors({});
    
    try {
      // محاكاة تعيين كلمة المرور الجديدة
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // في بيئة الإنتاج، سيتم تحديث كلمة المرور في قاعدة البيانات
      // هنا نقوم بمحاكاة العملية
      
      setSuccessMessage('تم تعيين كلمة المرور الجديدة بنجاح! جاري تحويلك إلى صفحة تسجيل الدخول...');
      
      // إعادة توجيه المستخدم بعد تعيين كلمة المرور بنجاح
      setTimeout(() => {
        router.push('/login');
      }, 3000);
      
    } catch (error) {
      console.error('خطأ في تعيين كلمة المرور:', error);
      setErrors({ submit: 'حدث خطأ أثناء تعيين كلمة المرور. يرجى المحاولة مرة أخرى.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div style={containerStyle} dir={direction}>
      <div style={formContainerStyle}>
        <h1 style={titleStyle}>{t('auth.forgotPassword.title', 'استعادة كلمة المرور')}</h1>
        
        {successMessage && (
          <div style={successMessageStyle}>
            {successMessage}
          </div>
        )}
        
        {errors.submit && (
          <div style={errorMessageStyle}>
            {errors.submit}
          </div>
        )}
        
        {/* الخطوة 1: إدخال البريد الإلكتروني */}
        {step === 1 && (
          <form onSubmit={handleSendVerificationCode} style={formStyle}>
            <div style={stepIndicatorStyle}>
              <div style={activeStepStyle}>1</div>
              <div style={stepLineStyle}></div>
              <div style={inactiveStepStyle}>2</div>
              <div style={stepLineStyle}></div>
              <div style={inactiveStepStyle}>3</div>
            </div>
            
            <p style={stepDescriptionStyle}>
              {t('auth.forgotPassword.subtitle', 'أدخل بريدك الإلكتروني لاستلام رمز التحقق')}
            </p>
            
            <div style={formGroupStyle}>
              <label htmlFor="email" style={labelStyle}>{t('common.labels.email', 'البريد الإلكتروني')}</label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  if (errors.email) setErrors({});
                }}
                style={{
                  ...inputStyle,
                  ...(errors.email ? inputErrorStyle : {})
                }}
                placeholder={t('auth.forgotPassword.emailPlaceholder', 'أدخل بريدك الإلكتروني')}
                disabled={isSubmitting}
              />
              {errors.email && <p style={fieldErrorStyle}>{errors.email}</p>}
            </div>
            
            <button 
              type="submit" 
              style={{
                ...buttonStyle,
                ...(isSubmitting ? buttonDisabledStyle : {})
              }}
              disabled={isSubmitting}
            >
              {isSubmitting ? t('common.messages.processing', 'جاري الإرسال...') : t('auth.forgotPassword.resetButton', 'إرسال رمز التحقق')}
            </button>
            
            <div style={backLinkContainerStyle}>
              <a href="/login" style={backLinkStyle}>{t('auth.forgotPassword.backToLogin', 'العودة إلى تسجيل الدخول')}</a>
            </div>
          </form>
        )}
        
        {/* الخطوة 2: إدخال رمز التحقق */}
        {step === 2 && (
          <form onSubmit={handleVerifyCode} style={formStyle}>
            <div style={stepIndicatorStyle}>
              <div style={completedStepStyle}>1</div>
              <div style={stepLineStyle}></div>
              <div style={activeStepStyle}>2</div>
              <div style={stepLineStyle}></div>
              <div style={inactiveStepStyle}>3</div>
            </div>
            
            <p style={stepDescriptionStyle}>
              أدخل رمز التحقق المرسل إلى بريدك الإلكتروني
            </p>
            
            <div style={formGroupStyle}>
              <label htmlFor="verificationCode" style={labelStyle}>رمز التحقق</label>
              <input
                type="text"
                id="verificationCode"
                value={verificationCode}
                onChange={(e) => {
                  setVerificationCode(e.target.value.replace(/[^0-9]/g, ''));
                  if (errors.verificationCode) setErrors({});
                }}
                style={{
                  ...inputStyle,
                  ...codeInputStyle,
                  ...(errors.verificationCode ? inputErrorStyle : {})
                }}
                placeholder="أدخل رمز التحقق المكون من 6 أرقام"
                maxLength={6}
                disabled={isSubmitting}
              />
              {errors.verificationCode && <p style={fieldErrorStyle}>{errors.verificationCode}</p>}
            </div>
            
            <button 
              type="submit" 
              style={{
                ...buttonStyle,
                ...(isSubmitting ? buttonDisabledStyle : {})
              }}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'جاري التحقق...' : 'التحقق من الرمز'}
            </button>
            
            <div style={backLinkContainerStyle}>
              <button 
                type="button" 
                onClick={() => setStep(1)} 
                style={backButtonStyle}
              >
                العودة للخطوة السابقة
              </button>
            </div>
          </form>
        )}
        
        {/* الخطوة 3: تعيين كلمة المرور الجديدة */}
        {step === 3 && (
          <form onSubmit={handleResetPassword} style={formStyle}>
            <div style={stepIndicatorStyle}>
              <div style={completedStepStyle}>1</div>
              <div style={stepLineStyle}></div>
              <div style={completedStepStyle}>2</div>
              <div style={stepLineStyle}></div>
              <div style={activeStepStyle}>3</div>
            </div>
            
            <p style={stepDescriptionStyle}>
              أدخل كلمة المرور الجديدة
            </p>
            
            <div style={formGroupStyle}>
              <label htmlFor="newPassword" style={labelStyle}>كلمة المرور الجديدة</label>
              <input
                type="password"
                id="newPassword"
                value={newPassword}
                onChange={(e) => {
                  setNewPassword(e.target.value);
                  if (errors.newPassword) setErrors({});
                }}
                style={{
                  ...inputStyle,
                  ...(errors.newPassword ? inputErrorStyle : {})
                }}
                placeholder="أدخل كلمة المرور الجديدة"
                disabled={isSubmitting}
              />
              {errors.newPassword && <p style={fieldErrorStyle}>{errors.newPassword}</p>}
            </div>
            
            <div style={formGroupStyle}>
              <label htmlFor="confirmNewPassword" style={labelStyle}>تأكيد كلمة المرور الجديدة</label>
              <input
                type="password"
                id="confirmNewPassword"
                value={confirmNewPassword}
                onChange={(e) => {
                  setConfirmNewPassword(e.target.value);
                  if (errors.confirmNewPassword) setErrors({});
                }}
                style={{
                  ...inputStyle,
                  ...(errors.confirmNewPassword ? inputErrorStyle : {})
                }}
                placeholder="أعد إدخال كلمة المرور الجديدة"
                disabled={isSubmitting}
              />
              {errors.confirmNewPassword && <p style={fieldErrorStyle}>{errors.confirmNewPassword}</p>}
            </div>
            
            <button 
              type="submit" 
              style={{
                ...buttonStyle,
                ...(isSubmitting ? buttonDisabledStyle : {})
              }}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'جاري الحفظ...' : 'تعيين كلمة المرور الجديدة'}
            </button>
            
            <div style={backLinkContainerStyle}>
              <button 
                type="button" 
                onClick={() => setStep(2)} 
                style={backButtonStyle}
              >
                العودة للخطوة السابقة
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}

// الألوان
const aliexpressOrange = '#ff4747';
const aliexpressLightOrange = '#ff6e6e';
const aliexpressDarkOrange = '#e53935';

// الأنماط
const containerStyle = {
  maxWidth: '1200px',
  margin: '0 auto',
  padding: '20px',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
};

const formContainerStyle = {
  backgroundColor: 'white',
  borderRadius: '8px',
  boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
  padding: '30px',
  width: '100%',
  maxWidth: '500px',
};

const titleStyle = {
  fontSize: '1.8rem',
  color: '#333',
  marginBottom: '25px',
  textAlign: 'center',
};

const formStyle = {
  width: '100%',
};

const stepIndicatorStyle = {
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  marginBottom: '25px',
};

const stepStyle = {
  width: '35px',
  height: '35px',
  borderRadius: '50%',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  fontWeight: 'bold',
};

const activeStepStyle = {
  ...stepStyle,
  backgroundColor: aliexpressOrange,
  color: 'white',
};

const inactiveStepStyle = {
  ...stepStyle,
  backgroundColor: '#f5f5f5',
  color: '#999',
  border: '1px solid #ddd',
};

const completedStepStyle = {
  ...stepStyle,
  backgroundColor: '#4CAF50',
  color: 'white',
};

const stepLineStyle = {
  height: '2px',
  width: '50px',
  backgroundColor: '#ddd',
};

const stepDescriptionStyle = {
  textAlign: 'center',
  color: '#666',
  marginBottom: '25px',
};

const formGroupStyle = {
  marginBottom: '20px',
};

const labelStyle = {
  display: 'block',
  marginBottom: '8px',
  fontSize: '0.95rem',
  color: '#333',
  fontWeight: '500',
};

const inputStyle = {
  width: '100%',
  padding: '12px 15px',
  fontSize: '1rem',
  border: '1px solid #ddd',
  borderRadius: '4px',
  transition: 'border-color 0.3s',
  outline: 'none',
};

const codeInputStyle = {
  textAlign: 'center',
  letterSpacing: '5px',
  fontSize: '1.2rem',
};

const inputErrorStyle = {
  borderColor: '#e53935',
  backgroundColor: '#fff8f8',
};

const fieldErrorStyle = {
  color: '#e53935',
  fontSize: '0.85rem',
  marginTop: '5px',
  marginBottom: '0',
};

const buttonStyle = {
  width: '100%',
  padding: '12px',
  backgroundColor: aliexpressOrange,
  color: 'white',
  border: 'none',
  borderRadius: '4px',
  fontSize: '1rem',
  fontWeight: 'bold',
  cursor: 'pointer',
  transition: 'background-color 0.3s',
};

const buttonDisabledStyle = {
  backgroundColor: '#ccc',
  cursor: 'not-allowed',
};

const backLinkContainerStyle = {
  marginTop: '20px',
  textAlign: 'center',
  fontSize: '0.95rem',
};

const backLinkStyle = {
  color: '#666',
  textDecoration: 'none',
  transition: 'color 0.3s',
};

const backButtonStyle = {
  background: 'none',
  border: 'none',
  color: '#666',
  cursor: 'pointer',
  fontSize: '0.95rem',
  padding: '5px 10px',
  textDecoration: 'underline',
};

const successMessageStyle = {
  backgroundColor: '#e8f5e9',
  color: '#2e7d32',
  padding: '12px 15px',
  borderRadius: '4px',
  marginBottom: '20px',
  textAlign: 'center',
};

const errorMessageStyle = {
  backgroundColor: '#ffebee',
  color: '#c62828',
  padding: '12px 15px',
  borderRadius: '4px',
  marginBottom: '20px',
  textAlign: 'center',
};