'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from '../components/TranslationProvider';

export default function DashboardPage() {
  const router = useRouter();
  const { t } = useTranslation();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userEmail, setUserEmail] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  
  // بيانات تغيير كلمة المرور
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmNewPassword: ''
  });
  
  // حالة الأخطاء والنجاح
  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // التحقق من حالة تسجيل الدخول عند تحميل الصفحة
  useEffect(() => {
    const checkAuth = () => {
      const auth = localStorage.getItem('is_authenticated');
      const email = localStorage.getItem('user_email');
      
      if (auth === 'true' && email) {
        setIsAuthenticated(true);
        setUserEmail(email);
      } else {
        // إعادة توجيه المستخدم إلى صفحة تسجيل الدخول إذا لم يكن مسجلاً
        router.push('/login');
      }
      
      setIsLoading(false);
    };
    
    checkAuth();
  }, [router]);
  
  // وظيفة تسجيل الخروج
  const handleLogout = () => {
    localStorage.removeItem('is_authenticated');
    router.push('/login');
  };
  
  // وظيفة تغيير قيم حقول تغيير كلمة المرور
  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData({
      ...passwordData,
      [name]: value
    });
    
    // مسح رسالة الخطأ عند الكتابة
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };
  
  // التحقق من صحة بيانات تغيير كلمة المرور
  const validatePasswordForm = () => {
    const newErrors = {};
    
    // التحقق من كلمة المرور الحالية
    if (!passwordData.currentPassword) {
      newErrors.currentPassword = 'كلمة المرور الحالية مطلوبة';
    }
    
    // التحقق من كلمة المرور الجديدة
    if (!passwordData.newPassword) {
      newErrors.newPassword = 'كلمة المرور الجديدة مطلوبة';
    } else if (passwordData.newPassword.length < 6) {
      newErrors.newPassword = 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل';
    }
    
    // التحقق من تطابق كلمة المرور الجديدة
    if (!passwordData.confirmNewPassword) {
      newErrors.confirmNewPassword = 'تأكيد كلمة المرور الجديدة مطلوب';
    } else if (passwordData.newPassword !== passwordData.confirmNewPassword) {
      newErrors.confirmNewPassword = 'كلمة المرور الجديدة غير متطابقة';
    }
    
    return newErrors;
  };
  
  // وظيفة تغيير كلمة المرور
  const handleChangePassword = async (e) => {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    const formErrors = validatePasswordForm();
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }
    
    setIsSubmitting(true);
    setSuccessMessage('');
    
    try {
      // في بيئة الإنتاج، ستقوم بإرسال البيانات إلى الخادم
      // هنا نقوم بمحاكاة عملية تغيير كلمة المرور
      
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // عرض رسالة نجاح
      setSuccessMessage('تم تغيير كلمة المرور بنجاح!');
      
      // إعادة تعيين حقول النموذج
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmNewPassword: ''
      });
      
    } catch (error) {
      console.error('خطأ في تغيير كلمة المرور:', error);
      setErrors({
        submit: 'حدث خطأ أثناء تغيير كلمة المرور. يرجى المحاولة مرة أخرى.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // عرض رسالة التحميل
  if (isLoading) {
    return (
      <div style={containerStyle}>
        <div style={loadingStyle}>{t('common.labels.loading')}</div>
      </div>
    );
  }
  
  // عرض لوحة التحكم إذا كان المستخدم مسجل الدخول
  return (
    <div style={containerStyle}>
      <div style={dashboardContainerStyle}>
        <div style={dashboardHeaderStyle}>
          <h1 style={titleStyle}>{t('dashboard.title')}</h1>
          <div style={headerButtonsStyle}>
            <a href="/profile" style={headerLinkStyle}>
              {t('navigation.profile')}
            </a>
            <a href="/settings" style={headerLinkStyle}>
              {t('navigation.settings')}
            </a>
            <button onClick={handleLogout} style={logoutButtonStyle}>
              {t('navigation.logout')}
            </button>
          </div>
        </div>
        
        <div style={sectionStyle}>
          <h2 style={sectionTitleStyle}>{t('dashboard.accountInfo')}</h2>
          <div style={userInfoStyle}>
            <div style={userInfoItemStyle}>
              <span style={userInfoLabelStyle}>{t('common.labels.email')}:</span>
              <span style={userInfoValueStyle}>{userEmail}</span>
            </div>
          </div>
        </div>
        
        <div style={sectionStyle}>
          <h2 style={sectionTitleStyle}>{t('admin.profile.changePassword')}</h2>
          
          {successMessage && (
            <div style={successMessageStyle}>
              {t('admin.profile.passwordChanged')}
            </div>
          )}
          
          {errors.submit && (
            <div style={errorMessageStyle}>
              {errors.submit}
            </div>
          )}
          
          <form onSubmit={handleChangePassword} style={formStyle}>
            <div style={formGroupStyle}>
              <label htmlFor="currentPassword" style={labelStyle}>{t('admin.profile.currentPasswordPlaceholder')}</label>
              <input
                type="password"
                id="currentPassword"
                name="currentPassword"
                value={passwordData.currentPassword}
                onChange={handlePasswordChange}
                style={{
                  ...inputStyle,
                  ...(errors.currentPassword ? inputErrorStyle : {})
                }}
                placeholder={t('admin.profile.currentPasswordPlaceholder')}
                disabled={isSubmitting}
              />
              {errors.currentPassword && <p style={fieldErrorStyle}>{errors.currentPassword}</p>}
            </div>
            
            <div style={formGroupStyle}>
              <label htmlFor="newPassword" style={labelStyle}>{t('admin.profile.newPasswordPlaceholder')}</label>
              <input
                type="password"
                id="newPassword"
                name="newPassword"
                value={passwordData.newPassword}
                onChange={handlePasswordChange}
                style={{
                  ...inputStyle,
                  ...(errors.newPassword ? inputErrorStyle : {})
                }}
                placeholder={t('admin.profile.newPasswordPlaceholder')}
                disabled={isSubmitting}
              />
              {errors.newPassword && <p style={fieldErrorStyle}>{errors.newPassword}</p>}
            </div>
            
            <div style={formGroupStyle}>
              <label htmlFor="confirmNewPassword" style={labelStyle}>{t('admin.profile.confirmPasswordPlaceholder')}</label>
              <input
                type="password"
                id="confirmNewPassword"
                name="confirmNewPassword"
                value={passwordData.confirmNewPassword}
                onChange={handlePasswordChange}
                style={{
                  ...inputStyle,
                  ...(errors.confirmNewPassword ? inputErrorStyle : {})
                }}
                placeholder={t('admin.profile.confirmPasswordPlaceholder')}
                disabled={isSubmitting}
              />
              {errors.confirmNewPassword && <p style={fieldErrorStyle}>{errors.confirmNewPassword}</p>}
            </div>
            
            <button 
              type="submit" 
              style={{
                ...buttonStyle,
                ...(isSubmitting ? buttonDisabledStyle : {})
              }}
              disabled={isSubmitting}
            >
              {isSubmitting ? t('common.messages.processing') : t('admin.profile.saveChanges')}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}

// الألوان
const aliexpressOrange = '#ff4747';
const aliexpressLightOrange = '#ff6e6e';
const aliexpressDarkOrange = '#e53935';

// الأنماط
const containerStyle = {
  maxWidth: '1200px',
  margin: '0 auto',
  padding: '20px',
};

const loadingStyle = {
  textAlign: 'center',
  padding: '50px',
  fontSize: '1.2rem',
  color: '#666',
};

const dashboardContainerStyle = {
  backgroundColor: 'white',
  borderRadius: '8px',
  boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
  padding: '30px',
  width: '100%',
};

const dashboardHeaderStyle = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: '30px',
  borderBottom: '1px solid #eee',
  paddingBottom: '20px',
};

const titleStyle = {
  fontSize: '1.8rem',
  color: '#333',
  margin: '0',
};

const headerButtonsStyle = {
  display: 'flex',
  gap: '10px',
  alignItems: 'center',
};

const headerLinkStyle = {
  backgroundColor: '#f5f5f5',
  color: '#333',
  border: '1px solid #ddd',
  borderRadius: '4px',
  padding: '8px 15px',
  fontSize: '0.9rem',
  cursor: 'pointer',
  transition: 'all 0.3s',
  textDecoration: 'none',
};

const logoutButtonStyle = {
  backgroundColor: '#f5f5f5',
  color: '#333',
  border: '1px solid #ddd',
  borderRadius: '4px',
  padding: '8px 15px',
  fontSize: '0.9rem',
  cursor: 'pointer',
  transition: 'all 0.3s',
};

const sectionStyle = {
  marginBottom: '30px',
  padding: '20px',
  backgroundColor: '#f9f9f9',
  borderRadius: '8px',
};

const sectionTitleStyle = {
  fontSize: '1.3rem',
  color: '#333',
  marginBottom: '20px',
  paddingBottom: '10px',
  borderBottom: '1px solid #eee',
};

const userInfoStyle = {
  padding: '10px 0',
};

const userInfoItemStyle = {
  marginBottom: '15px',
  display: 'flex',
  alignItems: 'center',
};

const userInfoLabelStyle = {
  fontWeight: 'bold',
  marginLeft: '10px',
  minWidth: '150px',
};

const userInfoValueStyle = {
  color: '#333',
};

const formStyle = {
  width: '100%',
  maxWidth: '500px',
};

const formGroupStyle = {
  marginBottom: '20px',
};

const labelStyle = {
  display: 'block',
  marginBottom: '8px',
  fontSize: '0.95rem',
  color: '#333',
  fontWeight: '500',
};

const inputStyle = {
  width: '100%',
  padding: '12px 15px',
  fontSize: '1rem',
  border: '1px solid #ddd',
  borderRadius: '4px',
  transition: 'border-color 0.3s',
  outline: 'none',
};

const inputErrorStyle = {
  borderColor: '#e53935',
  backgroundColor: '#fff8f8',
};

const fieldErrorStyle = {
  color: '#e53935',
  fontSize: '0.85rem',
  marginTop: '5px',
  marginBottom: '0',
};

const buttonStyle = {
  padding: '12px 20px',
  backgroundColor: aliexpressOrange,
  color: 'white',
  border: 'none',
  borderRadius: '4px',
  fontSize: '1rem',
  fontWeight: 'bold',
  cursor: 'pointer',
  transition: 'background-color 0.3s',
};

const buttonDisabledStyle = {
  backgroundColor: '#ccc',
  cursor: 'not-allowed',
};

const successMessageStyle = {
  backgroundColor: '#e8f5e9',
  color: '#2e7d32',
  padding: '12px 15px',
  borderRadius: '4px',
  marginBottom: '20px',
};

const errorMessageStyle = {
  backgroundColor: '#ffebee',
  color: '#c62828',
  padding: '12px 15px',
  borderRadius: '4px',
  marginBottom: '20px',
};