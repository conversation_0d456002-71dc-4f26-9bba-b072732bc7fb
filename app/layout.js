'use client';

import { useState, useEffect } from 'react';
import './globals.css';
import './styles/footer.css';
import { TranslationProvider } from './components/TranslationProvider';
import MainContent from './components/MainContent';
import Footer from './components/Footer';
import GoogleAnalytics from './components/GoogleAnalytics';

export default function RootLayout({ children }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userEmail, setUserEmail] = useState('');
  const [isClient, setIsClient] = useState(false);
  
  // التحقق من حالة تسجيل الدخول عند تحميل الصفحة
  useEffect(() => {
    setIsClient(true);
    
    const checkAuth = () => {
      const auth = localStorage.getItem('is_authenticated');
      const email = localStorage.getItem('user_email');
      
      if (auth === 'true' && email) {
        setIsAuthenticated(true);
        setUserEmail(email);
      } else {
        setIsAuthenticated(false);
        setUserEmail('');
      }
    };
    
    if (isClient) {
      checkAuth();
    }
  }, [isClient]);
  
  // تسجيل الخروج
  const handleLogout = () => {
    localStorage.removeItem('is_authenticated');
    localStorage.removeItem('user_email');
    setIsAuthenticated(false);
    setUserEmail('');
    window.location.href = '/';
  };
  
  return (
    <html lang={isClient ? document.documentElement.lang : "ar"} dir={isClient ? document.documentElement.dir : "rtl"}>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <style>
          {`
            /* تأكد من أن القوائم المنسدلة تظهر فوق جميع العناصر */
            .dropdown-menu {
              z-index: 9999 !important;
            }
            
            /* منع الاهتزاز عند تغيير اللغة */
            html {
              visibility: hidden;
            }
            
            /* تحسين تجربة المستخدم عند تغيير اللغة */
            body {
              transition: opacity 0.2s ease;
            }
          `}
        </style>
      </head>
      <body>
        <GoogleAnalytics />
        <TranslationProvider>
          <MainContent 
            isAuthenticated={isAuthenticated} 
            userEmail={userEmail} 
            handleLogout={handleLogout}
          >
            {children}
            <Footer />
          </MainContent>
        </TranslationProvider>
      </body>
    </html>
  );
}