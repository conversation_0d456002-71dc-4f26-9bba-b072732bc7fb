'use client';

import { useTranslation } from './TranslationProvider';
import { useState, useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';
import LanguageSwitcher from './LanguageSwitcher';
import CurrencySwitcher from './CurrencySwitcher';

export default function MainContent({ children, isAuthenticated, userEmail, handleLogout }) {
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const userMenuRef = useRef(null);
  const [isClient, setIsClient] = useState(false);
  const pathname = usePathname();
  
  // التحقق مما إذا كان المستخدم في صفحة الإدارة
  const isAdminPage = pathname?.startsWith('/admin-panel-95');
  
  // التحقق من حالة تسجيل الدخول عند تحميل الصفحة
  useEffect(() => {
    setIsClient(true);
    
    // إغلاق القائمة عند النقر خارجها
    const handleClickOutside = (event) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
        setIsUserMenuOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // استخدام مكون الترجمة
  const { t, language, direction } = useTranslation();
  
  // تبديل حالة قائمة المستخدم
  const toggleUserMenu = () => {
    setIsUserMenuOpen(!isUserMenuOpen);
  };
  
  // أنماط CSS محسنة بالألوان الجديدة
  const headerStyle = {
    background: 'linear-gradient(135deg, #4A90E2 0%, #FF8C42 100%)',
    boxShadow: '0 4px 20px rgba(74, 144, 226, 0.3)',
    padding: '1rem 0',
    position: 'sticky',
    top: 0,
    zIndex: 1000,
    backdropFilter: 'blur(10px)',
  };
  
  const containerStyle = {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '0 1rem',
  };
  
  const headerContentStyle = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  };
  
  const logoStyle = {
    display: 'flex',
    alignItems: 'center',
    textDecoration: 'none',
    color: 'white',
    transition: 'transform 0.3s ease',
  };
  
  const logoImageStyle = {
    width: '40px',
    height: '40px',
    marginRight: '0.5rem',
    background: 'linear-gradient(135deg, #FFE5D4, #E3F2FD)',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '1.5rem',
    boxShadow: '0 2px 8px rgba(74, 144, 226, 0.2)',
    border: '2px solid rgba(255, 255, 255, 0.3)',
  };
  
  const logoTextStyle = {
    fontSize: '1.2rem',
    fontWeight: 'bold',
    color: 'white',
    textShadow: '0 1px 3px rgba(0, 0, 0, 0.3)',
  };
  
  const navStyle = {
    display: 'flex',
    alignItems: 'center',
  };
  
  const navListStyle = {
    display: 'flex',
    listStyle: 'none',
    margin: 0,
    padding: 0,
  };
  
  const navItemStyle = {
    margin: '0 0.5rem',
  };
  
  const navLinkStyle = {
    textDecoration: 'none',
    color: 'white',
    padding: '0.8rem 1.2rem',
    borderRadius: '25px',
    transition: 'all 0.3s ease',
    fontWeight: '500',
    textShadow: '0 1px 2px rgba(0, 0, 0, 0.2)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    backdropFilter: 'blur(5px)',
  };
  
  const menuContainerStyle = {
    display: 'flex',
    alignItems: 'center',
    marginLeft: '1rem',
  };
  
  const langMenuStyle = {
    position: 'relative',
    marginRight: '1rem',
  };
  
  const userMenuStyle = {
    position: 'relative',
  };
  
  const userButtonStyle = {
    display: 'flex',
    alignItems: 'center',
    background: 'rgba(255, 255, 255, 0.1)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    cursor: 'pointer',
    padding: '0.8rem 1rem',
    borderRadius: '25px',
    transition: 'all 0.3s ease',
    color: 'white',
    fontWeight: '500',
    backdropFilter: 'blur(5px)',
  };
  
  const userIconStyle = {
    width: '30px',
    height: '30px',
    background: 'linear-gradient(135deg, #FFE5D4, #E3F2FD)',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: '0.5rem',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
  };
  
  const dropdownMenuStyle = {
    position: 'absolute',
    top: '100%',
    right: 0,
    backgroundColor: 'white',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
    borderRadius: '4px',
    minWidth: '150px',
    display: isUserMenuOpen ? 'block' : 'none',
    zIndex: 9999, // قيمة عالية جدًا لضمان ظهور القائمة فوق جميع العناصر
    maxHeight: '300px',
    overflowY: 'auto',
  };
  
  const dropdownItemStyle = {
    display: 'block',
    padding: '10px 15px',
    textDecoration: 'none',
    color: '#333',
    borderBottom: '1px solid #eee',
    transition: 'background-color 0.3s',
  };
  
  const mainStyle = {
    minHeight: 'calc(100vh - 100px)', // ارتفاع الصفحة ناقص ارتفاع الهيدر
    padding: '2rem 0',
  };
  
  // نمط خاص لصفحات الإدارة بدون هوامش أو حشو
  const adminMainStyle = {
    minHeight: '100vh',
    padding: 0,
    width: '100%',
  };
  
  return (
    <>
      <header style={headerStyle}>
        <div style={containerStyle}>
          <div style={headerContentStyle}>
            {/* الشعار - يظهر دائماً */}
            <div style={logoStyle}>
              <a href="/" style={logoStyle}>
                <div style={logoImageStyle}>
                  <span>🛒</span>
                </div>
                <div style={logoTextStyle}>
                  {t('common.siteTitle')}
                </div>
              </a>
            </div>
            
            {/* قائمة التنقل - تظهر فقط في الصفحات العادية */}
            {!isAdminPage && (
              <nav style={navStyle}>
                <ul style={navListStyle}>
                  <li style={navItemStyle}><a href="/search" style={navLinkStyle}>{t('navigation.search')}</a></li>
                  <li style={navItemStyle}><a href="/commission-discounts" style={navLinkStyle}>{t('common.commissionDiscounts')}</a></li>
                  <li style={navItemStyle}><a href="/offers" style={navLinkStyle}>{t('offers.title')}</a></li>
                </ul>
              </nav>
            )}
            
            {/* قائمة اللغات والعملات - تظهر دائماً */}
            <div style={menuContainerStyle}>
              <div style={langMenuStyle}>
                <LanguageSwitcher />
              </div>
              <div style={langMenuStyle}>
                <CurrencySwitcher />
              </div>
              
              {/* قائمة المستخدم - تظهر فقط في الصفحات العادية */}
              {!isAdminPage && (
                <div style={userMenuStyle} ref={userMenuRef}>
                  <button style={userButtonStyle} onClick={toggleUserMenu}>
                    <div style={userIconStyle}>
                      <span>👤</span>
                    </div>
                    <span>{isAuthenticated ? userEmail : t('auth.login.title')}</span>
                  </button>
                  
                  <div style={dropdownMenuStyle} className="dropdown-menu">
                    {isAuthenticated ? (
                      <>
                        <a href="/profile" style={dropdownItemStyle}>{t('navigation.profile')}</a>
                        <a href="/settings" style={dropdownItemStyle}>{t('navigation.settings')}</a>
                        <a href="#" style={dropdownItemStyle} onClick={handleLogout}>{t('navigation.logout')}</a>
                      </>
                    ) : (
                      <>
                        <a href="/login" style={dropdownItemStyle}>{t('navigation.login')}</a>
                        <a href="/register" style={dropdownItemStyle}>{t('navigation.register')}</a>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>
      
      <main style={isAdminPage ? adminMainStyle : mainStyle}>
        {isAdminPage ? (
          children
        ) : (
          <div style={containerStyle}>
            {children}
          </div>
        )}
      </main>
    </>
  );
}