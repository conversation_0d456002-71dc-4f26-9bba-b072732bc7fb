'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from './TranslationProvider';

// قائمة اللغات المدعومة في الموقع
const languages = [
  { code: 'ar', name: 'العربية', dir: 'rtl' },
  { code: 'en', name: 'English', dir: 'ltr' },
  { code: 'fr', name: 'Français', dir: 'ltr' },
  { code: 'es', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', dir: 'ltr' },
  { code: 'uk', name: 'Українська', dir: 'ltr' },
];

export default function LanguageSwitcher() {
  const { language: contextLanguage } = useTranslation();
  const [currentLang, setCurrentLang] = useState('ar');
  const [isOpen, setIsOpen] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // تحديد ما إذا كنا في بيئة المتصفح
  useEffect(() => {
    setIsClient(true);
  }, []);

  // تحميل اللغة المحفوظة عند بدء التشغيل
  useEffect(() => {
    if (isClient) {
      const savedLang = localStorage.getItem('language') || 'ar';
      setCurrentLang(savedLang);
      
      // تعيين اتجاه الصفحة بناءً على اللغة
      const langObj = languages.find(lang => lang.code === savedLang);
      if (langObj) {
        document.documentElement.dir = langObj.dir;
        document.documentElement.lang = langObj.code;
      }
    }
  }, [isClient]);

  // تحديث اللغة الحالية عندما تتغير في السياق
  useEffect(() => {
    if (contextLanguage && contextLanguage !== currentLang) {
      setCurrentLang(contextLanguage);
    }
  }, [contextLanguage]);

  // تغيير اللغة
  const changeLanguage = (langCode) => {
    const langObj = languages.find(lang => lang.code === langCode);
    if (langObj) {
      setCurrentLang(langCode);
      localStorage.setItem('language', langCode);
      document.documentElement.dir = langObj.dir;
      document.documentElement.lang = langObj.code;
      
      // إرسال حدث تغيير اللغة للمكونات الأخرى
      const event = new CustomEvent('languageChange', { 
        detail: { language: langCode, direction: langObj.dir } 
      });
      window.dispatchEvent(event);
      
      setIsOpen(false);
    }
  };

  // الحصول على اسم اللغة الحالية
  const getCurrentLanguageName = () => {
    const langObj = languages.find(lang => lang.code === currentLang);
    return langObj ? langObj.name : 'العربية';
  };

  // الأنماط
  const aliexpressOrange = '#ff4747';

  const langSwitcherStyle = {
    position: 'relative',
    display: 'inline-block',
  };

  const toggleButtonStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '10px 16px',
    background: 'rgba(255, 255, 255, 0.15)',
    border: '1px solid rgba(255, 255, 255, 0.3)',
    borderRadius: '25px',
    cursor: 'pointer',
    fontSize: '0.9rem',
    color: 'white',
    fontWeight: '500',
    transition: 'all 0.3s ease',
    backdropFilter: 'blur(10px)',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
  };

  const dropdownStyle = {
    position: 'absolute',
    top: '100%',
    right: isClient && document.documentElement.dir === 'rtl' ? '0' : 'auto',
    left: isClient && document.documentElement.dir === 'rtl' ? 'auto' : '0',
    background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95))',
    boxShadow: '0 8px 32px rgba(74, 144, 226, 0.2)',
    borderRadius: '15px',
    minWidth: '180px',
    maxHeight: '300px',
    overflowY: 'auto',
    zIndex: 9999, // قيمة عالية جدًا لضمان ظهور القائمة فوق جميع العناصر
    display: isOpen ? 'block' : 'none',
    backdropFilter: 'blur(20px)',
    border: '1px solid rgba(255, 255, 255, 0.3)',
  };

  const langItemStyle = {
    padding: '15px 20px',
    cursor: 'pointer',
    borderBottom: '1px solid rgba(74, 144, 226, 0.1)',
    transition: 'all 0.3s ease',
    borderRadius: '8px',
    margin: '4px 8px',
    color: '#2C3E50',
    fontWeight: '500',
  };

  const activeLangStyle = {
    ...langItemStyle,
    background: 'linear-gradient(135deg, rgba(74, 144, 226, 0.1), rgba(255, 140, 66, 0.1))',
    color: '#4A90E2',
    fontWeight: 'bold',
    transform: 'scale(1.02)',
    boxShadow: '0 2px 8px rgba(74, 144, 226, 0.2)',
  };

  return (
    <div style={langSwitcherStyle}>
      <button 
        style={toggleButtonStyle} 
        onClick={() => setIsOpen(!isOpen)}
        aria-label="تغيير اللغة"
        aria-expanded={isOpen}
      >
        <span>🌐</span>
        <span>{getCurrentLanguageName()}</span>
      </button>
      
      <div style={dropdownStyle} role="menu" aria-hidden={!isOpen}>
        {languages.map((lang) => (
          <div
            key={lang.code}
            style={lang.code === currentLang ? activeLangStyle : langItemStyle}
            onClick={() => changeLanguage(lang.code)}
            role="menuitem"
            tabIndex={isOpen ? 0 : -1}
          >
            {lang.name}
          </div>
        ))}
      </div>
    </div>
  );
}