"use client";

import { createContext, useContext, useState, useEffect } from "react";
import ar from "../translations/ar";
import en from "../translations/en";
import fr from "../translations/fr";
import es from "../translations/es";
import uk from "../translations/uk";

// قاموس الترجمات
const translations = {
  ar,
  en,
  fr,
  es,
  uk,
};

// إنشاء سياق الترجمة
const TranslationContext = createContext();

export function TranslationProvider({ children }) {
  const [language, setLanguage] = useState("ar");
  const [direction, setDirection] = useState("rtl");
  const [isClient, setIsClient] = useState(false);

  // تحديد ما إذا كنا في بيئة المتصفح
  useEffect(() => {
    setIsClient(true);
  }, []);

  // تحميل اللغة المحفوظة عند بدء التشغيل
  useEffect(() => {
    if (isClient) {
      try {
        const savedLang = localStorage.getItem("language") || "ar";

        // التحقق من أن اللغة المحفوظة هي لغة مدعومة
        if (!translations[savedLang]) {
          console.warn(
            `Language ${savedLang} is not supported, falling back to Arabic`,
          );
          setLanguage("ar");
          setDirection("rtl");
          localStorage.setItem("language", "ar");
        } else {
          setLanguage(savedLang);
          setDirection(savedLang === "ar" ? "rtl" : "ltr");
        }

        // تعيين اتجاه الصفحة بناءً على اللغة
        document.documentElement.dir = savedLang === "ar" ? "rtl" : "ltr";
        document.documentElement.lang = savedLang;

        // منع الاهتزاز عند تغيير اللغة
        document.documentElement.style.visibility = "visible";

        console.log(
          "Language loaded:",
          savedLang,
          "Translations available:",
          Object.keys(translations),
        );
      } catch (error) {
        console.error("Error loading language settings:", error);
      }

      // الاستماع لتغييرات اللغة
      const handleLanguageChange = (event) => {
        const newLanguage =
          event.detail?.language || localStorage.getItem("language") || "ar";
        const newDirection =
          event.detail?.direction || (newLanguage === "ar" ? "rtl" : "ltr");

        // التحقق من أن اللغة الجديدة هي لغة مدعومة
        if (!translations[newLanguage]) {
          console.warn(
            `Language ${newLanguage} is not supported, falling back to Arabic`,
          );
          setLanguage("ar");
          setDirection("rtl");
          localStorage.setItem("language", "ar");
          document.documentElement.dir = "rtl";
          document.documentElement.lang = "ar";
        } else {
          setLanguage(newLanguage);
          setDirection(newDirection);

          // تحديث اتجاه الصفحة عند تغيير اللغة
          document.documentElement.dir = newDirection;
          document.documentElement.lang = newLanguage;

          console.log(
            "Language changed to:",
            newLanguage,
            "Direction:",
            newDirection,
          );
        }
      };

      window.addEventListener("languageChange", handleLanguageChange);
      return () => {
        window.removeEventListener("languageChange", handleLanguageChange);
      };
    }
  }, [isClient]);

  // وظيفة الترجمة
  const t = (key) => {
    if (!isClient) return key;

    try {
      // تقسيم المفتاح إلى أجزاء (مثال: "common.buttons.submit")
      const parts = key.split(".");
      let translation = translations[language];

      if (!translation) {
        console.warn(`Translation not found for language: ${language}`);
        translation = translations["ar"]; // استخدام اللغة العربية كلغة احتياطية
      }

      // البحث عن الترجمة في الهيكل المتداخل
      for (const part of parts) {
        if (translation && translation[part] !== undefined) {
          translation = translation[part];
        } else {
          // إذا لم يتم العثور على الترجمة، نعود إلى المفتاح الأصلي
          console.warn(
            `Translation key not found: ${key} for language: ${language}`,
          );
          return key;
        }
      }

      return translation;
    } catch (error) {
      console.error("Error in translation function:", error);
      return key;
    }
  };

  // وظيفة للتنسيق مع القيم
  const formatWithValues = (text, values) => {
    if (!text) return "";
    if (!values) return text;

    return Object.entries(values).reduce((result, [key, value]) => {
      const regex = new RegExp(`{{${key}}}`, "g");
      return result.replace(regex, value);
    }, text);
  };

  // وظيفة مختصرة للترجمة مع القيم
  const tVal = (key, values) => {
    return formatWithValues(t(key), values);
  };

  return (
    <TranslationContext.Provider value={{ t, tVal, language, direction }}>
      {children}
    </TranslationContext.Provider>
  );
}

// Hook لاستخدام الترجمات
export function useTranslation() {
  const context = useContext(TranslationContext);
  if (context === undefined) {
    throw new Error("useTranslation must be used within a TranslationProvider");
  }
  return context;
}
