'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from './TranslationProvider';

export default function AdminSidebar() {
  const { t, language, direction } = useTranslation();
  const [activeItem, setActiveItem] = useState('dashboard');
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // تحديد ما إذا كنا في بيئة المتصفح
  useEffect(() => {
    setIsClient(true);
  }, []);

  // قائمة عناصر القائمة الجانبية
  const menuItems = [
    {
      id: 'dashboard',
      label: t('admin.dashboard.title'),
      icon: '📊',
      link: '/admin-panel-95/dashboard'
    },
    {
      id: 'users',
      label: t('admin.users.title'),
      icon: '👥',
      link: '/admin-panel-95/users'
    },
    {
      id: 'offers',
      label: t('offers.title'),
      icon: '🏷️',
      link: '/admin-panel-95/offers'
    },
    {
      id: 'commissions',
      label: t('commissionDiscounts.title'),
      icon: '💰',
      link: '/admin-panel-95/commissions'
    },
    {
      id: 'analytics',
      label: t('admin.analytics.title'),
      icon: '📈',
      link: '/admin-panel-95/analytics'
    },
    {
      id: 'settings',
      label: t('admin.settings.title'),
      icon: '⚙️',
      link: '/admin-panel-95/settings'
    }
  ];

  // تبديل حالة القائمة (مطوية/موسعة)
  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  // الأنماط
  const sidebarStyle = {
    width: isCollapsed ? '70px' : '250px',
    height: '100vh',
    backgroundColor: '#2c3e50',
    color: 'white',
    position: 'fixed',
    top: 0,
    [direction === 'rtl' ? 'right' : 'left']: 0,
    transition: 'width 0.3s ease',
    zIndex: 100,
    overflowX: 'hidden',
    display: 'flex',
    flexDirection: 'column'
  };

  const logoContainerStyle = {
    padding: '20px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: isCollapsed ? 'center' : (direction === 'rtl' ? 'flex-end' : 'flex-start'),
    borderBottom: '1px solid #34495e'
  };

  const logoStyle = {
    fontSize: '1.5rem',
    fontWeight: 'bold',
    color: '#ff4747',
    whiteSpace: 'nowrap'
  };

  const menuContainerStyle = {
    flex: 1,
    overflowY: 'auto'
  };

  const menuItemStyle = (isActive) => ({
    padding: '15px 20px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: isCollapsed ? 'center' : (direction === 'rtl' ? 'flex-end' : 'flex-start'),
    cursor: 'pointer',
    backgroundColor: isActive ? '#34495e' : 'transparent',
    transition: 'background-color 0.3s',
    textDecoration: 'none',
    color: 'white',
    gap: '10px',
    flexDirection: direction === 'rtl' ? 'row-reverse' : 'row'
  });

  const iconStyle = {
    fontSize: '1.2rem',
    minWidth: '30px',
    display: 'flex',
    justifyContent: 'center'
  };

  const labelStyle = {
    display: isCollapsed ? 'none' : 'block',
    whiteSpace: 'nowrap'
  };

  const toggleButtonStyle = {
    padding: '15px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    backgroundColor: '#34495e',
    borderTop: '1px solid #2c3e50'
  };

  return (
    <div style={sidebarStyle}>
      <div style={logoContainerStyle}>
        {!isCollapsed && <div style={logoStyle}>Admin Panel</div>}
        {isCollapsed && <div style={logoStyle}>AP</div>}
      </div>
      
      <div style={menuContainerStyle}>
        {menuItems.map(item => (
          <a
            key={item.id}
            href={item.link}
            style={menuItemStyle(activeItem === item.id)}
            onClick={() => setActiveItem(item.id)}
          >
            <div style={iconStyle}>{item.icon}</div>
            <div style={labelStyle}>{item.label}</div>
          </a>
        ))}
      </div>
      
      <div style={toggleButtonStyle} onClick={toggleSidebar}>
        {isCollapsed ? '→' : (direction === 'rtl' ? '→' : '←')}
      </div>
    </div>
  );
}