'use client';

import { useState, useEffect } from 'react';

/**
 * مكون إدارة الطوابير
 * يقوم بإدارة المهام في الطوابير ومتابعة حالتها
 * 
 * @param {Object} props - خصائص المكون
 * @param {string} props.queueName - اسم الطابور
 * @param {Object} props.task - المهمة المراد إضافتها
 * @param {Function} props.onTaskComplete - دالة تنفذ عند اكتمال المهمة
 * @param {Function} props.onTaskError - دالة تنفذ عند حدوث خطأ في المهمة
 * @param {boolean} props.autoSubmit - إضافة المهمة تلقائيًا عند تحميل المكون
 */
export default function QueueManager({
  queueName,
  task,
  onTaskComplete,
  onTaskError,
  autoSubmit = false
}) {
  // حالة معرف المهمة
  const [taskId, setTaskId] = useState(null);
  // حالة المهمة
  const [taskStatus, setTaskStatus] = useState(null);
  // حالة التحميل
  const [isLoading, setIsLoading] = useState(false);
  // حالة الخطأ
  const [error, setError] = useState(null);
  // حالة نتيجة المهمة
  const [result, setResult] = useState(null);
  
  // إضافة المهمة تلقائيًا عند تحميل المكون
  useEffect(() => {
    if (autoSubmit && queueName && task && !taskId) {
      addTaskToQueue();
    }
  }, [autoSubmit, queueName, task]);
  
  // متابعة حالة المهمة
  useEffect(() => {
    if (taskId) {
      const checkTaskStatus = async () => {
        try {
          const response = await fetch(`/api/queue?taskId=${taskId}`);
          
          if (!response.ok) {
            throw new Error(`فشل جلب حالة المهمة: ${response.status}`);
          }
          
          const data = await response.json();
          
          if (data.success) {
            setTaskStatus(data.status.status);
            
            if (data.status.status === 'completed') {
              setResult(data.status.result);
              if (onTaskComplete) onTaskComplete(data.status.result);
              return;
            } else if (data.status.status === 'failed') {
              setError(data.status.error);
              if (onTaskError) onTaskError(data.status.error);
              return;
            }
            
            // استمرار في التحقق من حالة المهمة كل ثانية
            setTimeout(checkTaskStatus, 1000);
          } else {
            setError(data.error);
            if (onTaskError) onTaskError(data.error);
          }
        } catch (err) {
          console.error('خطأ في جلب حالة المهمة:', err);
          setError(err.message);
          if (onTaskError) onTaskError(err.message);
        }
      };
      
      checkTaskStatus();
    }
  }, [taskId]);
  
  // إضافة المهمة إلى الطابور
  const addTaskToQueue = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch('/api/queue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ queueName, task })
      });
      
      if (!response.ok) {
        throw new Error(`فشل إضافة المهمة إلى الطابور: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setTaskId(data.taskId);
        setTaskStatus('pending');
      } else {
        setError(data.error);
        if (onTaskError) onTaskError(data.error);
      }
      
      setIsLoading(false);
    } catch (err) {
      console.error('خطأ في إضافة المهمة إلى الطابور:', err);
      setError(err.message);
      setIsLoading(false);
      if (onTaskError) onTaskError(err.message);
    }
  };
  
  // إعادة تعيين المكون
  const resetTask = () => {
    setTaskId(null);
    setTaskStatus(null);
    setIsLoading(false);
    setError(null);
    setResult(null);
  };
  
  // أنماط الحاوية
  const containerStyle = {
    padding: '15px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    marginBottom: '20px'
  };
  
  // أنماط العنوان
  const titleStyle = {
    fontSize: '16px',
    fontWeight: 'bold',
    marginBottom: '10px'
  };
  
  // أنماط الحالة
  const statusStyle = {
    display: 'flex',
    alignItems: 'center',
    marginBottom: '10px'
  };
  
  // أنماط مؤشر الحالة
  const statusIndicatorStyle = {
    width: '10px',
    height: '10px',
    borderRadius: '50%',
    marginRight: '10px',
    backgroundColor: 
      taskStatus === 'completed' ? '#4caf50' :
      taskStatus === 'processing' ? '#2196f3' :
      taskStatus === 'pending' ? '#ff9800' :
      taskStatus === 'failed' ? '#f44336' : '#ccc'
  };
  
  // أنماط نص الحالة
  const statusTextStyle = {
    fontSize: '14px',
    color: '#666'
  };
  
  // أنماط الزر
  const buttonStyle = {
    backgroundColor: '#ff4747',
    color: 'white',
    border: 'none',
    padding: '8px 16px',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: '14px',
    marginRight: '10px'
  };
  
  // أنماط زر إعادة التعيين
  const resetButtonStyle = {
    ...buttonStyle,
    backgroundColor: '#666'
  };
  
  // أنماط رسالة الخطأ
  const errorStyle = {
    color: '#f44336',
    fontSize: '14px',
    marginTop: '10px'
  };
  
  return (
    <div style={containerStyle}>
      <div style={titleStyle}>
        مدير الطابور: {queueName}
      </div>
      
      {taskStatus && (
        <div style={statusStyle}>
          <div style={statusIndicatorStyle}></div>
          <div style={statusTextStyle}>
            {taskStatus === 'pending' && 'في انتظار المعالجة'}
            {taskStatus === 'processing' && 'قيد المعالجة'}
            {taskStatus === 'completed' && 'تمت المعالجة بنجاح'}
            {taskStatus === 'failed' && 'فشلت المعالجة'}
          </div>
        </div>
      )}
      
      {!taskId && (
        <button 
          style={buttonStyle} 
          onClick={addTaskToQueue} 
          disabled={isLoading}
        >
          {isLoading ? 'جاري الإضافة...' : 'إضافة المهمة إلى الطابور'}
        </button>
      )}
      
      {taskId && taskStatus === 'completed' && (
        <button 
          style={resetButtonStyle} 
          onClick={resetTask}
        >
          إعادة تعيين
        </button>
      )}
      
      {error && (
        <div style={errorStyle}>
          خطأ: {error}
        </div>
      )}
    </div>
  );
}