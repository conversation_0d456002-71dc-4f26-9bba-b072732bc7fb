'use client';

import { useEffect } from 'react';

export default function GoogleAnalytics() {
  useEffect(() => {
    // تحميل ملف Google Analytics إذا كان موجوداً
    const script = document.createElement('script');
    script.src = '/analytics.js';
    script.async = true;
    document.head.appendChild(script);

    return () => {
      // تنظيف السكريبت عند إلغاء تحميل المكون
      const existingScript = document.querySelector('script[src="/analytics.js"]');
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, []);

  return null; // هذا المكون لا يعرض أي شيء
}