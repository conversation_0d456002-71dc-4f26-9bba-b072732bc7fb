'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from './TranslationProvider';

// قائمة العملات المدعومة في الخوارزميات
const currencies = [
  { code: 'USD', name: 'دولار أمريكي', symbol: '$' },
  { code: 'EUR', name: 'يورو', symbol: '€' },
  { code: 'GBP', name: 'جنيه استرليني', symbol: '£' },
  { code: 'SAR', name: 'ريال سعودي', symbol: 'ر.س' },
  { code: 'EGP', name: 'جنيه مصري', symbol: 'ج.م' },
  { code: 'UAH', name: 'هريفنيا أوكرانية', symbol: '₴' },
];

export default function CurrencySwitcher() {
  const { direction } = useTranslation();
  const [currentCurrency, setCurrentCurrency] = useState('SAR');
  const [isOpen, setIsOpen] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // تحديد ما إذا كنا في بيئة المتصفح
  useEffect(() => {
    setIsClient(true);
  }, []);

  // تحميل العملة المحفوظة عند بدء التشغيل
  useEffect(() => {
    if (isClient) {
      const savedCurrency = localStorage.getItem('currency') || 'SAR';
      setCurrentCurrency(savedCurrency);
    }
  }, [isClient]);

  // تغيير العملة
  const changeCurrency = (currencyCode) => {
    const currencyObj = currencies.find(currency => currency.code === currencyCode);
    if (currencyObj) {
      setCurrentCurrency(currencyCode);
      localStorage.setItem('currency', currencyCode);
      setIsOpen(false);
    }
  };

  // الحصول على رمز العملة الحالية
  const getCurrentCurrencySymbol = () => {
    const currencyObj = currencies.find(currency => currency.code === currentCurrency);
    return currencyObj ? `${currencyObj.symbol} ${currencyObj.code}` : 'SAR';
  };

  // الأنماط
  const aliexpressOrange = '#ff4747';

  const currencySwitcherStyle = {
    position: 'relative',
    display: 'inline-block',
  };

  const toggleButtonStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '10px 16px',
    background: 'rgba(255, 255, 255, 0.15)',
    border: '1px solid rgba(255, 255, 255, 0.3)',
    borderRadius: '25px',
    cursor: 'pointer',
    fontSize: '0.9rem',
    color: 'white',
    fontWeight: '500',
    transition: 'all 0.3s ease',
    backdropFilter: 'blur(10px)',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
  };

  const dropdownStyle = {
    position: 'absolute',
    top: '100%',
    right: isClient && document.documentElement.dir === 'rtl' ? '0' : 'auto',
    left: isClient && document.documentElement.dir === 'rtl' ? 'auto' : '0',
    background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95))',
    boxShadow: '0 8px 32px rgba(74, 144, 226, 0.2)',
    borderRadius: '15px',
    minWidth: '180px',
    maxHeight: '300px',
    overflowY: 'auto',
    zIndex: 9999, // قيمة عالية جدًا لضمان ظهور القائمة فوق جميع العناصر
    display: isOpen ? 'block' : 'none',
    backdropFilter: 'blur(20px)',
    border: '1px solid rgba(255, 255, 255, 0.3)',
  };

  const currencyItemStyle = {
    padding: '15px 20px',
    cursor: 'pointer',
    borderBottom: '1px solid rgba(74, 144, 226, 0.1)',
    transition: 'all 0.3s ease',
    borderRadius: '8px',
    margin: '4px 8px',
    color: '#2C3E50',
    fontWeight: '500',
  };

  const activeCurrencyStyle = {
    ...currencyItemStyle,
    background: 'linear-gradient(135deg, rgba(74, 144, 226, 0.1), rgba(255, 140, 66, 0.1))',
    color: '#4A90E2',
    fontWeight: 'bold',
    transform: 'scale(1.02)',
    boxShadow: '0 2px 8px rgba(74, 144, 226, 0.2)',
  };

  return (
    <div style={currencySwitcherStyle}>
      <button 
        style={toggleButtonStyle} 
        onClick={() => setIsOpen(!isOpen)}
      >
        <span>💱</span>
        <span>{getCurrentCurrencySymbol()}</span>
      </button>
      
      <div style={dropdownStyle}>
        {currencies.map((currency) => (
          <div
            key={currency.code}
            style={currency.code === currentCurrency ? activeCurrencyStyle : currencyItemStyle}
            onClick={() => changeCurrency(currency.code)}
          >
            {currency.symbol} {currency.code} - {currency.name}
          </div>
        ))}
      </div>
    </div>
  );
}