'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from './TranslationProvider';

// قائمة العملات المدعومة في الخوارزميات
const currencies = [
  { code: 'USD', name: 'دولار أمريكي', symbol: '$' },
  { code: 'EUR', name: 'يورو', symbol: '€' },
  { code: 'GBP', name: 'جنيه استرليني', symbol: '£' },
  { code: 'SAR', name: 'ريال سعودي', symbol: 'ر.س' },
  { code: 'EGP', name: 'جنيه مصري', symbol: 'ج.م' },
  { code: 'UAH', name: 'هريفنيا أوكرانية', symbol: '₴' },
];

export default function CurrencySwitcher() {
  const { direction } = useTranslation();
  const [currentCurrency, setCurrentCurrency] = useState('SAR');
  const [isOpen, setIsOpen] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // تحديد ما إذا كنا في بيئة المتصفح
  useEffect(() => {
    setIsClient(true);
  }, []);

  // تحميل العملة المحفوظة عند بدء التشغيل
  useEffect(() => {
    if (isClient) {
      const savedCurrency = localStorage.getItem('currency') || 'SAR';
      setCurrentCurrency(savedCurrency);
    }
  }, [isClient]);

  // تغيير العملة
  const changeCurrency = (currencyCode) => {
    const currencyObj = currencies.find(currency => currency.code === currencyCode);
    if (currencyObj) {
      setCurrentCurrency(currencyCode);
      localStorage.setItem('currency', currencyCode);
      setIsOpen(false);
    }
  };

  // الحصول على رمز العملة الحالية
  const getCurrentCurrencySymbol = () => {
    const currencyObj = currencies.find(currency => currency.code === currentCurrency);
    return currencyObj ? `${currencyObj.symbol} ${currencyObj.code}` : 'SAR';
  };

  // الأنماط
  const aliexpressOrange = '#ff4747';

  const currencySwitcherStyle = {
    position: 'relative',
    display: 'inline-block',
  };

  const toggleButtonStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '5px',
    padding: '8px 12px',
    backgroundColor: 'transparent',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: '0.9rem',
    color: '#333',
  };

  const dropdownStyle = {
    position: 'absolute',
    top: '100%',
    right: isClient && document.documentElement.dir === 'rtl' ? '0' : 'auto',
    left: isClient && document.documentElement.dir === 'rtl' ? 'auto' : '0',
    backgroundColor: 'white',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
    borderRadius: '4px',
    minWidth: '150px',
    maxHeight: '300px',
    overflowY: 'auto',
    zIndex: 9999, // قيمة عالية جدًا لضمان ظهور القائمة فوق جميع العناصر
    display: isOpen ? 'block' : 'none',
  };

  const currencyItemStyle = {
    padding: '10px 15px',
    cursor: 'pointer',
    borderBottom: '1px solid #eee',
    transition: 'background-color 0.3s',
  };

  const activeCurrencyStyle = {
    ...currencyItemStyle,
    backgroundColor: '#f5f5f5',
    color: aliexpressOrange,
    fontWeight: 'bold',
  };

  return (
    <div style={currencySwitcherStyle}>
      <button 
        style={toggleButtonStyle} 
        onClick={() => setIsOpen(!isOpen)}
      >
        <span>💱</span>
        <span>{getCurrentCurrencySymbol()}</span>
      </button>
      
      <div style={dropdownStyle}>
        {currencies.map((currency) => (
          <div
            key={currency.code}
            style={currency.code === currentCurrency ? activeCurrencyStyle : currencyItemStyle}
            onClick={() => changeCurrency(currency.code)}
          >
            {currency.symbol} {currency.code} - {currency.name}
          </div>
        ))}
      </div>
    </div>
  );
}