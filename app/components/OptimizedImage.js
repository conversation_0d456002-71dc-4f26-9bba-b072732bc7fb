'use client';

import { useState, useEffect } from 'react';

/**
 * مكون الصورة المحسنة
 * يقوم بتحميل الصور من خلال خدمة تحسين الصور
 * 
 * @param {Object} props - خصائص المكون
 * @param {string} props.src - رابط الصورة الأصلي
 * @param {string} props.alt - النص البديل للصورة
 * @param {number} props.width - عرض الصورة
 * @param {number} props.height - ارتفاع الصورة
 * @param {number} props.quality - جودة الصورة (10-100)
 * @param {string} props.format - تنسيق الصورة (webp, jpeg, png)
 * @param {Object} props.style - أنماط CSS للصورة
 * @param {string} props.className - اسم الفئة للصورة
 * @param {Function} props.onLoad - دالة تنفذ عند تحميل الصورة
 * @param {Function} props.onError - دالة تنفذ عند حدوث خطأ في تحميل الصورة
 */
export default function OptimizedImage({
  src,
  alt = '',
  width = 300,
  height = 300,
  quality = 80,
  format = 'webp',
  style = {},
  className = '',
  onLoad,
  onError,
  ...props
}) {
  // حالة رابط الصورة المحسنة
  const [optimizedSrc, setOptimizedSrc] = useState('');
  // حالة تحميل الصورة
  const [isLoading, setIsLoading] = useState(true);
  // حالة خطأ تحميل الصورة
  const [error, setError] = useState(null);
  
  // تحسين الصورة عند تغيير المصدر أو الأبعاد أو الجودة
  useEffect(() => {
    // إعادة تعيين الحالة
    setIsLoading(true);
    setError(null);
    
    // إذا لم يكن هناك مصدر، لا نفعل شيئًا
    if (!src) {
      setIsLoading(false);
      setError('لم يتم توفير مصدر الصورة');
      return;
    }
    
    // استخدام التخزين المؤقت للصور المحسنة
    const cacheKey = `optimized_image_${src}_${width}_${height}_${quality}_${format}`;
    const cachedSrc = localStorage.getItem(cacheKey);
    
    if (cachedSrc) {
      // استخدام الصورة المخزنة مؤقتًا
      setOptimizedSrc(cachedSrc);
      setIsLoading(false);
    } else {
      // طلب تحسين الصورة من الخادم
      fetch(`/api/image-optimizer?url=${encodeURIComponent(src)}&width=${width}&height=${height}&quality=${quality}&format=${format}`)
        .then(response => {
          if (!response.ok) {
            throw new Error(`فشل تحسين الصورة: ${response.status}`);
          }
          return response.json();
        })
        .then(data => {
          // تخزين رابط الصورة المحسنة
          setOptimizedSrc(data.optimized_url);
          // تخزين الصورة المحسنة في التخزين المؤقت
          localStorage.setItem(cacheKey, data.optimized_url);
          setIsLoading(false);
        })
        .catch(err => {
          console.error('خطأ في تحسين الصورة:', err);
          // في حالة الخطأ، استخدم الصورة الأصلية
          setOptimizedSrc(src);
          setError(err.message);
          setIsLoading(false);
        });
    }
  }, [src, width, height, quality, format]);
  
  // معالجة حدث تحميل الصورة
  const handleLoad = (e) => {
    if (onLoad) onLoad(e);
  };
  
  // معالجة حدث خطأ تحميل الصورة
  const handleError = (e) => {
    console.error('خطأ في تحميل الصورة:', e);
    // في حالة الخطأ، استخدم الصورة الأصلية
    if (optimizedSrc !== src) {
      setOptimizedSrc(src);
    }
    if (onError) onError(e);
  };
  
  // أنماط الحاوية
  const containerStyle = {
    position: 'relative',
    width: width ? `${width}px` : '100%',
    height: height ? `${height}px` : 'auto',
    overflow: 'hidden',
    ...style
  };
  
  // أنماط الصورة
  const imgStyle = {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    transition: 'opacity 0.3s ease',
    opacity: isLoading ? 0 : 1
  };
  
  // أنماط مؤشر التحميل
  const loaderStyle = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f0f0',
    opacity: isLoading ? 1 : 0,
    transition: 'opacity 0.3s ease',
    pointerEvents: isLoading ? 'auto' : 'none'
  };
  
  // أنماط رسالة الخطأ
  const errorStyle = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff8f8',
    color: '#d32f2f',
    padding: '10px',
    textAlign: 'center',
    fontSize: '12px',
    opacity: error ? 1 : 0,
    transition: 'opacity 0.3s ease',
    pointerEvents: error ? 'auto' : 'none'
  };
  
  return (
    <div style={containerStyle} className={className}>
      {/* الصورة المحسنة */}
      {optimizedSrc && (
        <img
          src={optimizedSrc}
          alt={alt}
          style={imgStyle}
          onLoad={handleLoad}
          onError={handleError}
          {...props}
        />
      )}
      
      {/* مؤشر التحميل */}
      <div style={loaderStyle}>
        <div className="loading-spinner" style={{ width: '30px', height: '30px' }}></div>
      </div>
      
      {/* رسالة الخطأ */}
      {error && (
        <div style={errorStyle}>
          <span>فشل تحميل الصورة</span>
        </div>
      )}
    </div>
  );
}