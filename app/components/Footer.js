'use client';

import Link from 'next/link';
import { useTranslation } from './TranslationProvider';
import { usePathname } from 'next/navigation';

export default function Footer() {
  const { t, language, direction } = useTranslation();
  const pathname = usePathname();
  
  // Don't show footer on admin pages
  if (pathname && pathname.includes('/admin-panel-95')) {
    return null;
  }

  return (
    <footer className="site-footer" dir={direction}>
      <div className="footer-container">
        <div className="footer-top">
          <div className="footer-section">
            <h3 className="footer-heading">{t('footer.about')}</h3>
            <p className="footer-text">
              {t('footer.aboutText')}
            </p>
          </div>
          
          <div className="footer-section">
            <h3 className="footer-heading">{t('footer.links')}</h3>
            <ul className="footer-links">
              <li>
                <Link href="/faq">
                  {t('footer.faq')}
                </Link>
              </li>
              <li>
                <Link href="/privacy-policy">
                  {t('footer.privacyPolicy')}
                </Link>
              </li>
              <li>
                <Link href="/terms">
                  {t('footer.terms')}
                </Link>
              </li>
            </ul>
          </div>
          
          <div className="footer-section">
            <h3 className="footer-heading">{t('footer.contact')}</h3>
            <ul className="footer-contact">
              <li>
                <a href="mailto:<EMAIL>">
                  <EMAIL>
                </a>
              </li>
              <li className="social-icons">
                <a href="#" className="social-icon" aria-label="Facebook">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                  </svg>
                </a>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="footer-bottom">
          <p className="copyright">
            © 2025 {t('common.siteTitle')}. {t('footer.allRightsReserved')}
          </p>
        </div>
      </div>
    </footer>
  );
}