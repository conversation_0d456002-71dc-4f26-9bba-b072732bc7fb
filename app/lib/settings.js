/**
 * وحدة إدارة إعدادات الموقع
 * تتيح الوصول إلى إعدادات الموقع المخزنة في قاعدة البيانات
 */

import { query } from './db';

// كائن للتخزين المؤقت للإعدادات (لتجنب الاستعلامات المتكررة)
let settingsCache = null;
let lastFetchTime = 0;
const CACHE_TTL = 60 * 60 * 1000; // ساعة واحدة بالمللي ثانية

/**
 * جلب إعدادات الموقع من قاعدة البيانات
 * مع دعم التخزين المؤقت لتحسين الأداء
 * @returns {Promise<Object>} كائن يحتوي على إعدادات الموقع
 */
export async function getSiteSettings() {
  const currentTime = Date.now();
  
  // إذا كانت الإعدادات مخزنة مؤقتًا وما زالت صالحة، نعيدها مباشرة
  if (settingsCache && (currentTime - lastFetchTime < CACHE_TTL)) {
    return settingsCache;
  }
  
  try {
    // في بيئة التطوير، نعيد إعدادات وهمية
    if (process.env.NODE_ENV === 'development') {
      settingsCache = {
        site_name: 'متجر التسوق الذكي',
        site_description: 'أفضل المنتجات بأفضل الأسعار',
        site_logo: '/logo.png',
        primary_color: '#3498db',
        secondary_color: '#2ecc71',
        ali_api_key: process.env.ALIEXPRESS_APP_KEY || 'dev_api_key',
        ali_secret_key: process.env.ALIEXPRESS_SECRET_KEY || 'dev_secret_key',
        ali_affiliate_tag: process.env.ALIEXPRESS_TAG_ID || 'dev_tag_id',
        currency: 'USD',
        language: 'ar',
        products_per_page: 30,
        cache_duration: 5 * 60 * 60, // 5 ساعات
      };
    } else {
      // في بيئة الإنتاج، نجلب الإعدادات من قاعدة البيانات
      // التحقق من وجود جدول الإعدادات
      try {
        await query(`
          CREATE TABLE IF NOT EXISTS site_settings (
            id INT PRIMARY KEY DEFAULT 1,
            site_name VARCHAR(100),
            site_description TEXT,
            site_logo VARCHAR(255),
            primary_color VARCHAR(20),
            secondary_color VARCHAR(20),
            ali_api_key VARCHAR(100),
            ali_secret_key VARCHAR(100),
            ali_affiliate_tag VARCHAR(100),
            default_currency VARCHAR(10),
            default_language VARCHAR(10),
            products_per_page INT DEFAULT 30,
            cache_duration INT DEFAULT 18000,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
          )
        `);
      } catch (err) {
        console.error('Error creating settings table:', err);
      }
      
      // جلب الإعدادات
      const settings = await query('SELECT * FROM site_settings WHERE id = 1');
      
      if (!settings || settings.length === 0) {
        // إذا لم توجد إعدادات، نعيد الإعدادات الافتراضية
        return {
          ali_api_key: process.env.ALIEXPRESS_APP_KEY,
          ali_secret_key: process.env.ALIEXPRESS_SECRET_KEY,
          ali_affiliate_tag: process.env.ALIEXPRESS_TAG_ID,
          currency: 'USD',
          language: 'ar',
        };
      }
      
      settingsCache = settings[0];
    }
    
    lastFetchTime = currentTime;
    return settingsCache;
  } catch (error) {
    console.error('Error fetching site settings:', error);
    
    // في حالة الخطأ، نعيد الإعدادات الافتراضية من متغيرات البيئة
    return {
      ali_api_key: process.env.ALIEXPRESS_APP_KEY,
      ali_secret_key: process.env.ALIEXPRESS_SECRET_KEY,
      ali_affiliate_tag: process.env.ALIEXPRESS_TAG_ID,
      currency: 'USD',
      language: 'ar',
    };
  }
}

/**
 * تحديث إعدادات الموقع في قاعدة البيانات
 * @param {Object} newSettings - الإعدادات الجديدة
 * @returns {Promise<boolean>} نجاح أو فشل العملية
 */
export async function updateSiteSettings(newSettings) {
  try {
    // في بيئة الإنتاج، نحدث الإعدادات في قاعدة البيانات
    if (process.env.NODE_ENV !== 'development') {
      // تحويل الإعدادات إلى صيغة SQL
      const fields = Object.keys(newSettings).map(key => `${key} = ?`).join(', ');
      const values = Object.values(newSettings);
      
      await query(`
        INSERT INTO site_settings (id, ${Object.keys(newSettings).join(', ')})
        VALUES (1, ${Array(Object.keys(newSettings).length).fill('?').join(', ')})
        ON DUPLICATE KEY UPDATE ${fields}
      `, [...values, ...values]);
    }
    
    // تحديث التخزين المؤقت
    settingsCache = { ...settingsCache, ...newSettings };
    lastFetchTime = Date.now();
    
    return true;
  } catch (error) {
    console.error('Error updating site settings:', error);
    return false;
  }
}