import { createClient } from 'redis';

// إنشاء عميل Redis
let redisClient;

/**
 * تهيئة اتصال Redis
 */
export async function initRedis() {
  try {
    // تجاهل اتصال Redis أثناء البناء أو إذا تم تعيين متغير البيئة NEXT_IGNORE_MISSING_REDIS
    if (process.env.NEXT_IGNORE_MISSING_REDIS === '1' || process.env.NODE_ENV === 'production' && process.env.NEXT_PHASE === 'build') {
      console.log('تجاهل اتصال Redis أثناء البناء');
      return null;
    }

    if (!redisClient) {
      // فشل إذا لم يتم توفير متغيرات الاتصال
      if (!process.env.REDIS_URL && !process.env.REDIS_HOST) {
        // إذا كنا في وضع التطوير، نستخدم اتصال محلي افتراضي
        if (process.env.NODE_ENV === 'development') {
          console.log('استخدام اتصال Redis محلي افتراضي في وضع التطوير');
          process.env.REDIS_HOST = 'localhost';
          process.env.REDIS_PORT = '6379';
        } else {
          throw new Error('لم يتم تكوين اتصال Redis - يلزم تحديد REDIS_URL أو REDIS_HOST');
        }
      }

      const options = process.env.REDIS_URL 
        ? { url: process.env.REDIS_URL }
        : {
            socket: {
              host: process.env.REDIS_HOST,
              port: parseInt(process.env.REDIS_PORT) || 6379,
              reconnectStrategy: (retries) => Math.min(retries * 100, 5000)
            },
            password: process.env.REDIS_PASSWORD,
            legacyMode: false
          };

      redisClient = createClient(options);

      redisClient.on('error', (err) => {
        console.error('Redis Error:', err);
      });

      await redisClient.connect();
      console.log(`✅ تم الاتصال بـ Redis على ${options.socket?.host || (options.url ? new URL(options.url).hostname : 'unknown')}`);
    }
    
    return redisClient;
  } catch (error) {
    console.error('❌ فشل الاتصال بـ Redis:', error);
    
    // إذا كنا في وضع البناء، نتجاهل الخطأ
    if (process.env.NEXT_IGNORE_MISSING_REDIS === '1' || process.env.NODE_ENV === 'production' && process.env.NEXT_PHASE === 'build') {
      console.log('تجاهل خطأ Redis أثناء البناء');
      return null;
    }
    
    throw error; // إعادة رمي الخطأ لإدارة أفضل
  }
}

/**
 * الحصول على عميل Redis
 */
export async function getRedisClient() {
  try {
    // تجاهل اتصال Redis أثناء البناء
    if (process.env.NEXT_IGNORE_MISSING_REDIS === '1' || process.env.NODE_ENV === 'production' && process.env.NEXT_PHASE === 'build') {
      return null;
    }
    
    if (!redisClient || !redisClient.isOpen) {
      await initRedis();
    }
    return redisClient;
  } catch (error) {
    console.error('فشل في الحصول على عميل Redis:', error);
    return null;
  }
}

/**
 * تخزين البيانات في Redis
 * @param {string} key - المفتاح
 * @param {any} data - البيانات للتخزين
 * @param {number} expiryInSeconds - وقت انتهاء الصلاحية بالثواني
 */
export async function setCache(key, data, expiryInSeconds = 86400) {
  try {
    // تجاهل تخزين البيانات أثناء البناء
    if (process.env.NEXT_IGNORE_MISSING_REDIS === '1' || process.env.NODE_ENV === 'production' && process.env.NEXT_PHASE === 'build') {
      return true; // نعيد true لتجنب الأخطاء في الكود الذي يستخدم هذه الدالة
    }
    
    const client = await getRedisClient();
    if (!client) return false;
    
    await client.set(key, JSON.stringify(data));
    if (expiryInSeconds > 0) {
      await client.expire(key, expiryInSeconds);
    }
    
    return true;
  } catch (error) {
    console.error(`خطأ في تخزين البيانات في Redis (${key}):`, error);
    
    // تجاهل الأخطاء أثناء البناء
    if (process.env.NEXT_IGNORE_MISSING_REDIS === '1' || process.env.NODE_ENV === 'production' && process.env.NEXT_PHASE === 'build') {
      return true;
    }
    
    return false;
  }
}

/**
 * استرجاع البيانات من Redis
 * @param {string} key - المفتاح
 * @returns {any} البيانات المخزنة أو null
 */
export async function getCache(key) {
  try {
    // تجاهل استرجاع البيانات أثناء البناء
    if (process.env.NEXT_IGNORE_MISSING_REDIS === '1' || process.env.NODE_ENV === 'production' && process.env.NEXT_PHASE === 'build') {
      return null;
    }
    
    const client = await getRedisClient();
    if (!client) return null;
    
    const data = await client.get(key);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error(`خطأ في استرجاع البيانات من Redis (${key}):`, error);
    return null;
  }
}

/**
 * حذف البيانات من Redis
 * @param {string} key - المفتاح
 */
export async function deleteCache(key) {
  try {
    // تجاهل حذف البيانات أثناء البناء
    if (process.env.NEXT_IGNORE_MISSING_REDIS === '1' || process.env.NODE_ENV === 'production' && process.env.NEXT_PHASE === 'build') {
      return true;
    }
    
    const client = await getRedisClient();
    if (!client) return false;
    
    await client.del(key);
    return true;
  } catch (error) {
    console.error(`خطأ في حذف البيانات من Redis (${key}):`, error);
    
    // تجاهل الأخطاء أثناء البناء
    if (process.env.NEXT_IGNORE_MISSING_REDIS === '1' || process.env.NODE_ENV === 'production' && process.env.NEXT_PHASE === 'build') {
      return true;
    }
    
    return false;
  }
}