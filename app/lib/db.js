import prisma from '@/lib/prisma';
import { Prisma } from '@prisma/client';

/**
 * تنفيذ استعلام SQL مع معلمات اختيارية باستخدام Prisma
 * يدعم كلا من DATABASE_URL والمتغيرات المنفصلة
 * 
 * @param {string} sql - استعلام SQL
 * @param {Array} params - معلمات الاستعلام (اختياري)
 * @returns {Promise<Array>} - نتائج الاستعلام
 */
export async function query(sql, params = []) {
  try {
    // استخدام Prisma.$queryRawUnsafe لتنفيذ استعلامات SQL مخصصة
    const results = await prisma.$queryRawUnsafe(sql, ...params);
    return results;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
}

/**
 * تنفيذ مجموعة من الاستعلامات في معاملة واحدة باستخدام Prisma
 * 
 * @param {Function} callback - دالة تنفذ الاستعلامات داخل المعاملة
 * @returns {Promise<any>} - نتيجة المعاملة
 */
export async function transaction(callback) {
  try {
    // استخدام Prisma.$transaction لتنفيذ معاملات
    return await prisma.$transaction(async (tx) => {
      return await callback(tx);
    });
  } catch (error) {
    console.error('Transaction error:', error);
    throw error;
  }
}

/**
 * إنشاء جداول قاعدة البيانات إذا لم تكن موجودة
 * تم تحديثها لاستخدام Prisma بدلاً من استعلامات SQL المباشرة
 */
export async function initDatabase() {
  try {
    // استخدام Prisma.$queryRaw لتنفيذ استعلامات SQL مباشرة
    // هذه الاستعلامات ستعمل سواء كان الاتصال عبر DATABASE_URL أو المتغيرات المنفصلة
    
    // جدول اللغات المدعومة
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS languages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        code VARCHAR(10) UNIQUE,
        name VARCHAR(50),
        is_active BOOLEAN DEFAULT TRUE
      )
    `;

    // جدول العملات المدعومة
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS currencies (
        id INT AUTO_INCREMENT PRIMARY KEY,
        code VARCHAR(10) UNIQUE,
        name VARCHAR(50),
        symbol VARCHAR(10),
        is_active BOOLEAN DEFAULT TRUE
      )
    `;

    // جدول فئات العروض
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS offer_categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        category_key VARCHAR(50) UNIQUE,
        ar_name VARCHAR(100),
        en_name VARCHAR(100),
        icon VARCHAR(10),
        color VARCHAR(20)
      )
    `;

    // جدول العروض المخزنة مؤقتاً
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS cached_offers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        category_key VARCHAR(50),
        language_code VARCHAR(10),
        currency_code VARCHAR(10),
        offers_data JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP DEFAULT (CURRENT_TIMESTAMP + INTERVAL 24 HOUR),
        INDEX idx_category_lang_curr (category_key, language_code, currency_code),
        INDEX idx_expires_at (expires_at)
      )
    `;

    // جدول لتتبع آخر تحديث للعروض
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS offers_refresh_status (
        id INT PRIMARY KEY DEFAULT 1,
        last_refresh TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        next_refresh TIMESTAMP DEFAULT (CURRENT_TIMESTAMP + INTERVAL 24 HOUR),
        is_refreshing BOOLEAN DEFAULT FALSE,
        refresh_count INT DEFAULT 0
      )
    `;

    // إدخال البيانات الأساسية إذا لم تكن موجودة
    const languages = await prisma.$queryRaw`SELECT COUNT(*) as count FROM languages`;
    if (languages[0].count === 0) {
      await prisma.$executeRaw`
        INSERT INTO languages (code, name) VALUES 
          ('ar', 'العربية'),
          ('en', 'English'),
          ('fr', 'Français'),
          ('es', 'Español'),
          ('uk', 'Українська')
      `;
    }

    const currencies = await prisma.$queryRaw`SELECT COUNT(*) as count FROM currencies`;
    if (currencies[0].count === 0) {
      await prisma.$executeRaw`
        INSERT INTO currencies (code, name, symbol) VALUES 
          ('USD', 'US Dollar', '$'),
          ('EUR', 'Euro', '€'),
          ('SAR', 'Saudi Riyal', 'ر.س'),
          ('GBP', 'British Pound', '£'),
          ('EGP', 'Egyptian Pound', 'ج.م'),
          ('UAH', 'Ukrainian Hryvnia', '₴')
      `;
    }

    const categories = await prisma.$queryRaw`SELECT COUNT(*) as count FROM offer_categories`;
    if (categories[0].count === 0) {
      await prisma.$executeRaw`
        INSERT INTO offer_categories (category_key, ar_name, en_name, icon, color) VALUES 
          ('top_rated', 'الأعلى تقييماً', 'Top Rated', '⭐', '#3498db'),
          ('best_sellers', 'الأكثر مبيعاً', 'Best Sellers', '🔥', '#e74c3c'),
          ('discounted', 'خصومات كبيرة', 'Big Discounts', '💰', '#2ecc71'),
          ('new_arrivals', 'وصل حديثاً', 'New Arrivals', '🆕', '#9b59b6'),
          ('premium', 'منتجات مميزة', 'Premium Products', '👑', '#f39c12')
      `;
    }

    const status = await prisma.$queryRaw`SELECT COUNT(*) as count FROM offers_refresh_status`;
    if (status[0].count === 0) {
      await prisma.$executeRaw`
        INSERT INTO offers_refresh_status (id, last_refresh, next_refresh, is_refreshing, refresh_count) 
        VALUES (1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL 24 HOUR, FALSE, 0)
      `;
    }

    // إنشاء حدث لحذف البيانات القديمة
    await prisma.$executeRaw`
      SET GLOBAL event_scheduler = ON
    `;

    // حذف الحدث إذا كان موجوداً
    await prisma.$executeRaw`
      DROP EVENT IF EXISTS delete_expired_offers
    `;

    // إنشاء حدث جديد
    await prisma.$executeRaw`
      CREATE EVENT delete_expired_offers
      ON SCHEDULE EVERY 1 HOUR
      DO
        DELETE FROM cached_offers WHERE expires_at < NOW()
    `;

    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Database initialization error:', error);
    throw error;
  }
}

// تصدير الدوال
export default {
  query,
  transaction,
  initDatabase
};