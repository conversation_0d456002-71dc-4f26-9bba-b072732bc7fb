import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Importar archivos de traducción
import translationEN from './translations/en.js';
import translationAR from './translations/ar.js';
import translationES from './translations/es.js';
import translationFR from './translations/fr.js';
import translationUK from './translations/uk.js';

// Los recursos de traducción
const resources = {
  en: {
    translation: translationEN
  },
  ar: {
    translation: translationAR
  },
  es: {
    translation: translationES
  },
  fr: {
    translation: translationFR
  },
  uk: {
    translation: translationUK
  }
};

i18n
  // Detectar el idioma del navegador
  .use(LanguageDetector)
  // Pasar el módulo i18n a react-i18next
  .use(initReactI18next)
  // Inicializar i18next
  .init({
    resources,
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',
    interpolation: {
      escapeValue: false, // No es necesario para React
    },
    react: {
      useSuspense: false,
    }
  });

export default i18n;