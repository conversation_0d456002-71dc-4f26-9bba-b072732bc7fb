'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from '../components/TranslationProvider';

export default function SettingsPage() {
  const router = useRouter();
  const { t, language, direction } = useTranslation();
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmNewPassword: ''
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  useEffect(() => {
    // التحقق من حالة تسجيل الدخول
    const isAuthenticated = localStorage.getItem('is_authenticated');
    if (!isAuthenticated || isAuthenticated !== 'true') {
      router.push('/login');
      return;
    }
    
    // نتحقق فقط من حالة تسجيل الدخول كمستخدم عادي
    // ولا نقوم بالتوجيه التلقائي إلى صفحة إعدادات المسؤول حتى لو كان مسجل دخول كمسؤول أيضاً
    // هذا يسمح للمستخدم بالوصول إلى كلا الصفحتين إذا كان مسجل دخول بكلا الحسابين
  }, [router]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
    // مسح رسالة الخطأ عند الكتابة
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };

  const togglePasswordVisibility = (field) => {
    switch (field) {
      case 'currentPassword':
        setShowCurrentPassword(!showCurrentPassword);
        break;
      case 'newPassword':
        setShowNewPassword(!showNewPassword);
        break;
      case 'confirmNewPassword':
        setShowConfirmPassword(!showConfirmPassword);
        break;
      default:
        break;
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    // التحقق من كلمة المرور الحالية
    if (!formData.currentPassword) {
      newErrors.currentPassword = t('common.errors.required', 'هذا الحقل مطلوب');
    }
    
    // التحقق من كلمة المرور الجديدة
    if (!formData.newPassword) {
      newErrors.newPassword = t('common.errors.required', 'هذا الحقل مطلوب');
    } else if (formData.newPassword.length < 6) {
      newErrors.newPassword = t('common.errors.passwordLength', 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
    }
    
    // التحقق من تأكيد كلمة المرور
    if (!formData.confirmNewPassword) {
      newErrors.confirmNewPassword = t('common.errors.required', 'هذا الحقل مطلوب');
    } else if (formData.newPassword !== formData.confirmNewPassword) {
      newErrors.confirmNewPassword = t('common.errors.passwordMatch', 'كلمة المرور غير متطابقة');
    }
    
    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    const formErrors = validateForm();
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }
    
    setIsSubmitting(true);
    setSuccessMessage('');
    
    try {
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // في بيئة الإنتاج، ستقوم بالتحقق من كلمة المرور الحالية وتحديث كلمة المرور الجديدة
      // هنا نقوم بمحاكاة العملية
      
      // عرض رسالة نجاح
      setSuccessMessage(t('settings.passwordChanged', 'تم تغيير كلمة المرور بنجاح'));
      
      // إعادة تعيين النموذج
      setFormData({
        currentPassword: '',
        newPassword: '',
        confirmNewPassword: ''
      });
      
    } catch (error) {
      console.error('خطأ في تغيير كلمة المرور:', error);
      setErrors({
        submit: t('common.errors.serverError', 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى.')
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div style={containerStyle} dir={direction}>
      <div style={settingsContainerStyle}>
        <h1 style={titleStyle}>{t('settings.title', 'الإعدادات')}</h1>
        
        <div style={sectionStyle}>
          <h2 style={sectionTitleStyle}>{t('settings.changePassword', 'تغيير كلمة المرور')}</h2>
          
          {successMessage && (
            <div style={successMessageStyle}>
              {successMessage}
            </div>
          )}
          
          {errors.submit && (
            <div style={errorMessageStyle}>
              {errors.submit}
            </div>
          )}
          
          <form onSubmit={handleSubmit} style={formStyle}>
            <div style={formGroupStyle}>
              <label htmlFor="currentPassword" style={labelStyle}>
                {t('settings.currentPassword', 'كلمة المرور الحالية')}
              </label>
              <div style={passwordContainerStyle}>
                <input
                  type={showCurrentPassword ? "text" : "password"}
                  id="currentPassword"
                  name="currentPassword"
                  value={formData.currentPassword}
                  onChange={handleChange}
                  style={{
                    ...inputStyle,
                    ...(errors.currentPassword ? inputErrorStyle : {}),
                    paddingRight: direction === 'rtl' ? '40px' : '15px',
                    paddingLeft: direction === 'rtl' ? '15px' : '40px'
                  }}
                  placeholder={t('settings.currentPasswordPlaceholder', 'أدخل كلمة المرور الحالية')}
                  disabled={isSubmitting}
                />
                <button 
                  type="button" 
                  onClick={() => togglePasswordVisibility('currentPassword')} 
                  style={{
                    ...eyeIconStyle,
                    right: direction === 'rtl' ? 'auto' : '10px',
                    left: direction === 'rtl' ? '10px' : 'auto'
                  }}
                  aria-label={showCurrentPassword ? "إخفاء كلمة المرور" : "إظهار كلمة المرور"}
                >
                  <svg 
                    width="20" 
                    height="20" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                    style={{display: 'block'}}
                  >
                    {showCurrentPassword ? (
                      <>
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                      </>
                    ) : (
                      <>
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                        <line x1="1" y1="1" x2="23" y2="23"></line>
                      </>
                    )}
                  </svg>
                </button>
              </div>
              {errors.currentPassword && <p style={fieldErrorStyle}>{errors.currentPassword}</p>}
            </div>
            
            <div style={formGroupStyle}>
              <label htmlFor="newPassword" style={labelStyle}>
                {t('settings.newPassword', 'كلمة المرور الجديدة')}
              </label>
              <div style={passwordContainerStyle}>
                <input
                  type={showNewPassword ? "text" : "password"}
                  id="newPassword"
                  name="newPassword"
                  value={formData.newPassword}
                  onChange={handleChange}
                  style={{
                    ...inputStyle,
                    ...(errors.newPassword ? inputErrorStyle : {}),
                    paddingRight: direction === 'rtl' ? '40px' : '15px',
                    paddingLeft: direction === 'rtl' ? '15px' : '40px'
                  }}
                  placeholder={t('settings.newPasswordPlaceholder', 'أدخل كلمة المرور الجديدة')}
                  disabled={isSubmitting}
                />
                <button 
                  type="button" 
                  onClick={() => togglePasswordVisibility('newPassword')} 
                  style={{
                    ...eyeIconStyle,
                    right: direction === 'rtl' ? 'auto' : '10px',
                    left: direction === 'rtl' ? '10px' : 'auto'
                  }}
                  aria-label={showNewPassword ? "إخفاء كلمة المرور" : "إظهار كلمة المرور"}
                >
                  <svg 
                    width="20" 
                    height="20" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                    style={{display: 'block'}}
                  >
                    {showNewPassword ? (
                      <>
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                      </>
                    ) : (
                      <>
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                        <line x1="1" y1="1" x2="23" y2="23"></line>
                      </>
                    )}
                  </svg>
                </button>
              </div>
              {errors.newPassword && <p style={fieldErrorStyle}>{errors.newPassword}</p>}
            </div>
            
            <div style={formGroupStyle}>
              <label htmlFor="confirmNewPassword" style={labelStyle}>
                {t('settings.confirmNewPassword', 'تأكيد كلمة المرور الجديدة')}
              </label>
              <div style={passwordContainerStyle}>
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  id="confirmNewPassword"
                  name="confirmNewPassword"
                  value={formData.confirmNewPassword}
                  onChange={handleChange}
                  style={{
                    ...inputStyle,
                    ...(errors.confirmNewPassword ? inputErrorStyle : {}),
                    paddingRight: direction === 'rtl' ? '40px' : '15px',
                    paddingLeft: direction === 'rtl' ? '15px' : '40px'
                  }}
                  placeholder={t('settings.confirmNewPasswordPlaceholder', 'أعد إدخال كلمة المرور الجديدة')}
                  disabled={isSubmitting}
                />
                <button 
                  type="button" 
                  onClick={() => togglePasswordVisibility('confirmNewPassword')} 
                  style={{
                    ...eyeIconStyle,
                    right: direction === 'rtl' ? 'auto' : '10px',
                    left: direction === 'rtl' ? '10px' : 'auto'
                  }}
                  aria-label={showConfirmPassword ? "إخفاء كلمة المرور" : "إظهار كلمة المرور"}
                >
                  <svg 
                    width="20" 
                    height="20" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                    style={{display: 'block'}}
                  >
                    {showConfirmPassword ? (
                      <>
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                      </>
                    ) : (
                      <>
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                        <line x1="1" y1="1" x2="23" y2="23"></line>
                      </>
                    )}
                  </svg>
                </button>
              </div>
              {errors.confirmNewPassword && <p style={fieldErrorStyle}>{errors.confirmNewPassword}</p>}
            </div>
            
            <button 
              type="submit" 
              style={{
                ...buttonStyle,
                ...(isSubmitting ? buttonDisabledStyle : {})
              }}
              disabled={isSubmitting}
            >
              {isSubmitting ? t('common.messages.processing', 'جاري المعالجة...') : t('settings.saveChanges', 'حفظ التغييرات')}
            </button>
          </form>
          
          <div style={forgotPasswordContainerStyle}>
            <a href="/reset-password" style={forgotPasswordLinkStyle}>
              {t('auth.login.forgotPassword', 'نسيت كلمة المرور؟')}
            </a>
          </div>
        </div>
        
        <div style={actionsContainerStyle}>
          <button 
            onClick={() => router.push('/profile')}
            style={secondaryButtonStyle}
          >
            {t('settings.backToProfile', 'العودة إلى الملف الشخصي')}
          </button>
        </div>
      </div>
    </div>
  );
}

// الألوان
const aliexpressOrange = '#ff4747';
const aliexpressLightOrange = '#ff6e6e';
const aliexpressDarkOrange = '#e53935';

// الأنماط
const containerStyle = {
  maxWidth: '1200px',
  margin: '0 auto',
  padding: '20px',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'flex-start',
  minHeight: '80vh',
};

const settingsContainerStyle = {
  backgroundColor: 'white',
  borderRadius: '8px',
  boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
  padding: '30px',
  width: '100%',
  maxWidth: '700px',
};

const titleStyle = {
  fontSize: '1.8rem',
  color: '#333',
  marginBottom: '25px',
  textAlign: 'center',
};

const sectionStyle = {
  marginBottom: '30px',
  padding: '20px',
  backgroundColor: '#f9f9f9',
  borderRadius: '6px',
};

const sectionTitleStyle = {
  fontSize: '1.3rem',
  color: '#333',
  marginBottom: '20px',
  paddingBottom: '10px',
  borderBottom: '1px solid #eee',
};

const formStyle = {
  width: '100%',
};

const formGroupStyle = {
  marginBottom: '20px',
};

const labelStyle = {
  display: 'block',
  marginBottom: '8px',
  fontSize: '0.95rem',
  color: '#333',
  fontWeight: '500',
};

const inputStyle = {
  width: '100%',
  padding: '12px 15px',
  fontSize: '1rem',
  border: '1px solid #ddd',
  borderRadius: '4px',
  transition: 'border-color 0.3s',
  outline: 'none',
  boxSizing: 'border-box',
};

const inputErrorStyle = {
  borderColor: '#e53935',
  backgroundColor: '#fff8f8',
};

const fieldErrorStyle = {
  color: '#e53935',
  fontSize: '0.85rem',
  marginTop: '5px',
  marginBottom: '0',
};

const buttonStyle = {
  width: '100%',
  padding: '12px',
  backgroundColor: aliexpressOrange,
  color: 'white',
  border: 'none',
  borderRadius: '4px',
  fontSize: '1rem',
  fontWeight: 'bold',
  cursor: 'pointer',
  transition: 'background-color 0.3s',
};

const secondaryButtonStyle = {
  padding: '10px 20px',
  backgroundColor: '#f5f5f5',
  color: '#333',
  border: '1px solid #ddd',
  borderRadius: '4px',
  fontSize: '0.95rem',
  cursor: 'pointer',
  transition: 'background-color 0.3s',
};

const buttonDisabledStyle = {
  backgroundColor: '#ccc',
  cursor: 'not-allowed',
};

const actionsContainerStyle = {
  marginTop: '20px',
  display: 'flex',
  justifyContent: 'center',
};

const successMessageStyle = {
  backgroundColor: '#e8f5e9',
  color: '#2e7d32',
  padding: '12px 15px',
  borderRadius: '4px',
  marginBottom: '20px',
  textAlign: 'center',
};

const errorMessageStyle = {
  backgroundColor: '#ffebee',
  color: '#c62828',
  padding: '12px 15px',
  borderRadius: '4px',
  marginBottom: '20px',
  textAlign: 'center',
};

const forgotPasswordContainerStyle = {
  textAlign: 'center',
  marginTop: '15px',
};

const forgotPasswordLinkStyle = {
  color: '#666',
  textDecoration: 'none',
  fontSize: '0.9rem',
  transition: 'color 0.3s',
};

const passwordContainerStyle = {
  position: 'relative',
  width: '100%',
};

const eyeIconStyle = {
  position: 'absolute',
  top: '50%',
  transform: 'translateY(-50%)',
  background: 'none',
  border: 'none',
  cursor: 'pointer',
  fontSize: '1.2rem',
  color: '#666',
  padding: '0',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '30px',
  height: '30px',
};