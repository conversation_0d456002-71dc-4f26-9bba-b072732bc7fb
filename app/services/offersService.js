import { setCache, getCache } from '../lib/redis';

// المفتاح المستخدم لتخزين العروض في Redis
const OFFERS_CACHE_KEY = 'offers:en:USD';

// وقت انتهاء الصلاحية (24 ساعة بالثواني)
const CACHE_EXPIRY = 24 * 60 * 60;

/**
 * جلب أفضل المنتجات من API علي إكسبرس
 * @returns {Promise<Array>} قائمة المنتجات
 */
async function fetchTopProducts() {
  try {
    console.log("جاري التحقق من مفاتيح API علي إكسبرس...");

    // التحقق من وجود مفاتيح API
    const appKey = process.env.ALIEXPRESS_APP_KEY;
    const secretKey = process.env.ALIEXPRESS_SECRET_KEY;
    const tagId = process.env.ALIEXPRESS_TAG_ID;

    // إذا لم تكن المفاتيح متوفرة، لا نعرض أي منتجات
    if (!appKey || !secretKey || !tagId ||
        appKey === 'dev_app_key_123' ||
        secretKey === 'dev_secret_key_456' ||
        tagId === 'dev_tag_id_789') {
      console.log("مفاتيح API علي إكسبرس غير متوفرة - لن يتم عرض أي منتجات");
      return [];
    }

    console.log("جاري جلب أفضل 50 منتج من API علي إكسبرس...");

    // في الإنتاج، هذا سيكون طلب حقيقي إلى API علي إكسبرس
    // حالياً نستخدم بيانات وهمية للتجربة فقط عند وجود المفاتيح
    const products = generateDummyProducts(50);
    
    // تطبيق خوارزمية التصفية وحساب درجة الثقة
    const filteredProducts = products.filter(product => {
      // التحقق من الشروط الأساسية
      if (product.rating >= 4.0 && 
          product.reviewCount >= 100 && 
          product.sold >= 50 && 
          (product.sellerRank === 'Gold' || product.sellerRank === 'Diamond')) {
        
        // حساب درجة الثقة
        const ratingScore = Math.min(product.rating / 5.0, 1.0);
        const reviewsScore = Math.min(product.reviewCount / 1000.0, 1.0);
        const salesScore = Math.min(product.sold / 500.0, 1.0);
        
        const trustScore = 0.4 * ratingScore + 0.3 * reviewsScore + 0.3 * salesScore;
        
        if (trustScore >= 0.7) {
          product.trustScore = parseFloat(trustScore.toFixed(2));
          return true;
        }
      }
      return false;
    });
    
    // ترتيب المنتجات حسب درجة الثقة
    const sortedProducts = filteredProducts.sort((a, b) => b.trustScore - a.trustScore);
    
    return sortedProducts;
  } catch (error) {
    console.error("خطأ في جلب المنتجات:", error);
    return [];
  }
}

/**
 * توزيع المنتجات على الفئات
 * @param {Array} products - قائمة المنتجات
 * @returns {Object} المنتجات موزعة على الفئات
 */
function distributeProductsToCategories(products) {
  const categories = {
    top_rated: [],
    best_sellers: [],
    discounted: [],
    new_arrivals: [],
    premium: []
  };
  
  // نسخة من المنتجات للاستخدام في ملء الفئات الفارغة
  const remainingProducts = [...products];
  
  // توزيع المنتجات على الفئات حسب خصائصها
  for (const product of products) {
    let assigned = false;
    
    if (product.rating >= 4.7 && categories.top_rated.length < 10) {
      categories.top_rated.push(product);
      assigned = true;
    }
    
    if (product.sold > 500 && categories.best_sellers.length < 10) {
      categories.best_sellers.push(product);
      assigned = true;
    }
    
    if (product.discountRate >= 20 && categories.discounted.length < 10) {
      categories.discounted.push(product);
      assigned = true;
    }
    
    if (product.isNew && categories.new_arrivals.length < 10) {
      categories.new_arrivals.push(product);
      assigned = true;
    }
    
    if (product.sellerRank === 'Diamond' && categories.premium.length < 10) {
      categories.premium.push(product);
      assigned = true;
    }
    
    // إزالة المنتج من القائمة المتبقية إذا تم تعيينه لفئة واحدة على الأقل
    if (assigned) {
      const index = remainingProducts.findIndex(p => p.id === product.id);
      if (index !== -1) {
        remainingProducts.splice(index, 1);
      }
    }
  }
  
  // ملء الفئات الفارغة أو التي تحتوي على عدد قليل من المنتجات
  for (const category in categories) {
    if (categories[category].length < 5 && remainingProducts.length > 0) {
      const needed = 5 - categories[category].length;
      const productsToAdd = remainingProducts.splice(0, Math.min(needed, remainingProducts.length));
      categories[category].push(...productsToAdd);
    }
  }
  
  return categories;
}

/**
 * تخزين المنتجات المصنفة في Redis
 * @param {Object} categorizedProducts - المنتجات المصنفة
 * @returns {Promise<boolean>} نجاح أو فشل العملية
 */
async function cacheProductsInRedis(categorizedProducts) {
  try {
    // تخزين المنتجات المصنفة
    const success = await setCache(OFFERS_CACHE_KEY, categorizedProducts, CACHE_EXPIRY);
    
    if (success) {
      console.log("تم تخزين المنتجات في Redis بنجاح");
    }
    
    return success;
  } catch (error) {
    console.error("خطأ في تخزين المنتجات في Redis:", error);
    return false;
  }
}

/**
 * استرجاع المنتجات المصنفة من Redis
 * @returns {Promise<Object|null>} المنتجات المصنفة أو null
 */
export async function getCachedProducts() {
  try {
    return await getCache(OFFERS_CACHE_KEY);
  } catch (error) {
    console.error("خطأ في استرجاع المنتجات من Redis:", error);
    return null;
  }
}

/**
 * تحديث المنتجات وتخزينها في Redis
 * @returns {Promise<Object>} المنتجات المصنفة
 */
export async function updateProducts() {
  try {
    console.log("بدء تحديث المنتجات...");
    
    // جلب المنتجات
    const products = await fetchTopProducts();
    
    if (products.length === 0) {
      console.error("لم يتم العثور على منتجات!");
      return null;
    }
    
    // توزيع المنتجات على الفئات
    const categorizedProducts = distributeProductsToCategories(products);
    
    // تخزين المنتجات في Redis
    await cacheProductsInRedis(categorizedProducts);
    
    console.log("اكتمل تحديث المنتجات");
    return categorizedProducts;
  } catch (error) {
    console.error("خطأ في تحديث المنتجات:", error);
    return null;
  }
}

/**
 * الحصول على المنتجات المصنفة (من التخزين المؤقت أو تحديثها)
 * @returns {Promise<Object>} المنتجات المصنفة
 */
export async function getProducts() {
  // محاولة استرجاع المنتجات من التخزين المؤقت
  const cachedProducts = await getCachedProducts();
  
  if (cachedProducts) {
    return cachedProducts;
  }
  
  // إذا لم تكن هناك منتجات مخزنة، نقوم بتحديثها
  return await updateProducts();
}

/**
 * إنشاء منتجات وهمية للاختبار
 * @param {number} count - عدد المنتجات
 * @returns {Array} قائمة المنتجات الوهمية
 */
function generateDummyProducts(count = 50) {
  const products = [];
  
  const categories = ['Electronics', 'Fashion', 'Home', 'Beauty', 'Sports'];
  const sellerRanks = ['Diamond', 'Gold', 'Silver'];
  
  for (let i = 1; i <= count; i++) {
    const category = categories[Math.floor(Math.random() * categories.length)];
    const rating = (4 + Math.random()).toFixed(1);
    const reviewCount = Math.floor(100 + Math.random() * 5000);
    const sold = Math.floor(50 + Math.random() * 10000);
    const discountRate = Math.floor(Math.random() * 50);
    const isNew = Math.random() > 0.7;
    const sellerRank = sellerRanks[Math.floor(Math.random() * sellerRanks.length)];
    
    products.push({
      id: `product-${i}`,
      title: `${category} Product ${i}`,
      price: (10 + Math.random() * 990).toFixed(2),
      discountRate: discountRate,
      rating: parseFloat(rating),
      reviewCount: reviewCount,
      sold: sold,
      sellerRank: sellerRank,
      isNew: isNew,
      productUrl: `https://example.com/product/${i}`,
      affiliateUrl: `https://example.com/product/${i}?tag=affiliate`,
      imageUrl: `https://picsum.photos/seed/product-${i}/300/300`,
    });
  }
  
  return products;
}