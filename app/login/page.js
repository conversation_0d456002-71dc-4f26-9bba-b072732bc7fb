'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from '../components/TranslationProvider';

export default function LoginPage() {
  const router = useRouter();
  const { t, language, direction } = useTranslation();
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
    // مسح رسالة الخطأ عند الكتابة
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };
  
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const validateForm = () => {
    const newErrors = {};
    
    // التحقق من البريد الإلكتروني
    if (!formData.email) {
      newErrors.email = t('common.errors.required');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t('common.errors.invalidEmail');
    }
    
    // التحقق من كلمة المرور
    if (!formData.password) {
      newErrors.password = t('common.errors.required');
    }
    
    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    const formErrors = validateForm();
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // في بيئة الإنتاج، ستقوم بإرسال البيانات إلى الخادم للتحقق
      // هنا نقوم بمحاكاة عملية تسجيل الدخول
      
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // للتوضيح فقط: التحقق من البريد الإلكتروني المخزن في localStorage
      const storedEmail = localStorage.getItem('user_email');
      
      if (storedEmail && storedEmail === formData.email) {
        // تخزين حالة تسجيل الدخول
        localStorage.setItem('is_authenticated', 'true');
        
        // إعادة توجيه المستخدم بعد تسجيل الدخول الناجح
        router.push('/dashboard');
      } else {
        // في حالة عدم وجود مستخدم مسجل، نقوم بتسجيل الدخول افتراضياً للتوضيح
        localStorage.setItem('user_email', formData.email);
        localStorage.setItem('is_authenticated', 'true');
        
        // إعادة توجيه المستخدم
        router.push('/dashboard');
      }
      
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error);
      setErrors({
        submit: t('common.errors.serverError')
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div style={containerStyle} dir={direction}>
      <div style={formContainerStyle}>
        <h1 style={titleStyle}>{t('auth.login.title')}</h1>
        
        {errors.submit && (
          <div style={errorMessageStyle}>
            {errors.submit}
          </div>
        )}
        
        <form onSubmit={handleSubmit} style={formStyle}>
          <div style={formGroupStyle}>
            <label htmlFor="email" style={labelStyle}>{t('common.labels.email')}</label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              style={{
                ...inputStyle,
                ...(errors.email ? inputErrorStyle : {})
              }}
              placeholder={t('auth.login.emailPlaceholder')}
              disabled={isSubmitting}
            />
            {errors.email && <p style={fieldErrorStyle}>{errors.email}</p>}
          </div>
          
          <div style={formGroupStyle}>
            <label htmlFor="password" style={labelStyle}>{t('common.labels.password')}</label>
            <div style={passwordContainerStyle}>
              <input
                type={showPassword ? "text" : "password"}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                style={{
                  ...inputStyle,
                  ...(errors.password ? inputErrorStyle : {}),
                  paddingRight: direction === 'rtl' ? '40px' : '15px',
                  paddingLeft: direction === 'rtl' ? '15px' : '40px'
                }}
                placeholder={t('auth.login.passwordPlaceholder')}
                disabled={isSubmitting}
              />
              <button 
                type="button" 
                onClick={togglePasswordVisibility} 
                style={{
                  ...eyeIconStyle,
                  right: direction === 'rtl' ? 'auto' : '10px',
                  left: direction === 'rtl' ? '10px' : 'auto'
                }}
                aria-label={showPassword ? "إخفاء كلمة المرور" : "إظهار كلمة المرور"}
              >
                <svg 
                  width="20" 
                  height="20" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                  style={{display: 'block'}}
                >
                  {showPassword ? (
                    <>
                      {/* أيقونة العين المفتوحة */}
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                      <circle cx="12" cy="12" r="3"></circle>
                    </>
                  ) : (
                    <>
                      {/* أيقونة العين المشطوبة */}
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                      <circle cx="12" cy="12" r="3"></circle>
                      <line x1="1" y1="1" x2="23" y2="23"></line>
                    </>
                  )}
                </svg>
              </button>
            </div>
            {errors.password && <p style={fieldErrorStyle}>{errors.password}</p>}
          </div>
          
          <button 
            type="submit" 
            style={{
              ...buttonStyle,
              ...(isSubmitting ? buttonDisabledStyle : {})
            }}
            disabled={isSubmitting}
          >
            {isSubmitting ? t('common.messages.processing') : t('auth.login.loginButton')}
          </button>
          
          <div style={forgotPasswordContainerStyle}>
            <a href="/reset-password" style={forgotPasswordLinkStyle}>{t('auth.login.forgotPassword')}</a>
          </div>
          
          <div style={registerLinkContainerStyle}>
            <span>{t('auth.login.noAccount')}</span>
            <a href="/register" style={registerLinkStyle}>{t('auth.login.createAccount')}</a>
          </div>
        </form>
      </div>
    </div>
  );
}

// الألوان
const aliexpressOrange = '#ff4747';
const aliexpressLightOrange = '#ff6e6e';
const aliexpressDarkOrange = '#e53935';

// الأنماط
const containerStyle = {
  maxWidth: '1200px',
  margin: '0 auto',
  padding: '20px',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  minHeight: '80vh',
};

const formContainerStyle = {
  backgroundColor: 'white',
  borderRadius: '8px',
  boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
  padding: '30px',
  width: '100%',
  maxWidth: '500px',
};

const titleStyle = {
  fontSize: '1.8rem',
  color: '#333',
  marginBottom: '25px',
  textAlign: 'center',
};

const formStyle = {
  width: '100%',
};

const formGroupStyle = {
  marginBottom: '20px',
};

const labelStyle = {
  display: 'block',
  marginBottom: '8px',
  fontSize: '0.95rem',
  color: '#333',
  fontWeight: '500',
};

const inputStyle = {
  width: '100%',
  padding: '12px 15px',
  fontSize: '1rem',
  border: '1px solid #ddd',
  borderRadius: '4px',
  transition: 'border-color 0.3s',
  outline: 'none',
  boxSizing: 'border-box',
};

const inputErrorStyle = {
  borderColor: '#e53935',
  backgroundColor: '#fff8f8',
};

const fieldErrorStyle = {
  color: '#e53935',
  fontSize: '0.85rem',
  marginTop: '5px',
  marginBottom: '0',
};

const buttonStyle = {
  width: '100%',
  padding: '12px',
  backgroundColor: aliexpressOrange,
  color: 'white',
  border: 'none',
  borderRadius: '4px',
  fontSize: '1rem',
  fontWeight: 'bold',
  cursor: 'pointer',
  transition: 'background-color 0.3s',
};

const buttonDisabledStyle = {
  backgroundColor: '#ccc',
  cursor: 'not-allowed',
};

const registerLinkContainerStyle = {
  marginTop: '20px',
  textAlign: 'center',
  fontSize: '0.95rem',
};

const registerLinkStyle = {
  color: aliexpressOrange,
  textDecoration: 'none',
  marginRight: '5px',
  fontWeight: 'bold',
};

const errorMessageStyle = {
  backgroundColor: '#ffebee',
  color: '#c62828',
  padding: '12px 15px',
  borderRadius: '4px',
  marginBottom: '20px',
  textAlign: 'center',
};

const forgotPasswordContainerStyle = {
  textAlign: 'center',
  marginTop: '15px',
  marginBottom: '15px',
};

const forgotPasswordLinkStyle = {
  color: '#666',
  textDecoration: 'none',
  fontSize: '0.9rem',
  transition: 'color 0.3s',
};

const passwordContainerStyle = {
  position: 'relative',
  width: '100%',
};

const eyeIconStyle = {
  position: 'absolute',
  top: '50%',
  transform: 'translateY(-50%)',
  background: 'none',
  border: 'none',
  cursor: 'pointer',
  fontSize: '1.2rem',
  color: '#666',
  padding: '0',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '30px',
  height: '30px',
};