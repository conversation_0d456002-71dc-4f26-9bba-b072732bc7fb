'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from '../components/TranslationProvider';

export default function RegisterPage() {
  const router = useRouter();
  const { t, direction } = useTranslation();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
    // Clear error message when typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };
  
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  
  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const validateForm = () => {
    const newErrors = {};
    
    // Email validation
    if (!formData.email) {
      newErrors.email = t('common.errors.required');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t('common.errors.invalidEmail');
    }
    
    // Password validation
    if (!formData.password) {
      newErrors.password = t('common.errors.required');
    } else if (formData.password.length < 6) {
      newErrors.password = t('settings.passwordLength');
    }
    
    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = t('common.errors.required');
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = t('common.errors.passwordMismatch');
    }
    
    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form data
    const formErrors = validateForm();
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // In production, you would send data to the server
      // Here we simulate the registration process
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Store user data in localStorage (for demonstration only)
      localStorage.setItem('user_email', formData.email);
      localStorage.setItem('is_authenticated', 'true');
      
      // Show success message
      setSuccessMessage(t('common.messages.created') + ' ' + t('common.messages.processing'));
      
      // Redirect user after successful registration
      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);
      
    } catch (error) {
      console.error('Registration error:', error);
      setErrors({
        submit: t('common.errors.serverError')
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div style={containerStyle} dir={direction}>
      <div style={formContainerStyle}>
        <h1 style={titleStyle}>{t('auth.register.title')}</h1>
        
        {successMessage && (
          <div style={successMessageStyle}>
            {successMessage}
          </div>
        )}
        
        {errors.submit && (
          <div style={errorMessageStyle}>
            {errors.submit}
          </div>
        )}
        
        <form onSubmit={handleSubmit} style={formStyle}>
          <div style={formGroupStyle}>
            <label htmlFor="email" style={labelStyle}>{t('common.labels.email')}</label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              style={{
                ...inputStyle,
                ...(errors.email ? inputErrorStyle : {}),
                ...(direction === 'rtl' ? { textAlign: 'right' } : {})
              }}
              placeholder={t('auth.register.emailPlaceholder')}
              disabled={isSubmitting}
            />
            {errors.email && <p style={fieldErrorStyle}>{errors.email}</p>}
          </div>
          
          <div style={formGroupStyle}>
            <label htmlFor="password" style={labelStyle}>{t('common.labels.password')}</label>
            <div style={passwordContainerStyle}>
              <input
                type={showPassword ? "text" : "password"}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                style={{
                  ...inputStyle,
                  ...(errors.password ? inputErrorStyle : {}),
                  ...(direction === 'rtl' ? { paddingRight: '40px', textAlign: 'right' } : { paddingLeft: '40px' })
                }}
                placeholder={t('auth.register.passwordPlaceholder')}
                disabled={isSubmitting}
              />
              <button 
                type="button" 
                onClick={togglePasswordVisibility} 
                style={{
                  ...eyeIconStyle,
                  ...(direction === 'rtl' ? { left: '10px', right: 'auto' } : { right: '10px', left: 'auto' })
                }}
                aria-label={showPassword ? t('common.labels.password') + ' ' + t('common.buttons.close') : t('common.labels.password') + ' ' + t('common.buttons.view')}
              >
                <svg 
                  width="20" 
                  height="20" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                  style={{display: 'block'}}
                >
                  {showPassword ? (
                    <>
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                      <circle cx="12" cy="12" r="3"></circle>
                    </>
                  ) : (
                    <>
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                      <circle cx="12" cy="12" r="3"></circle>
                      <line x1="1" y1="1" x2="23" y2="23"></line>
                    </>
                  )}
                </svg>
              </button>
            </div>
            {errors.password && <p style={fieldErrorStyle}>{errors.password}</p>}
          </div>
          
          <div style={formGroupStyle}>
            <label htmlFor="confirmPassword" style={labelStyle}>{t('common.labels.confirmPassword')}</label>
            <div style={passwordContainerStyle}>
              <input
                type={showConfirmPassword ? "text" : "password"}
                id="confirmPassword"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                style={{
                  ...inputStyle,
                  ...(errors.confirmPassword ? inputErrorStyle : {}),
                  ...(direction === 'rtl' ? { paddingRight: '40px', textAlign: 'right' } : { paddingLeft: '40px' })
                }}
                placeholder={t('auth.register.confirmPasswordPlaceholder')}
                disabled={isSubmitting}
              />
              <button 
                type="button" 
                onClick={toggleConfirmPasswordVisibility} 
                style={{
                  ...eyeIconStyle,
                  ...(direction === 'rtl' ? { left: '10px', right: 'auto' } : { right: '10px', left: 'auto' })
                }}
                aria-label={showConfirmPassword ? t('common.labels.confirmPassword') + ' ' + t('common.buttons.close') : t('common.labels.confirmPassword') + ' ' + t('common.buttons.view')}
              >
                <svg 
                  width="20" 
                  height="20" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                  style={{display: 'block'}}
                >
                  {showConfirmPassword ? (
                    <>
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                      <circle cx="12" cy="12" r="3"></circle>
                    </>
                  ) : (
                    <>
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                      <circle cx="12" cy="12" r="3"></circle>
                      <line x1="1" y1="1" x2="23" y2="23"></line>
                    </>
                  )}
                </svg>
              </button>
            </div>
            {errors.confirmPassword && <p style={fieldErrorStyle}>{errors.confirmPassword}</p>}
          </div>
          
          <button 
            type="submit" 
            style={{
              ...buttonStyle,
              ...(isSubmitting ? buttonDisabledStyle : {})
            }}
            disabled={isSubmitting}
          >
            {isSubmitting ? t('common.messages.processing') : t('auth.register.registerButton')}
          </button>
          
          <div style={loginLinkContainerStyle}>
            <span>{t('auth.register.hasAccount')}</span>
            <a href="/login" style={loginLinkStyle}>{t('auth.register.loginHere')}</a>
          </div>
        </form>
      </div>
    </div>
  );
}

// الألوان
const aliexpressOrange = '#ff4747';
const aliexpressLightOrange = '#ff6e6e';
const aliexpressDarkOrange = '#e53935';

// الأنماط
const containerStyle = {
  maxWidth: '1200px',
  margin: '0 auto',
  padding: '20px',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
};

const formContainerStyle = {
  backgroundColor: 'white',
  borderRadius: '8px',
  boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
  padding: '30px',
  width: '100%',
  maxWidth: '500px',
};

const titleStyle = {
  fontSize: '1.8rem',
  color: '#333',
  marginBottom: '25px',
  textAlign: 'center',
};

const formStyle = {
  width: '100%',
};

const formGroupStyle = {
  marginBottom: '20px',
};

const labelStyle = {
  display: 'block',
  marginBottom: '8px',
  fontSize: '0.95rem',
  color: '#333',
  fontWeight: '500',
};

const inputStyle = {
  width: '100%',
  padding: '12px 15px',
  fontSize: '1rem',
  border: '1px solid #ddd',
  borderRadius: '4px',
  transition: 'border-color 0.3s',
  outline: 'none',
};

const inputErrorStyle = {
  borderColor: '#e53935',
  backgroundColor: '#fff8f8',
};

const fieldErrorStyle = {
  color: '#e53935',
  fontSize: '0.85rem',
  marginTop: '5px',
  marginBottom: '0',
};

const buttonStyle = {
  width: '100%',
  padding: '12px',
  backgroundColor: aliexpressOrange,
  color: 'white',
  border: 'none',
  borderRadius: '4px',
  fontSize: '1rem',
  fontWeight: 'bold',
  cursor: 'pointer',
  transition: 'background-color 0.3s',
};

const buttonDisabledStyle = {
  backgroundColor: '#ccc',
  cursor: 'not-allowed',
};

const loginLinkContainerStyle = {
  marginTop: '20px',
  textAlign: 'center',
  fontSize: '0.95rem',
};

const loginLinkStyle = {
  color: aliexpressOrange,
  textDecoration: 'none',
  marginRight: '5px',
  fontWeight: 'bold',
};

const successMessageStyle = {
  backgroundColor: '#e8f5e9',
  color: '#2e7d32',
  padding: '12px 15px',
  borderRadius: '4px',
  marginBottom: '20px',
  textAlign: 'center',
};

const errorMessageStyle = {
  backgroundColor: '#ffebee',
  color: '#c62828',
  padding: '12px 15px',
  borderRadius: '4px',
  marginBottom: '20px',
  textAlign: 'center',
};

const passwordContainerStyle = {
  position: 'relative',
  width: '100%',
};

const eyeIconStyle = {
  position: 'absolute',
  top: '50%',
  left: '10px',
  transform: 'translateY(-50%)',
  background: 'none',
  border: 'none',
  cursor: 'pointer',
  fontSize: '1.2rem',
  color: '#666',
  padding: '0',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '30px',
  height: '30px',
};