# تطبيق متجر علي إكسبرس

## نظرة عامة
تطبيق ويب لعرض وبحث منتجات علي إكسبرس مع نظام تخزين مؤقت ذكي للبحث وواجهة مستخدم سهلة الاستخدام. يعرض النظام منتجات عشوائية حقيقية من نتائج البحث المخزنة مؤقتاً، مما يوفر تجربة مستخدم ديناميكية حتى بدون بحث نشط.

## الميزات
- بحث ذكي عن المنتجات مع خوارزمية تصفية متقدمة
- تصنيف النتائج (السعر، المبيعات، التقييم)
- عرض المنتجات بطريقة الشبكة أو القائمة
- **عرض منتجات عشوائية من التخزين المؤقت** - تجربة ديناميكية حتى بدون بحث
- تخزين مؤقت ذكي للبحث باستخدام Redis (مشاركة النتائج بين المستخدمين)
- لوحة إدارة منفصلة
- نظام ضغط الصور لتحسين الأداء وتقليل استهلاك البيانات
- نظام الطوابير لإدارة المهام الثقيلة

## التحديثات الجديدة في نظام التخزين المؤقت باستخدام Redis

1. **نظام تخزين مؤقت للعروض باستخدام Redis**:
   - تم تنفيذ نظام تخزين مؤقت للعروض باستخدام Redis.
   - يتم تخزين العروض في الذاكرة (RAM) لمدة 24 ساعة لتحسين الأداء.
   - لا حاجة للتخزين في MySQL، مما يقلل الحمل على قاعدة البيانات.

2. **جلب 50 منتج باللغة الإنجليزية والدولار فقط**:
   - تبسيط النظام بجلب 50 منتج فقط باللغة الإنجليزية والدولار.
   - تطبيق خوارزمية التصفية وحساب درجة الثقة على المنتجات.
   - توزيع المنتجات على 5 فئات مختلفة (الأعلى تقييماً، الأكثر مبيعاً، خصومات كبيرة، وصل حديثاً، منتجات مميزة).

3. **نظام تخزين مؤقت للبحث باستخدام Redis**:
   - تم تنفيذ نظام تخزين مؤقت مركزي لنتائج البحث باستخدام Redis.
   - يتم تخزين نتائج البحث لمدة 5 ساعات لتوفير مساحة الذاكرة.
   - مشاركة نتائج البحث بين جميع المستخدمين (إذا بحث مستخدم عن "هاتف"، يستفيد جميع المستخدمين من النتائج المخزنة).
   - **ميزة جديدة**: عرض منتجات عشوائية من جميع نتائج البحث المخزنة عند عدم وجود بحث نشط.

4. **تحسين الأداء**:
   - سرعة استجابة أعلى بكثير باستخدام Redis (0.1-1 مللي ثانية) مقارنة بـ التخزين المحلي.
   - تقليل عدد الطلبات إلى API علي إكسبرس بشكل كبير.
   - تحسين تجربة المستخدم من خلال استجابة أسرع للبحث.
   - **لا توجد صفحات فارغة**: عرض منتجات حقيقية حتى بدون بحث نشط.

5. **نقاط نهاية API محسنة**:
   - إضافة نقطة نهاية API جديدة لتحديث العروض يدوياً.
   - تحسين نقطة نهاية API للبحث لاستخدام التخزين المؤقت في Redis.
   - **نقطة نهاية جديدة**: `/api/random-products` لجلب منتجات عشوائية من التخزين المؤقت.
   - يمكن استخدامها من لوحة الإدارة أو من خلال مهمة مجدولة.

## الإعداد

### المتطلبات
- Node.js (الإصدار 14 أو أحدث)
- npm أو yarn
- Redis (الإصدار 6 أو أحدث) - مطلوب لنظام العروض المحدث

### التثبيت
1. استنساخ المستودع
```
git clone <repository-url>
```

2. تثبيت التبعيات
```
npm install
```

3. إنشاء ملف `.env.local` من النموذج
```
cp .env.local.example .env.local
```

4. تعديل ملف `.env.local` وإضافة إعدادات Redis ومفاتيح API الخاصة بك:
```
REDIS_URL=redis://localhost:6379
UPDATE_SECRET_KEY=your_update_secret_key_here
```

5. تشغيل Redis:
```bash
# على Linux/Mac
redis-server

# أو باستخدام Docker
docker run -d -p 6379:6379 redis

# ملاحظة: Redis مطلوب لنظام المنتجات العشوائية
# عند أول تشغيل، ستظهر رسالة ترحيب حتى يبدأ المستخدمون في البحث
```

### التشغيل
```
npm run dev
```

### تحديث العروض يدوياً
يمكنك تحديث العروض يدوياً من خلال نقطة نهاية API:
```
http://localhost:3000/api/offers/update?secret=your_update_secret_key_here
```

### جدولة تحديث العروض
يمكنك استخدام cron job لتحديث العروض تلقائياً كل 24 ساعة:
```bash
# تحديث العروض كل 24 ساعة (الساعة 3 صباحاً)
0 3 * * * curl http://localhost:3000/api/offers/update?secret=your_update_secret_key_here
```

## الأمان

### مفاتيح API
- مفاتيح API محمية ولا يتم إرسالها إلى المتصفح
- جميع طلبات API تتم من خلال الخادم
- يتم تخزين المفاتيح في متغيرات بيئية آمنة

### الفصل بين واجهة المستخدم ولوحة الإدارة
- لوحة الإدارة منفصلة تمامًا عن واجهة المستخدم
- يجب تنفيذ نظام مصادقة قوي للوصول إلى لوحة الإدارة

### معالجة الأخطاء
- لا يتم عرض تفاصيل الأخطاء التقنية للمستخدمين
- يتم تسجيل الأخطاء داخليًا فقط
- يتم عرض رسائل خطأ مناسبة للمستخدمين

## الهيكل
- `/app` - تطبيق Next.js الرئيسي
- `/app/api` - نقاط نهاية API الداخلية
  - `/app/api/search` - نقطة نهاية البحث مع تخزين مؤقت ذكي
  - `/app/api/random-products` - **جديد**: نقطة نهاية المنتجات العشوائية من Redis
  - `/app/api/commission-products` - نقطة نهاية منتجات التخفيض بالعمولات
  - `/app/api/offers` - نقطة نهاية العروض
  - `/app/api/offers/update` - نقطة نهاية تحديث العروض
  - `/app/lib/redis.js` - وظائف التعامل مع Redis
  - `/app/services/offersService.js` - خدمة جلب وتوزيع المنتجات
  - `/app/api/image-optimizer` - نقطة نهاية تحسين الصور
  - `/app/api/queue` - نقطة نهاية نظام الطوابير
- `/app/components` - مكونات قابلة لإعادة الاستخدام
  - `/app/components/OptimizedImage.js` - مكون الصورة المحسنة
  - `/app/components/QueueManager.js` - مكون إدارة الطوابير
- `/app/search` - صفحة البحث
- `/app/commission-discounts` - صفحة منتجات التخفيض بالعمولات
- `/app/offers` - صفحة العروض
- `/app/utilities` - صفحة الأدوات المساعدة (ضغط الصور والطوابير)
- `/admin` - لوحة الإدارة (تطبيق Next.js منفصل)

## التطوير
- استخدام Next.js 14 مع App Router
- تنفيذ نظام تخزين مؤقت ذكي للبحث مع Redis
- **نظام منتجات عشوائية**: لا توجد بيانات وهمية، كل المنتجات حقيقية من API
- استخدام React Hooks للحالة وتأثيرات الجانب
- تحسين أداء الصور باستخدام نظام ضغط الصور
- إدارة المهام الثقيلة باستخدام نظام الطوابير

## كيف يعمل نظام المنتجات العشوائية:
1. **عند البحث**: يتم تخزين النتائج في Redis لمدة 5 ساعات
2. **عند عدم البحث**: يتم جلب منتجات عشوائية من جميع نتائج البحث المخزنة
3. **عند أول تشغيل**: تظهر رسالة ترحيب تشجع على البحث
4. **بعد أول بحث**: تصبح المنتجات متاحة للعرض العشوائي لجميع المستخدمين

## نظام ضغط الصور
نظام ضغط الصور يقوم بتحسين الصور وتقليل حجمها لتحسين أداء الموقع وتقليل استهلاك البيانات. يتضمن النظام:
- تحجيم الصور حسب الحاجة
- ضغط الصور مع الحفاظ على الجودة
- تحويل الصور إلى تنسيقات أكثر كفاءة (WebP)
- تخزين مؤقت للصور المحسنة
- معالجة أخطاء تحميل الصور

## نظام الطوابير
نظام الطوابير يقوم بإدارة المهام الثقيلة وتنفيذها في الخلفية لتحسين أداء الموقع. يتضمن النظام:
- طوابير متعددة للمهام المختلفة (معالجة الصور، طلبات API، تصدير البيانات)
- تتبع حالة المهام
- معالجة الأخطاء والمحاولات المتكررة
- واجهة برمجة تطبيقات (API) لإضافة المهام ومتابعة حالتها