# تعليمات إعداد Prisma

## 1. تثبيت Prisma ومتطلباتها

قم بتنفيذ الأمر التالي لتثبيت Prisma ومتطلباتها:

```bash
npm install prisma @prisma/client bcryptjs
```

## 2. إنشاء قاعدة البيانات

قم بإنشاء قاعدة بيانات MySQL جديدة باسم `assistantbb`:

```sql
CREATE DATABASE assistantbb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 3. تهيئة Prisma

بعد تثبيت Prisma، قم بتنفيذ الأوامر التالية لتهيئة قاعدة البيانات:

```bash
# إنشاء عميل Prisma
npx prisma generate

# إنشاء ترحيلات قاعدة البيانات
npx prisma migrate dev --name init

# إنشاء البيانات الأولية
npx prisma db seed
```

## 4. تشغيل Prisma Studio (اختياري)

يمكنك تشغيل Prisma Studio لإدارة قاعدة البيانات بواجهة رسومية:

```bash
npx prisma studio
```

## ملاحظات هامة

1. تأكد من تعديل ملف `.env` بمعلومات الاتصال الصحيحة لقاعدة البيانات.
2. يجب أن يكون لديك MySQL مثبت ويعمل على جهازك.
3. بيانات تسجيل دخول المسؤول الافتراضية:
   - البريد الإلكتروني: `<EMAIL>`
   - كلمة المرور: `admin123`
   - التوكن: `admin_secure_token`