#!/usr/bin/env node

/**
 * Login Page Check
 * Checks login page functionality and translations
 */

const fs = require('fs');

// Load Arabic translation file
function loadArabicTranslations() {
  try {
    const content = fs.readFileSync('./app/translations/ar.js', 'utf8');
    const match = content.match(/const ar = ({[\s\S]*});/);
    if (!match) {
      throw new Error('Could not parse Arabic translation file');
    }
    return eval(`(${match[1]})`);
  } catch (error) {
    console.error('Error loading Arabic translations:', error.message);
    return null;
  }
}

// Check if a nested key exists
function hasKey(obj, keyPath) {
  const keys = keyPath.split('.');
  let current = obj;
  
  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return false;
    }
  }
  
  return true;
}

// Check login page file
function checkLoginPageFile() {
  try {
    const content = fs.readFileSync('./app/login/page.js', 'utf8');
    
    // Check if it uses translation system
    const usesTranslation = content.includes('useTranslation');
    const usesTranslationKeys = content.includes("t('");
    
    return {
      exists: true,
      usesTranslation,
      usesTranslationKeys,
      content
    };
  } catch (error) {
    return {
      exists: false,
      error: error.message
    };
  }
}

// Main check function
function checkLoginPage() {
  console.log('🔍 فحص صفحة تسجيل الدخول...\n');
  
  // Check translations
  const translations = loadArabicTranslations();
  if (!translations) {
    console.log('❌ فشل في تحميل ملف الترجمة العربي');
    return false;
  }
  
  // Check login page file
  const loginPage = checkLoginPageFile();
  if (!loginPage.exists) {
    console.log('❌ ملف صفحة تسجيل الدخول غير موجود');
    console.log(`   خطأ: ${loginPage.error}`);
    return false;
  }
  
  console.log('✅ ملف صفحة تسجيل الدخول موجود');
  console.log(`✅ يستخدم نظام الترجمة: ${loginPage.usesTranslation ? 'نعم' : 'لا'}`);
  console.log(`✅ يستخدم مفاتيح الترجمة: ${loginPage.usesTranslationKeys ? 'نعم' : 'لا'}\n`);
  
  // Critical login translation keys
  const loginKeys = [
    'auth.login.title',
    'auth.login.emailPlaceholder',
    'auth.login.passwordPlaceholder',
    'auth.login.loginButton',
    'auth.login.forgotPassword',
    'auth.login.noAccount',
    'auth.login.createAccount',
    'common.labels.email',
    'common.labels.password',
    'common.errors.required',
    'common.errors.invalidEmail',
    'common.errors.serverError',
    'common.messages.processing'
  ];
  
  let missingKeys = [];
  let presentKeys = [];
  
  for (const key of loginKeys) {
    if (hasKey(translations, key)) {
      presentKeys.push(key);
    } else {
      missingKeys.push(key);
    }
  }
  
  console.log(`📊 نتائج فحص الترجمات:`);
  console.log(`✅ المفاتيح الموجودة: ${presentKeys.length}`);
  console.log(`❌ المفاتيح المفقودة: ${missingKeys.length}\n`);
  
  if (missingKeys.length > 0) {
    console.log('❌ مفاتيح الترجمة المفقودة:');
    missingKeys.forEach(key => console.log(`   - ${key}`));
    console.log('');
  }
  
  // Check specific translation values
  console.log('🔍 فحص قيم الترجمات المحددة:\n');
  
  const specificChecks = [
    { key: 'auth.login.title', expected: 'تسجيل الدخول' },
    { key: 'common.labels.email', expected: 'البريد الإلكتروني' },
    { key: 'common.labels.password', expected: 'كلمة المرور' },
    { key: 'auth.login.loginButton', expected: 'تسجيل الدخول' }
  ];
  
  for (const check of specificChecks) {
    if (hasKey(translations, check.key)) {
      const keys = check.key.split('.');
      let value = translations;
      for (const k of keys) {
        value = value[k];
      }
      
      const matches = value === check.expected;
      const status = matches ? '✅' : '⚠️';
      console.log(`${status} ${check.key}: "${value}" ${matches ? '' : `(متوقع: "${check.expected}")`}`);
    } else {
      console.log(`❌ ${check.key}: غير موجود`);
    }
  }
  
  console.log('\n🎯 التوصيات:');
  
  if (missingKeys.length === 0 && loginPage.usesTranslation) {
    console.log('✅ صفحة تسجيل الدخول مكتملة ومترجمة بالكامل!');
    console.log('✅ جميع الترجمات المطلوبة موجودة');
    console.log('✅ الصفحة تستخدم نظام الترجمة بشكل صحيح');
  } else {
    if (!loginPage.usesTranslation) {
      console.log('⚠️  الصفحة لا تستخدم نظام الترجمة');
    }
    if (missingKeys.length > 0) {
      console.log('⚠️  يوجد ترجمات مفقودة');
    }
  }
  
  console.log('\n🚀 الحالة العامة:');
  const completionRate = ((presentKeys.length / loginKeys.length) * 100).toFixed(1);
  console.log(`📈 نسبة اكتمال الترجمات: ${completionRate}%`);
  
  if (completionRate >= 95 && loginPage.usesTranslation) {
    console.log('🎉 ممتاز! صفحة تسجيل الدخول جاهزة تماماً');
  } else if (completionRate >= 80) {
    console.log('👍 جيد! صفحة تسجيل الدخول تعمل بشكل جيد');
  } else {
    console.log('⚠️  تحتاج صفحة تسجيل الدخول إلى تحسينات');
  }
  
  // Check for common issues
  console.log('\n🔧 فحص المشاكل الشائعة:');
  
  if (loginPage.content.includes('404')) {
    console.log('❌ تم العثور على خطأ 404 في الكود');
  } else {
    console.log('✅ لا توجد أخطاء 404 ظاهرة في الكود');
  }
  
  if (loginPage.content.includes('useRouter')) {
    console.log('✅ يستخدم التوجيه بشكل صحيح');
  } else {
    console.log('⚠️  قد لا يستخدم التوجيه بشكل صحيح');
  }
  
  if (loginPage.content.includes('dir={direction}')) {
    console.log('✅ يدعم اتجاه النص (RTL/LTR)');
  } else {
    console.log('⚠️  قد لا يدعم اتجاه النص بشكل كامل');
  }
  
  return missingKeys.length === 0 && loginPage.usesTranslation;
}

// Run the check
if (require.main === module) {
  const isComplete = checkLoginPage();
  process.exit(isComplete ? 0 : 1);
}

module.exports = { checkLoginPage };