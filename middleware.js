import { NextResponse } from 'next/server';
import { initServices } from './app/lib/init';

// تهيئة الخدمات عند بدء التطبيق
let initialized = false;

export async function middleware(request) {
  // تهيئة الخدمات مرة واحدة فقط عند بدء التطبيق
  if (!initialized) {
    try {
      await initServices();
      initialized = true;
    } catch (error) {
      console.error('خطأ في تهيئة الخدمات:', error);
    }
  }
  
  return NextResponse.next();
}

// تطبيق الـ middleware على جميع الطلبات
export const config = {
  matcher: '/((?!_next/static|_next/image|favicon.ico).*)',
};