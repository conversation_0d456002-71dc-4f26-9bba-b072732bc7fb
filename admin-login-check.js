#!/usr/bin/env node

/**
 * Admin Login Page Check
 * Checks admin login page functionality and translations
 */

const fs = require('fs');
const path = require('path');

// Check if admin login page exists
function checkAdminLoginPage() {
  const adminLoginPath = './app/admin-panel-95/admin-login/page.js';
  const adminLoginLayoutPath = './app/admin-panel-95/admin-login/layout.js';
  
  try {
    const pageExists = fs.existsSync(adminLoginPath);
    const layoutExists = fs.existsSync(adminLoginLayoutPath);
    
    if (!pageExists) {
      return {
        exists: false,
        error: 'Admin login page file not found'
      };
    }
    
    if (!layoutExists) {
      return {
        exists: false,
        error: 'Admin login layout file not found'
      };
    }
    
    const pageContent = fs.readFileSync(adminLoginPath, 'utf8');
    const layoutContent = fs.readFileSync(adminLoginLayoutPath, 'utf8');
    
    return {
      exists: true,
      pageContent,
      layoutContent,
      usesTranslation: pageContent.includes('useTranslation'),
      usesTranslationKeys: pageContent.includes("t('"),
      hasFormValidation: pageContent.includes('validateForm'),
      hasErrorHandling: pageContent.includes('setErrors'),
      hasRouting: pageContent.includes('useRouter')
    };
  } catch (error) {
    return {
      exists: false,
      error: error.message
    };
  }
}

// Load Arabic translation file
function loadArabicTranslations() {
  try {
    const content = fs.readFileSync('./app/translations/ar.js', 'utf8');
    const match = content.match(/const ar = ({[\s\S]*});/);
    if (!match) {
      throw new Error('Could not parse Arabic translation file');
    }
    return eval(`(${match[1]})`);
  } catch (error) {
    console.error('Error loading Arabic translations:', error.message);
    return null;
  }
}

// Check if a nested key exists
function hasKey(obj, keyPath) {
  const keys = keyPath.split('.');
  let current = obj;
  
  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return false;
    }
  }
  
  return true;
}

// Main check function
function checkAdminLogin() {
  console.log('🔍 فحص صفحة تسجيل الدخول للإدارة...\n');
  
  // Check if admin login page exists
  const adminLoginCheck = checkAdminLoginPage();
  
  if (!adminLoginCheck.exists) {
    console.log('❌ صفحة تسجيل الدخول للإدارة غير موجودة');
    console.log(`   خطأ: ${adminLoginCheck.error}`);
    return false;
  }
  
  console.log('✅ ملف صفحة تسجيل الدخول للإدارة موجود');
  console.log(`✅ يستخدم نظام الترجمة: ${adminLoginCheck.usesTranslation ? 'نعم' : 'لا'}`);
  console.log(`✅ يستخدم مفاتيح الترجمة: ${adminLoginCheck.usesTranslationKeys ? 'نعم' : 'لا'}`);
  console.log(`✅ يحتوي على التحقق من البيانات: ${adminLoginCheck.hasFormValidation ? 'نعم' : 'لا'}`);
  console.log(`✅ يحتوي على معالجة الأخطاء: ${adminLoginCheck.hasErrorHandling ? 'نعم' : 'لا'}`);
  console.log(`✅ يحتوي على التوجيه: ${adminLoginCheck.hasRouting ? 'نعم' : 'لا'}\n`);
  
  // Check translations
  const translations = loadArabicTranslations();
  if (!translations) {
    console.log('❌ فشل في تحميل ملف الترجمة العربي');
    return false;
  }
  
  // Required translation keys for admin login
  const requiredKeys = [
    'adminPanel.adminLogin.title',
    'adminPanel.adminLogin.subtitle',
    'adminPanel.adminLogin.loginButton',
    'adminPanel.adminLogin.backToSite',
    'common.labels.email',
    'common.labels.password',
    'common.errors.required',
    'common.errors.invalidEmail',
    'common.errors.unauthorized',
    'common.errors.serverError',
    'common.messages.processing',
    'auth.login.emailPlaceholder',
    'auth.login.passwordPlaceholder'
  ];
  
  let missingKeys = [];
  let presentKeys = [];
  
  for (const key of requiredKeys) {
    if (hasKey(translations, key)) {
      presentKeys.push(key);
    } else {
      missingKeys.push(key);
    }
  }
  
  console.log(`📊 نتائج فحص الترجمات:`);
  console.log(`✅ المفاتيح الموجودة: ${presentKeys.length}`);
  console.log(`❌ المفاتيح المفقودة: ${missingKeys.length}\n`);
  
  if (missingKeys.length > 0) {
    console.log('❌ مفاتيح الترجمة المفقودة:');
    missingKeys.forEach(key => console.log(`   - ${key}`));
    console.log('');
  }
  
  // Check specific translation values
  console.log('🔍 فحص قيم الترجمات المحددة:\n');
  
  const specificChecks = [
    { key: 'adminPanel.adminLogin.title', expected: 'تسجيل دخول الإدارة' },
    { key: 'adminPanel.adminLogin.subtitle', expected: 'سجل دخولك للوصول إلى لوحة الإدارة' },
    { key: 'adminPanel.adminLogin.loginButton', expected: 'تسجيل الدخول' },
    { key: 'adminPanel.adminLogin.backToSite', expected: 'العودة إلى الموقع' }
  ];
  
  for (const check of specificChecks) {
    if (hasKey(translations, check.key)) {
      const keys = check.key.split('.');
      let value = translations;
      for (const k of keys) {
        value = value[k];
      }
      
      const matches = value === check.expected;
      const status = matches ? '✅' : '⚠️';
      console.log(`${status} ${check.key}: "${value}" ${matches ? '' : `(متوقع: "${check.expected}")`}`);
    } else {
      console.log(`❌ ${check.key}: غير موجود`);
    }
  }
  
  // Check CSS styles
  console.log('\n🎨 فحص ملف الأنماط:');
  
  try {
    const cssPath = './app/admin-panel-95/admin-styles.css';
    const cssExists = fs.existsSync(cssPath);
    
    if (cssExists) {
      const cssContent = fs.readFileSync(cssPath, 'utf8');
      const hasAdminLoginStyles = cssContent.includes('.admin-login-container');
      const hasFormStyles = cssContent.includes('.form-input');
      const hasErrorStyles = cssContent.includes('.error-message');
      
      console.log(`✅ ملف الأنماط موجود: ${cssExists ? 'نعم' : 'لا'}`);
      console.log(`✅ يحتوي على أنماط تسجيل الدخ��ل: ${hasAdminLoginStyles ? 'نعم' : 'لا'}`);
      console.log(`✅ يحتوي على أنماط النماذج: ${hasFormStyles ? 'نعم' : 'لا'}`);
      console.log(`✅ يحتوي على أنماط الأخطاء: ${hasErrorStyles ? 'نعم' : 'لا'}`);
    } else {
      console.log('❌ ملف الأنماط غير موجود');
    }
  } catch (error) {
    console.log('❌ خطأ في فحص ملف الأنماط:', error.message);
  }
  
  console.log('\n🎯 التوصيات:');
  
  const allGood = missingKeys.length === 0 && 
                  adminLoginCheck.usesTranslation && 
                  adminLoginCheck.hasFormValidation && 
                  adminLoginCheck.hasErrorHandling;
  
  if (allGood) {
    console.log('✅ صفحة تسجيل الدخول للإدارة مكتملة ومترجمة بالكامل!');
    console.log('✅ جميع الترجمات المطلوبة موجودة');
    console.log('✅ الصفحة تستخدم نظام الترجمة بشكل صحيح');
    console.log('✅ التحقق من البيانات ومعالجة الأخطاء موجودان');
  } else {
    if (!adminLoginCheck.usesTranslation) {
      console.log('⚠️  الصفحة لا تستخدم نظام الترجمة');
    }
    if (missingKeys.length > 0) {
      console.log('⚠️  يوجد ترجمات مفقودة');
    }
    if (!adminLoginCheck.hasFormValidation) {
      console.log('⚠️  التحقق من البيانات مفقود');
    }
    if (!adminLoginCheck.hasErrorHandling) {
      console.log('⚠️  معالجة الأخطاء مفقودة');
    }
  }
  
  console.log('\n🚀 الحالة العامة:');
  const completionRate = ((presentKeys.length / requiredKeys.length) * 100).toFixed(1);
  console.log(`📈 نسبة اكتمال الترجمات: ${completionRate}%`);
  
  if (completionRate >= 95 && adminLoginCheck.usesTranslation) {
    console.log('🎉 ممتاز! صفحة تسجيل الدخول للإدارة جاهزة تماماً');
  } else if (completionRate >= 80) {
    console.log('👍 جيد! صفحة تسجيل الدخول للإدارة تعمل بشكل جيد');
  } else {
    console.log('⚠️  تحتاج صفحة تسجيل الدخول للإدارة إلى تحسينات');
  }
  
  console.log('\n📍 معلومات الوصول:');
  console.log('🔗 الرابط: /admin-panel-95/admin-login');
  console.log('📧 ا��بريد الإلكتروني التجريبي: <EMAIL>');
  console.log('🔑 كلمة المرور التجريبية: admin123');
  
  return allGood;
}

// Run the check
if (require.main === module) {
  const isComplete = checkAdminLogin();
  process.exit(isComplete ? 0 : 1);
}

module.exports = { checkAdminLogin };