#!/usr/bin/env node

/**
 * Translation Validation Script
 * This script validates translation files for consistency and completeness
 */

const fs = require('fs');
const path = require('path');

// Translation files to validate
const translationFiles = [
  './app/translations/ar.js',
  './app/translations/en.js',
  './app/translations/es.js',
  './app/translations/fr.js',
  './app/translations/uk.js'
];

// Function to extract all translation keys from an object
function extractKeys(obj, prefix = '') {
  const keys = [];
  
  for (const key in obj) {
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      keys.push(...extractKeys(obj[key], prefix ? `${prefix}.${key}` : key));
    } else {
      keys.push(prefix ? `${prefix}.${key}` : key);
    }
  }
  
  return keys;
}

// Function to load translation file
function loadTranslation(filePath) {
  try {
    // Read file content
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Extract the object from the file (simple approach)
    const match = content.match(/const \w+ = ({[\s\S]*});/);
    if (!match) {
      throw new Error(`Could not parse translation file: ${filePath}`);
    }
    
    // Use eval to parse the object (not recommended for production, but OK for this script)
    const translationObj = eval(`(${match[1]})`);
    return translationObj;
  } catch (error) {
    console.error(`Error loading ${filePath}:`, error.message);
    return null;
  }
}

// Main validation function
function validateTranslations() {
  console.log('🔍 Validating translation files...\n');
  
  const translations = {};
  const allKeys = new Set();
  
  // Load all translation files
  for (const filePath of translationFiles) {
    const lang = path.basename(filePath, '.js');
    const translation = loadTranslation(filePath);
    
    if (translation) {
      translations[lang] = translation;
      const keys = extractKeys(translation);
      keys.forEach(key => allKeys.add(key));
      console.log(`✅ Loaded ${lang}: ${keys.length} keys`);
    } else {
      console.log(`❌ Failed to load ${lang}`);
    }
  }
  
  console.log(`\n📊 Total unique keys found: ${allKeys.size}\n`);
  
  // Check for missing keys in each language
  const missingKeys = {};
  
  for (const lang in translations) {
    missingKeys[lang] = [];
    const langKeys = extractKeys(translations[lang]);
    const langKeySet = new Set(langKeys);
    
    for (const key of allKeys) {
      if (!langKeySet.has(key)) {
        missingKeys[lang].push(key);
      }
    }
  }
  
  // Report missing keys
  console.log('🔍 Missing Keys Report:\n');
  
  let hasIssues = false;
  for (const lang in missingKeys) {
    if (missingKeys[lang].length > 0) {
      hasIssues = true;
      console.log(`❌ ${lang.toUpperCase()} missing ${missingKeys[lang].length} keys:`);
      missingKeys[lang].forEach(key => console.log(`   - ${key}`));
      console.log('');
    } else {
      console.log(`✅ ${lang.toUpperCase()}: All keys present`);
    }
  }
  
  if (!hasIssues) {
    console.log('\n🎉 All translation files are consistent!');
  } else {
    console.log('\n⚠️  Translation files have inconsistencies that need to be fixed.');
  }
  
  // Check for commonly used keys that might be missing
  console.log('\n🔍 Checking for commonly used translation patterns...\n');
  
  const commonPatterns = [
    'search.title',
    'search.placeholder',
    'search.button',
    'search.filters.bestPrice',
    'search.filters.bestSelling',
    'search.filters.bestRated',
    'search.product.reviews',
    'search.product.sales',
    'search.product.freeShipping',
    'offers.title',
    'commissionDiscounts.title',
    'common.buttons.search',
    'navigation.search'
  ];
  
  for (const pattern of commonPatterns) {
    const missingInLangs = [];
    
    for (const lang in translations) {
      const keys = extractKeys(translations[lang]);
      if (!keys.includes(pattern)) {
        missingInLangs.push(lang);
      }
    }
    
    if (missingInLangs.length > 0) {
      console.log(`⚠️  "${pattern}" missing in: ${missingInLangs.join(', ')}`);
    } else {
      console.log(`✅ "${pattern}" present in all languages`);
    }
  }
  
  return !hasIssues;
}

// Run validation
if (require.main === module) {
  const isValid = validateTranslations();
  process.exit(isValid ? 0 : 1);
}

module.exports = { validateTranslations, extractKeys, loadTranslation };