#!/usr/bin/env node

/**
 * Complete System Check
 * Comprehensive check for all login pages and admin functionality
 */

console.log('🔍 الفحص الشامل للنظام\n');
console.log('=' .repeat(60));

// Import check functions
const { checkLoginPage } = require('./login-page-check');
const { checkAdminTranslations } = require('./admin-translation-check');
const { checkAdminLogin } = require('./admin-login-check');

async function runCompleteCheck() {
  let allPassed = true;
  const results = {};
  
  console.log('\n1️⃣ فحص صفحة تسجيل الدخول العادية:');
  console.log('-'.repeat(40));
  
  try {
    const loginResult = checkLoginPage();
    results.userLogin = loginResult;
    if (loginResult) {
      console.log('✅ صفحة تسجيل الدخول العادية: نجحت في جميع الاختبارات');
    } else {
      console.log('❌ صفحة تسجيل الدخول العادية: فشلت في بعض الاختبارات');
      allPassed = false;
    }
  } catch (error) {
    console.log('❌ خطأ في فحص صفحة تسجيل الدخول العادية:', error.message);
    results.userLogin = false;
    allPassed = false;
  }
  
  console.log('\n2️⃣ فحص صفحة تسجيل الدخول للإدارة:');
  console.log('-'.repeat(40));
  
  try {
    const adminLoginResult = checkAdminLogin();
    results.adminLogin = adminLoginResult;
    if (adminLoginResult) {
      console.log('✅ صفحة تسجيل الدخول للإدارة: نجحت في جميع الاختبارات');
    } else {
      console.log('❌ صفحة تسجيل الدخول للإدارة: فشلت في بعض الاختبارات');
      allPassed = false;
    }
  } catch (error) {
    console.log('❌ خطأ في فحص صفحة تسجيل الدخول للإدارة:', error.message);
    results.adminLogin = false;
    allPassed = false;
  }
  
  console.log('\n3️⃣ فحص ترجمات صفحة الإدارة:');
  console.log('-'.repeat(40));
  
  try {
    const adminTranslationsResult = checkAdminTranslations();
    results.adminTranslations = adminTranslationsResult;
    if (adminTranslationsResult) {
      console.log('✅ ترجمات صفحة الإدارة: نجحت في جميع الاختبارات');
    } else {
      console.log('❌ ترجمات صفحة الإدارة: فشلت في بعض الا��تبارات');
      allPassed = false;
    }
  } catch (error) {
    console.log('❌ خطأ في فحص ترجمات صفحة الإدارة:', error.message);
    results.adminTranslations = false;
    allPassed = false;
  }
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 النتيجة النهائية الشاملة:');
  console.log('='.repeat(60));
  
  // Display detailed results
  console.log('\n📋 تفاصيل النتائج:');
  console.log(`🔐 صفحة تسجيل الدخول العادية: ${results.userLogin ? '✅ جاهزة' : '❌ تحتاج إصلاح'}`);
  console.log(`🔐 صفحة تسجيل الدخول للإدارة: ${results.adminLogin ? '✅ جاهزة' : '❌ تحتاج إصلاح'}`);
  console.log(`🌐 ترجمات صفحة الإدارة: ${results.adminTranslations ? '✅ مكتملة' : '❌ تحتاج إكمال'}`);
  
  if (allPassed) {
    console.log('\n🎉 تهانينا! جميع الاختبارات نجحت');
    console.log('✅ صفحة تسجيل الدخول العادية تعمل بشكل مثالي');
    console.log('✅ صفحة تسجيل الدخول للإدارة تعمل بشكل مثالي');
    console.log('✅ ترجمات صفحة الإدارة مكتملة');
    console.log('�� النظام جاهز للاستخدام بالكامل');
    
    console.log('\n🚀 روابط الوصول:');
    console.log('👤 تسجيل الدخول العادي: /login');
    console.log('🔧 تسجيل الدخول للإدارة: /admin-panel-95/admin-login');
    console.log('📊 لوحة تحكم الإدارة: /admin-panel-95/dashboard');
    
    console.log('\n🔑 بيانات تجريبية:');
    console.log('📧 البريد الإلكتروني: <EMAIL>');
    console.log('🔒 كلمة المرور: admin123');
    
  } else {
    console.log('\n⚠️  يوجد بعض المشاكل التي تحتاج إلى إصلاح');
    console.log('📝 راجع التفاصيل أعلاه لمعرفة المشاكل المحددة');
    
    console.log('\n🔧 خطوات الإصلاح المقترحة:');
    if (!results.userLogin) {
      console.log('1. تحقق من صفحة تسجيل الدخول العادية (/login)');
    }
    if (!results.adminLogin) {
      console.log('2. تحقق من صفحة تسجيل الدخول للإدارة (/admin-panel-95/admin-login)');
    }
    if (!results.adminTranslations) {
      console.log('3. أكمل ترجمات صفحة الإدارة');
    }
  }
  
  console.log('\n📁 الملفات المهمة:');
  console.log('- /app/login/page.js (صفحة تسجيل الدخول العادية)');
  console.log('- /app/admin-panel-95/admin-login/page.js (صفحة تسجيل الدخول للإدارة)');
  console.log('- /app/translations/ar.js (الترجمات العربية)');
  console.log('- /app/admin-panel-95/admin-styles.css (أنماط الإدارة)');
  
  console.log('\n🛠️  أدوات التحقق المتاحة:');
  console.log('- node login-page-check.js (فحص صفحة تسجيل الدخول العادية)');
  console.log('- node admin-login-check.js (فحص صفحة تسجيل الدخول للإدارة)');
  console.log('- node admin-translation-check.js (فحص ترجمات الإدارة)');
  console.log('- node translation-validator.js (فحص شامل للترجمات)');
  console.log('- node complete-system-check.js (هذا الفحص الشامل)');
  
  console.log('\n📈 إحصائيات النجاح:');
  const successCount = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  const successRate = ((successCount / totalTests) * 100).toFixed(1);
  console.log(`🎯 معدل النجاح: ${successRate}% (${successCount}/${totalTests})`);
  
  if (successRate >= 100) {
    console.log('🏆 مثالي! جميع الاختبارات ن��حت');
  } else if (successRate >= 80) {
    console.log('👍 جيد جداً! معظم الاختبارات نجحت');
  } else if (successRate >= 60) {
    console.log('👌 جيد! أكثر من نصف الاختبارات نجحت');
  } else {
    console.log('⚠️  يحتاج إلى المزيد من العمل');
  }
  
  return allPassed;
}

// Run the complete check
if (require.main === module) {
  runCompleteCheck().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('خطأ في الفحص الشامل:', error);
    process.exit(1);
  });
}

module.exports = { runCompleteCheck };