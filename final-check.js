#!/usr/bin/env node

/**
 * Final System Check
 * Comprehensive check for login page and admin translations
 */

console.log('🔍 الفحص النهائي للنظام\n');
console.log('=' .repeat(50));

// Import check functions
const { checkLoginPage } = require('./login-page-check');
const { checkAdminTranslations } = require('./admin-translation-check');

async function runFinalCheck() {
  let allPassed = true;
  
  console.log('\n1️⃣ فحص صفحة تسجيل الدخول:');
  console.log('-'.repeat(30));
  
  try {
    const loginResult = checkLoginPage();
    if (loginResult) {
      console.log('✅ صفحة تسجيل الدخول: نجحت في جميع الاختبارات');
    } else {
      console.log('❌ صفحة تسجيل الدخول: فشلت في بعض الاختبارات');
      allPassed = false;
    }
  } catch (error) {
    console.log('❌ خطأ في فحص صفحة تسجيل الدخول:', error.message);
    allPassed = false;
  }
  
  console.log('\n2️⃣ فحص ترجمات صفحة الإدارة:');
  console.log('-'.repeat(30));
  
  try {
    const adminResult = checkAdminTranslations();
    if (adminResult) {
      console.log('✅ ترجمات الإدارة: نجحت في جميع الاختبارات');
    } else {
      console.log('❌ ترجمات الإدارة: فشلت في بعض الاختبارات');
      allPassed = false;
    }
  } catch (error) {
    console.log('❌ خطأ في فحص ترجمات الإدارة:', error.message);
    allPassed = false;
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 النتيجة النهائية:');
  console.log('='.repeat(50));
  
  if (allPassed) {
    console.log('🎉 تهانينا! جميع الاختبارات نجحت');
    console.log('✅ صفحة تسجيل الدخول تعمل بشكل مثالي');
    console.log('✅ ترجمات صفحة الإدارة مكتملة');
    console.log('✅ النظام جاهز للاستخدام');
    
    console.log('\n🚀 الخطوات التالية:');
    console.log('1. اختبر صفحة تسجيل الدخول في المتصفح');
    console.log('2. تأكد من عمل صفحة الإدارة');
    console.log('3. اختبر التنقل بين الصفحات');
    
  } else {
    console.log('⚠️  يوجد بعض المشاكل التي تحتاج إلى إصلاح');
    console.log('📝 راجع التفاصيل أعلاه لمعرفة المشاكل المحددة');
  }
  
  console.log('\n📁 الملفات المهمة:');
  console.log('- /app/login/page.js (صفحة تسجيل الدخول)');
  console.log('- /app/translations/ar.js (الترجمات العربية)');
  console.log('- /app/admin-panel-95/ (صفحات الإدارة)');
  
  console.log('\n🛠️  أدوات التحقق:');
  console.log('- node login-page-check.js');
  console.log('- node admin-translation-check.js');
  console.log('- node translation-validator.js');
  
  return allPassed;
}

// Run the final check
if (require.main === module) {
  runFinalCheck().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('خطأ في الفحص النهائي:', error);
    process.exit(1);
  });
}

module.exports = { runFinalCheck };