# تشغيل المشروع باستخدام Docker

هذا الدليل يشرح كيفية تشغيل المشروع باستخدام Docker و Docker Compose في أي بيئة.

## المتطلبات

- [Docker](https://docs.docker.com/get-docker/)
- [Docker Compose](https://docs.docker.com/compose/install/) (اختياري، للتشغيل المحلي)

## خطوات التشغيل المحلي

1. قم بنسخ ملف `.env.example` إلى `.env`:

```bash
cp .env.example .env
```

2. قم بتعديل ملف `.env` لتعيين القيم المناسبة للمتغيرات البيئية.

3. قم ببناء وتشغيل الحاويات باستخدام Docker Compose:

```bash
docker-compose up -d
```

4. قم بتهيئة قاعدة البيانات:

```bash
docker-compose exec app npm run db:init
```

5. يمكنك الوصول إلى التطبيق على العنوان التالي:

```
http://localhost:4000
```

## خطوات النشر على أي منصة تدعم Docker

1. تأكد من أن لديك ملف Dockerfile في مجلد المشروع.

2. قم بتكوين متغيرات البيئة المطلوبة:
   - `DATABASE_URL`: رابط قاعدة البيانات
   - `REDIS_URL`: رابط خادم Redis
   - `UPDATE_SECRET_KEY`: مفتاح سري لتحديث المنتجات
   - `REFRESH_SECRET_KEY`: مفتاح سري لتحديث العروض
   - `INIT_DB_SECRET_KEY`: مفتاح سري لتهيئة قاعدة البيانات
   - وأي متغيرات بيئة أخرى مطلوبة للتطبيق

3. قم ببناء صورة Docker:

```bash
docker build -t my-nextjs-app .
```

4. قم بتشغيل الحاوية:

```bash
docker run -p 4000:4000 --env-file .env my-nextjs-app
```

## النشر على منصات مختلفة

### النشر على Coolify

1. قم بإضافة المشروع في Coolify:
   - اختر "Docker" كنوع النشر
   - حدد "Dockerfile" كطريقة البناء
   - تأكد من تعيين المنفذ الصحيح (4000)

2. قم بتكوين متغيرات البيئة المطلوبة.

3. قم بنشر التطبيق من خلال واجهة Coolify.

### النشر على Heroku

1. قم بتثبيت Heroku CLI وتسجيل الدخول.

2. قم بإنشاء تطبيق Heroku:

```bash
heroku create
```

3. قم بإضافة متغيرات البيئة:

```bash
heroku config:set NODE_ENV=production
heroku config:set DATABASE_URL=your_database_url
# أضف باقي المتغيرات
```

4. قم بالنشر:

```bash
git push heroku main
```

### النشر على DigitalOcean App Platform

1. قم بإنشاء تطبيق جديد في DigitalOcean App Platform.
2. اختر GitHub كمصدر للكود.
3. حدد مجلد المشروع.
4. قم بتكوين متغيرات البيئة المطلوبة.
5. قم بنشر التطبيق.

## أوامر مفيدة للتشغيل المحلي

- عرض سجلات التطبيق:

```bash
docker-compose logs -f app
```

- إيقاف الحاويات:

```bash
docker-compose down
```

- إعادة بناء وتشغيل الحاويات:

```bash
docker-compose up -d --build
```

- الدخول إلى حاوية التطبيق:

```bash
docker-compose exec app sh
```

- الدخول إلى حاوية قاعدة البيانات:

```bash
docker-compose exec db mysql -u root -p
```

## استكشاف الأخطاء وإصلاحها

- **مشكلة**: التطبيق لا يستطيع الاتصال بقاعدة البيانات.
  **الحل**: تأكد من أن متغير البيئة `DATABASE_URL` صحيح وأن قاعدة البيانات متاحة.

- **مشكلة**: التطبيق لا يستطيع الاتصال بخادم Redis.
  **الحل**: تأكد من أن متغير البيئة `REDIS_URL` صحيح وأن خادم Redis متاح.

- **مشكلة**: خطأ "Permission denied" عند تشغيل التطبيق.
  **الحل**: تأكد من أن المستخدم `nextjs` لديه الأذونات المناسبة للمجلدات.