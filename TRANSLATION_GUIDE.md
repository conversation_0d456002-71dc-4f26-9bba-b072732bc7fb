# Translation System Guide

## Overview
This document provides guidelines for maintaining a unified translation system across the AliExpress Shopping Assistant website.

## Translation Structure

### File Organization
- **Location**: `/app/translations/`
- **Files**: 
  - `ar.js` - Arabic (Primary language)
  - `en.js` - English
  - `es.js` - Spanish
  - `fr.js` - French
  - `uk.js` - Ukrainian

### Key Naming Convention

Translation keys follow a hierarchical structure using dot notation:

```javascript
{
  section: {
    subsection: {
      key: 'Translation value'
    }
  }
}
```

#### Main Sections:
1. **common** - Shared elements (buttons, labels, errors, messages)
2. **navigation** - Navigation menu items
3. **home** - Homepage content
4. **search** - Search functionality
5. **offers** - Offers and deals
6. **commissionDiscounts** - Commission discount products
7. **auth** - Authentication (login, register, etc.)
8. **dashboard** - User dashboard
9. **admin** - Admin panel
10. **analytics** - Analytics and statistics

## Critical Translation Keys

### Search Functionality (Fixed ✅)
These keys are now properly unified across all languages:

```javascript
search: {
  title: 'البحث', // Arabic
  placeholder: 'ابحث عن المنتجات أو أدخل رابط المنتج...',
  button: 'بحث',
  searching: 'جاري البحث...',
  noResults: 'لم يتم العثور على منتجات',
  tryDifferent: 'جرب استخدام كلمات بحث مختلفة أو تصفح الفئات',
  loading: 'جاري البحث عن المنتجات...',
  error: 'نواجه مشكلة فنية مؤقتة في خدمة البحث. يرجى المحاولة لاحقًا.',
  filters: {
    bestPrice: 'أفضل سعر',
    bestSelling: 'الأكثر مبيعًا',
    bestRated: 'الأعلى تقييمًا',
    viewModes: {
      grid: 'شبكة',
      list: 'قائمة'
    }
  },
  product: {
    freeShipping: 'شحن مجاني',
    reviews: 'مراجعة',
    sales: 'مبيعات',
    discount: 'خصم',
    viewDetails: 'عرض التفاصيل',
    addToCart: 'أضف إلى السلة',
    outOfStock: 'نفد من المخزون'
  }
}
```

## Usage in Components

### Using Translation Hook
```javascript
import { useTranslation } from '../components/TranslationProvider';

function MyComponent() {
  const { t, language, direction } = useTranslation();
  
  return (
    <div>
      <h1>{t('search.title')}</h1>
      <button>{t('search.button')}</button>
    </div>
  );
}
```

### Common Patterns
```javascript
// Simple translation
{t('common.buttons.save')}

// Translation with interpolation
{t('dashboard.welcome', { name: userName })}

// Conditional translation
{isLoading ? t('common.labels.loading') : t('search.button')}
```

## Validation

### Running Translation Validation
```bash
node translation-validator.js
```

This script will:
- Check for missing keys across all language files
- Report inconsistencies
- Validate common translation patterns
- Provide detailed reports for each language

### Current Status (After Fixes)
✅ **Search functionality** - All keys present in all languages
✅ **Core navigation** - Basic navigation keys unified
✅ **Common buttons and labels** - Standard UI elements consistent

⚠️ **Still needs work**:
- Admin panel translations (260+ missing keys in Arabic)
- Authentication flow translations
- Dashboard translations
- Analytics translations

## Best Practices

### 1. Key Naming
- Use descriptive, hierarchical keys
- Follow existing patterns
- Use camelCase for multi-word keys
- Group related translations together

### 2. Translation Values
- Keep translations concise but clear
- Maintain consistent tone across languages
- Consider cultural context
- Use proper punctuation for each language

### 3. Placeholders
- Use `{{variable}}` for interpolation
- Keep placeholder names consistent across languages
- Document required variables

### 4. Adding New Translations
1. Add the key to the English file first (as reference)
2. Add translations to all other language files
3. Run validation script to ensure consistency
4. Test in the application

### 5. Maintenance
- Run validation script before commits
- Update all languages when adding new features
- Review translations with native speakers when possible
- Keep backup files for major changes

## Common Issues and Solutions

### Issue 1: Missing Translation Keys
**Problem**: Component shows translation key instead of text
**Solution**: Add missing key to all translation files

### Issue 2: Inconsistent Key Names
**Problem**: Same functionality uses different key names
**Solution**: Standardize key names across components

### Issue 3: Hardcoded Text
**Problem**: Text is hardcoded in components instead of using translations
**Solution**: Replace with translation keys

### Issue 4: Syntax Errors in Translation Files
**Problem**: Missing commas, brackets, or quotes
**Solution**: Use proper JavaScript object syntax, validate with linter

## Translation Priority

### High Priority (Core Functionality)
1. ✅ Search functionality
2. ✅ Navigation menu
3. ✅ Common buttons and labels
4. 🔄 Authentication flow
5. 🔄 Product display

### Medium Priority
1. 🔄 Dashboard
2. 🔄 User profile
3. 🔄 Settings

### Low Priority
1. 🔄 Admin panel
2. 🔄 Analytics
3. 🔄 Advanced features

## Tools and Scripts

### Translation Validator (`translation-validator.js`)
- Validates all translation files
- Reports missing keys
- Checks for common patterns
- Provides detailed analysis

### Usage:
```bash
# Run validation
node translation-validator.js

# Exit code 0 = success, 1 = issues found
echo $?
```

## Contributing

When adding new features:
1. Add English translations first
2. Add Arabic translations (primary language)
3. Add other language translations
4. Run validation script
5. Test in application
6. Update this guide if needed

## Support

For translation-related issues:
1. Check this guide first
2. Run the validation script
3. Review existing patterns
4. Test changes thoroughly
5. Document any new patterns

---

**Last Updated**: $(date)
**Status**: Search functionality unified ✅, Admin panel translations in progress 🔄