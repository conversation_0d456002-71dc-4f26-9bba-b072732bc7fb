# syntax=docker/dockerfile:1.4
FROM node:18-alpine AS base

# تثبيت المتطلبات الأساسية
RUN apk add --no-cache libc6-compat openssl gcompat python3 g++ make
WORKDIR /app

# مرحلة التبعيات
FROM base AS deps
COPY package.json package-lock.json* .npmrc* ./
COPY prisma ./prisma/

# تحسين إعدادات npm (بدون cache-min المهمل)
RUN npm config set registry https://registry.npmjs.org/ \
    && npm config set fetch-timeout 300000 \
    && npm config set fetch-retry-mintimeout 20000 \
    && npm config set fetch-retry-maxtimeout 120000 \
    && npm config set fetch-retries 5 \
    && npm config set prefer-offline true

# تثبيت التبعيات مع التخزين المؤقت
RUN --mount=type=cache,target=/npm-cache \
    npm ci --prefer-offline --no-audit || \
    npm install --prefer-offline --no-audit

# مرحلة البناء
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules

# نسخ ملفات التكوين
COPY next.config.* ./
COPY tsconfig.json* ./
COPY .env* ./

# نسخ الكود المصدري
COPY public ./public
COPY app ./app
COPY lib ./lib
COPY . .

# إعداد بيئة البناء
ENV NEXT_TELEMETRY_DISABLED=1 \
    NEXT_IGNORE_MISSING_REDIS=1 \
    NEXT_IGNORE_MISSING_DB=1 \
    NEXT_PUBLIC_SKIP_API_ROUTES=1 \
    NEXT_PHASE=build \
    NEXT_SKIP_API_ROUTES=1 \
    PRISMA_SKIP_POSTINSTALL_GENERATE=true

# إنشاء ملف .env للبناء
RUN { [ -f .env.production ] && cat .env.production || : ; } > .env && \
    echo "DATABASE_URL=\"mysql://placeholder:placeholder@localhost:3306/placeholder\"" >> .env && \
    echo "REDIS_URL=\"redis://dummy:6379\"" >> .env

# إعداد مجلدات البناء
RUN mkdir -p /app/.next/cache

# توليد عميل Prisma
RUN --mount=type=cache,target=/npm-cache \
    npx prisma generate

# بناء التطبيق
RUN --mount=type=cache,target=/app/.next/cache \
    npm run build:production

# مرحلة التشغيل
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production \
    NEXT_TELEMETRY_DISABLED=1 \
    PORT=4000 \
    HOSTNAME="0.0.0.0"

# إعداد مستخدم غير جذري
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 --ingroup nodejs nextjs && \
    mkdir -p /app/public /app/.next /app/src && \
    chown nextjs:nodejs /app/.next /app/public /app/src

# نسخ الملفات المبنية
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/prisma ./prisma
COPY --from=builder --chown=nextjs:nodejs /app/node_modules/.prisma ./node_modules/.prisma

USER nextjs

EXPOSE 4000

CMD ["sh", "-c", \
    "npx prisma generate && \
    if [ -f server.js ]; then \
        node server.js; \
    else \
        node node_modules/next/dist/bin/next start -p 4000; \
    fi"]