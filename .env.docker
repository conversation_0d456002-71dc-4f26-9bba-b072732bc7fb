# متغيرات البيئة للتشغيل باستخدام Docker
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
NEXT_IGNORE_MISSING_REDIS=1
NEXT_IGNORE_MISSING_DB=1
NEXT_PUBLIC_SKIP_API_ROUTES=1

# متغيرات قاعدة البيانات
MYSQL_ROOT_PASSWORD=root_password
MYSQL_DATABASE=assistantbb
MYSQL_USER=assistantbb_user
MYSQL_PASSWORD=assistantbb_password
DATABASE_URL=mysql://assistantbb_user:assistantbb_password@db:3306/assistantbb

# متغيرات Redis
REDIS_URL=redis://redis:6379

# متغيرات API
UPDATE_SECRET_KEY=your_update_secret_key
REFRESH_SECRET_KEY=your_refresh_secret_key
INIT_DB_SECRET_KEY=your_init_db_secret_key
ALIEXPRESS_APP_KEY=your_aliexpress_app_key
ALIEXPRESS_SECRET_KEY=your_aliexpress_secret_key
ALIEXPRESS_TAG_ID=your_aliexpress_tag_id