// Translation test script
const fs = require('fs');
const path = require('path');

// Import translation files
const ar = require('./app/translations/ar.js').default;
const en = require('./app/translations/en.js').default;
const es = require('./app/translations/es.js').default;
const fr = require('./app/translations/fr.js').default;
const uk = require('./app/translations/uk.js').default;

const translations = { ar, en, es, fr, uk };

// Test keys that should exist in all languages
const testKeys = [
  'footer.about',
  'footer.aboutText',
  'footer.links',
  'footer.contact',
  'footer.faq',
  'footer.privacyPolicy',
  'footer.terms',
  'footer.allRightsReserved',
  'auth.register.title',
  'auth.register.emailPlaceholder',
  'auth.register.passwordPlaceholder',
  'auth.register.confirmPasswordPlaceholder',
  'auth.register.registerButton',
  'auth.register.hasAccount',
  'auth.register.loginHere',
  'common.labels.email',
  'common.labels.password',
  'common.labels.confirmPassword',
  'common.errors.required',
  'common.errors.invalidEmail',
  'common.errors.passwordMismatch',
  'common.messages.created',
  'common.messages.processing',
  'common.errors.serverError'
];

function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current && current[key], obj);
}

function testTranslations() {
  console.log('🔍 Testing translations...\n');
  
  let allPassed = true;
  
  for (const key of testKeys) {
    console.log(`Testing key: ${key}`);
    
    for (const [lang, translation] of Object.entries(translations)) {
      const value = getNestedValue(translation, key);
      if (!value) {
        console.log(`  ❌ Missing in ${lang}: ${key}`);
        allPassed = false;
      } else {
        console.log(`  ✅ ${lang}: ${value}`);
      }
    }
    console.log('');
  }
  
  if (allPassed) {
    console.log('🎉 All translations are complete!');
  } else {
    console.log('⚠️  Some translations are missing.');
  }
}

testTranslations();