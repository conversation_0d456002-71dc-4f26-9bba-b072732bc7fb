const fs = require('fs');
const { URL } = require('url');

/**
 * توليد رابط قاعدة البيانات (DATABASE_URL) من المتغيرات المنفصلة أو استخدام الرابط الموجود
 * يدعم كلا من DATABASE_URL والمتغيرات المنفصلة (DB_HOST, DB_USER, DB_PASSWORD, DB_NAME)
 * 
 * @returns {string} رابط قاعدة البيانات
 */
function generateDatabaseUrl() {
  // التحقق أولاً من وجود DATABASE_URL
  if (process.env.DATABASE_URL) {
    console.log('تم العثور على DATABASE_URL في متغيرات البيئة');
    return process.env.DATABASE_URL;
  }

  // إذا لم يكن DATABASE_URL موجوداً، نستخدم المتغيرات المنفصلة
  const requiredVars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME'];
  const missingVars = requiredVars.filter(v => !process.env[v]);
  
  if (missingVars.length > 0) {
    throw new Error(`متغيرات البيئة المطلوبة مفقودة: ${missingVars.join(', ')}`);
  }

  // ترميز المكونات بشكل آمن
  const encode = (str) => encodeURIComponent(str);
  const host = process.env.DB_HOST;
  const port = process.env.DB_PORT || '3306';
  const user = encode(process.env.DB_USER);
  const password = encode(process.env.DB_PASSWORD);
  const database = encode(process.env.DB_NAME);

  // إنشاء URL مع معاملات إضافية
  const url = new URL(`mysql://${user}:${password}@${host}:${port}/${database}`);
  url.searchParams.append('connection_limit', '5');
  url.searchParams.append('pool_timeout', '10');
  url.searchParams.append('schema', 'public');

  return url.toString();
}

// الاستخدام الرئيسي
try {
  // تحقق من وجود متغير SKIP_DB_URL_GENERATION لتجاهل توليد سلسلة الاتصال في بيئة التطوير
  if (process.env.SKIP_DB_URL_GENERATION === '1') {
    console.log('تم تجاهل توليد سلسلة الاتصال بناءً على متغير SKIP_DB_URL_GENERATION');
    // استخدام القيمة الموجودة في DATABASE_URL أو قيمة افتراضية
    const defaultUrl = process.env.DATABASE_URL || "mysql://placeholder:placeholder@localhost:3306/placeholder";
    module.exports = defaultUrl;
    return;
  }

  const dbUrl = generateDatabaseUrl();
  
  // تعيين DATABASE_URL إذا لم يكن موجوداً
  if (!process.env.DATABASE_URL) {
    process.env.DATABASE_URL = dbUrl;
    console.log('تم تعيين DATABASE_URL في متغيرات البيئة');
  }

  // للاستخدام المحلي فقط (لا تفعل هذا في الإنتاج)
  if (process.env.NODE_ENV === 'development') {
    try {
      const envPath = '.env';
      let envContent = fs.existsSync(envPath) ? fs.readFileSync(envPath, 'utf8') : '';
      
      if (!envContent.includes('DATABASE_URL=')) {
        envContent += `\nDATABASE_URL="${dbUrl}"\n`;
        fs.writeFileSync(envPath, envContent);
        console.log('تم تحديث ملف .env للتنمية المحلية');
      }
    } catch (error) {
      console.warn('تحذير: لم يتم تحديث ملف .env:', error.message);
    }
  }
  
  module.exports = dbUrl;
} catch (error) {
  console.error('خطأ في إنشاء سلسلة الاتصال:', error.message);
  
  // إذا كان في بيئة التطوير وفشل إنشاء سلسلة الاتصال، استخدم قيمة افتراضية بدلاً من الخروج
  if (process.env.NODE_ENV === 'development') {
    console.log('استخدام سلسلة اتصال افتراضية لبيئة التطوير');
    const defaultUrl = "mysql://placeholder:placeholder@localhost:3306/placeholder";
    process.env.DATABASE_URL = defaultUrl;
    module.exports = defaultUrl;
  } else {
    process.exit(1);
  }
}