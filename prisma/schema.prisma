// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// نموذج المسؤول (Admin)
model Admin {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  password  String   // سيتم تخزينها مشفرة
  token     String   // التوكن الثابت للمصادقة الإضافية
  name      String?
  role      String   @default("admin")
  lastLogin DateTime?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// نموذج المستخدم (User)
model User {
  id             Int             @id @default(autoincrement())
  email          String          @unique
  password       String?         // سيتم تخزينها مشفرة
  name           String?
  isActive       Boolean         @default(true)
  lastLogin      DateTime?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  searches       Search[]
  userPreferences UserPreference?
}

// نموذج البحث (Search)
model Search {
  id        Int      @id @default(autoincrement())
  query     String
  userId    Int?
  user      User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  createdAt DateTime @default(now())
  results   Json?    // نتائج البحث كـ JSON
}

// نموذج تفضيلات المستخدم (UserPreference)
model UserPreference {
  id           Int      @id @default(autoincrement())
  userId       Int      @unique
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  language     String   @default("ar")
  currency     String   @default("SAR")
  theme        String   @default("light")
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}

// نموذج الإعدادات (Settings)
model Settings {
  id          Int      @id @default(autoincrement())
  key         String   @unique
  value       String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// نموذج اللغات (Language)
model Language {
  id       Int      @id @default(autoincrement())
  code     String   @unique @db.VarChar(10)
  name     String   @db.VarChar(50)
  isActive Boolean  @default(true)
  offers   CachedOffer[]
}

// نموذج العملات (Currency)
model Currency {
  id       Int      @id @default(autoincrement())
  code     String   @unique @db.VarChar(10)
  name     String   @db.VarChar(50)
  symbol   String   @db.VarChar(10)
  isActive Boolean  @default(true)
  offers   CachedOffer[]
}

// نموذج فئات العروض (OfferCategory)
model OfferCategory {
  id          Int      @id @default(autoincrement())
  categoryKey String   @unique @db.VarChar(50)
  arName      String   @db.VarChar(100)
  enName      String   @db.VarChar(100)
  icon        String   @db.VarChar(10)
  color       String   @db.VarChar(20)
  offers      CachedOffer[]
}

// نموذج العروض المخزنة مؤقتاً (CachedOffer)
model CachedOffer {
  id           Int           @id @default(autoincrement())
  categoryKey  String        @db.VarChar(50)
  category     OfferCategory @relation(fields: [categoryKey], references: [categoryKey])
  languageCode String        @db.VarChar(10)
  language     Language      @relation(fields: [languageCode], references: [code])
  currencyCode String        @db.VarChar(10)
  currency     Currency      @relation(fields: [currencyCode], references: [code])
  offersData   Json
  createdAt    DateTime      @default(now())
  expiresAt    DateTime      @default(dbgenerated("(NOW() + INTERVAL 24 HOUR)"))

  @@index([categoryKey, languageCode, currencyCode])
  @@index([expiresAt])
}

// نموذج حالة تحديث العروض (OffersRefreshStatus)
model OffersRefreshStatus {
  id           Int      @id @default(1)
  lastRefresh  DateTime @default(now())
  nextRefresh  DateTime @default(dbgenerated("(NOW() + INTERVAL 24 HOUR)"))
  isRefreshing Boolean  @default(false)
  refreshCount Int      @default(0)
}

// نموذج مفاتيح API (ApiKey)
model ApiKey {
  id          Int      @id @default(autoincrement())
  service     String   // اسم الخدمة (مثل AliExpress)
  keyType     String   // نوع المفتاح (مثل appKey, secretKey)
  keyValue    String   // قيمة المفتاح
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([service, keyType])
}