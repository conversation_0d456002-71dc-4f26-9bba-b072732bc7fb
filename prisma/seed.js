// هذا الملف يستخدم لإنشاء البيانات الأولية في قاعدة البيانات
// يتم تنفيذه عند تشغيل الأمر: npx prisma db seed

import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('بدء إنشاء البيانات الأولية...');

    // 1. إنشاء المسؤول الافتراضي
    const adminEmail = process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'admin123';
    const adminToken = process.env.DEFAULT_ADMIN_TOKEN || 'admin_secure_token';

    // التحقق من وجود المسؤول
    const existingAdmin = await prisma.admin.findUnique({
      where: { email: adminEmail }
    });

    if (!existingAdmin) {
      // تشفير كلمة المرور
      const hashedPassword = await bcrypt.hash(adminPassword, 10);

      // إنشاء المسؤول
      const admin = await prisma.admin.create({
        data: {
          email: adminEmail,
          password: hashedPassword,
          token: adminToken,
          name: 'Admin',
          role: 'admin'
        }
      });

      console.log(`تم إنشاء المسؤول الافتراضي: ${admin.email}`);
    } else {
      console.log(`المسؤول موجود بالفعل: ${existingAdmin.email}`);
    }

    // 2. إنشاء إعدادات افتراضية
    const defaultSettings = [
      { key: 'site_name', value: 'مساعد التسوق', description: 'اسم الموقع' },
      { key: 'site_description', value: 'موقع مساعد التسوق - أفضل العروض والتخفيضات', description: 'وصف الموقع' },
      { key: 'default_currency', value: 'SAR', description: 'العملة الافتراضية' },
      { key: 'default_language', value: 'ar', description: 'اللغة الافتراضية' },
      { key: 'maintenance_mode', value: 'false', description: 'وضع الصيانة' },
      { key: 'enable_registration', value: 'true', description: 'تفعيل التسجيل' }
    ];

    for (const setting of defaultSettings) {
      const existingSetting = await prisma.settings.findUnique({
        where: { key: setting.key }
      });

      if (!existingSetting) {
        await prisma.settings.create({
          data: setting
        });
        console.log(`تم إنشاء الإعداد: ${setting.key}`);
      } else {
        console.log(`الإعداد موجود بالفعل: ${setting.key}`);
      }
    }

    // 3. إنشاء اللغات المدعومة
    const languages = [
      { code: 'ar', name: 'العربية' },
      { code: 'en', name: 'English' },
      { code: 'fr', name: 'Français' },
      { code: 'es', name: 'Español' },
      { code: 'uk', name: 'Українська' }
    ];

    for (const lang of languages) {
      const existingLanguage = await prisma.language.findUnique({
        where: { code: lang.code }
      });

      if (!existingLanguage) {
        await prisma.language.create({
          data: lang
        });
        console.log(`تم إنشاء اللغة: ${lang.name}`);
      } else {
        console.log(`اللغة موجودة بالفعل: ${lang.name}`);
      }
    }

    // 4. إنشاء العملات المدعومة
    const currencies = [
      { code: 'USD', name: 'US Dollar', symbol: '$' },
      { code: 'EUR', name: 'Euro', symbol: '€' },
      { code: 'SAR', name: 'Saudi Riyal', symbol: 'ر.س' },
      { code: 'GBP', name: 'British Pound', symbol: '£' },
      { code: 'EGP', name: 'Egyptian Pound', symbol: 'ج.م' },
      { code: 'UAH', name: 'Ukrainian Hryvnia', symbol: '₴' }
    ];

    for (const currency of currencies) {
      const existingCurrency = await prisma.currency.findUnique({
        where: { code: currency.code }
      });

      if (!existingCurrency) {
        await prisma.currency.create({
          data: currency
        });
        console.log(`تم إنشاء العملة: ${currency.name}`);
      } else {
        console.log(`العملة موجودة بالفعل: ${currency.name}`);
      }
    }

    // 5. إنشاء فئات العروض
    const categories = [
      { categoryKey: 'top_rated', arName: 'الأعلى تقييماً', enName: 'Top Rated', icon: '⭐', color: '#3498db' },
      { categoryKey: 'best_sellers', arName: 'الأكثر مبيعاً', enName: 'Best Sellers', icon: '🔥', color: '#e74c3c' },
      { categoryKey: 'discounted', arName: 'خصومات كبيرة', enName: 'Big Discounts', icon: '💰', color: '#2ecc71' },
      { categoryKey: 'new_arrivals', arName: 'وصل حديثاً', enName: 'New Arrivals', icon: '🆕', color: '#9b59b6' },
      { categoryKey: 'premium', arName: 'منتجات مميزة', enName: 'Premium Products', icon: '👑', color: '#f39c12' }
    ];

    for (const category of categories) {
      const existingCategory = await prisma.offerCategory.findUnique({
        where: { categoryKey: category.categoryKey }
      });

      if (!existingCategory) {
        await prisma.offerCategory.create({
          data: category
        });
        console.log(`تم إنشاء فئة العروض: ${category.arName}`);
      } else {
        console.log(`فئة العروض موجودة بالفعل: ${category.arName}`);
      }
    }

    // 6. إنشاء حالة تحديث العروض
    const existingStatus = await prisma.offersRefreshStatus.findUnique({
      where: { id: 1 }
    });

    if (!existingStatus) {
      await prisma.offersRefreshStatus.create({
        data: {
          id: 1,
          lastRefresh: new Date(),
          nextRefresh: new Date(Date.now() + 24 * 60 * 60 * 1000), // بعد 24 ساعة
          isRefreshing: false,
          refreshCount: 0
        }
      });
      console.log('تم إنشاء حالة تحديث العروض');
    } else {
      console.log('حالة تحديث العروض موجودة بالفعل');
    }

    // 7. إنشاء مفاتيح API الافتراضية
    const apiKeys = [
      { service: 'AliExpress', keyType: 'appKey', keyValue: process.env.ALIEXPRESS_APP_KEY || 'your_app_key' },
      { service: 'AliExpress', keyType: 'secretKey', keyValue: process.env.ALIEXPRESS_SECRET_KEY || 'your_secret_key' },
      { service: 'AliExpress', keyType: 'tagId', keyValue: process.env.ALIEXPRESS_TAG_ID || 'your_tag_id' }
    ];

    for (const apiKey of apiKeys) {
      const existingApiKey = await prisma.apiKey.findFirst({
        where: {
          service: apiKey.service,
          keyType: apiKey.keyType
        }
      });

      if (!existingApiKey) {
        await prisma.apiKey.create({
          data: apiKey
        });
        console.log(`تم إنشاء مفتاح API: ${apiKey.service} - ${apiKey.keyType}`);
      } else {
        console.log(`مفتاح API موجود بالفعل: ${apiKey.service} - ${apiKey.keyType}`);
      }
    }

    console.log('تم إنشاء البيانات الأولية بنجاح');
  } catch (error) {
    console.error('حدث خطأ أثناء إنشاء البيانات الأولية:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();