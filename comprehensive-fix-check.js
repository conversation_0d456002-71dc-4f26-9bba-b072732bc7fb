#!/usr/bin/env node

/**
 * Comprehensive Fix Check
 * Checks all the issues mentioned in the task
 */

const fs = require('fs');

function checkTranslationKeys() {
  console.log('🌐 فحص مفاتيح الترجمة...\n');
  
  try {
    const content = fs.readFileSync('./app/translations/ar.js', 'utf8');
    
    const requiredKeys = [
      'admin.users.status.active',
      'admin.settings.aliexpressApiKeys',
      'admin.settings.appKeyHint',
      'admin.settings.secretKeyHint',
      'admin.settings.tagIdHint',
      'admin.settings.saveSettings',
      'admin.settings.apiSettings',
      'admin.analytics.dailyTraffic',
      'analytics.loadingData',
      'analytics.newUsersDaily',
      'analytics.demographics'
    ];
    
    let allKeysPresent = true;
    
    for (const key of requiredKeys) {
      const keyExists = content.includes(key);
      const status = keyExists ? '✅' : '❌';
      console.log(`${status} ${key}: ${keyExists ? 'موجود' : 'غير موجود'}`);
      
      if (!keyExists) {
        allKeysPresent = false;
      }
    }
    
    return allKeysPresent;
  } catch (error) {
    console.log('❌ خطأ في فحص ملف الترجمة:', error.message);
    return false;
  }
}

function checkCSSIssues() {
  console.log('\n🎨 فحص مشاكل CSS...\n');
  
  try {
    const cssPath = './app/admin-panel-95/admin-styles.css';
    const cssContent = fs.readFileSync(cssPath, 'utf8');
    
    // Check for specific CSS issues
    const issues = [];
    
    // Check for missing dots before class names
    const missingDotPattern = /^[a-zA-Z-]+\s*\{/gm;
    const missingDots = cssContent.match(missingDotPattern);
    
    if (missingDots) {
      issues.push(`قواعد CSS بدون نقطة: ${missingDots.length}`);
    }
    
    // Check for empty rulesets
    const emptyRulesetPattern = /[^{}]*\{\s*\}/g;
    const emptyRulesets = cssContent.match(emptyRulesetPattern);
    
    if (emptyRulesets && emptyRulesets.length > 0) {
      issues.push(`قواعد CSS فارغة: ${emptyRulesets.length}`);
    }
    
    // Check for specific logout button class
    const hasLogoutButton = cssContent.includes('.logout-button {');
    if (!hasLogoutButton) {
      issues.push('قاعدة .logout-button مفقودة أو خاطئة');
    }
    
    if (issues.length === 0) {
      console.log('✅ لا توجد مشاكل في CSS');
      return true;
    } else {
      console.log('❌ مشاكل CSS موجودة:');
      issues.forEach(issue => console.log(`   - ${issue}`));
      return false;
    }
  } catch (error) {
    console.log('❌ خطأ في فحص ملف CSS:', error.message);
    return false;
  }
}

function checkReactErrors() {
  console.log('\n⚛️ فحص أخطاء React...\n');
  
  try {
    const analyticsPath = './app/admin-panel-95/analytics/page.js';
    const content = fs.readFileSync(analyticsPath, 'utf8');
    
    // Check for String() wrapping around object properties
    const hasStringWrapping = content.includes('String(source.name)') && 
                             content.includes('String(category.name)') && 
                             content.includes('String(demographic.name)');
    
    if (hasStringWrapping) {
      console.log('✅ تم إصلاح أخطاء React - تم تحويل الكائنات إلى نصوص');
      return true;
    } else {
      console.log('❌ أخطاء React لم يتم إصلاحها - الكائنات لا تزال تُعرض مباشرة');
      return false;
    }
  } catch (error) {
    console.log('❌ خطأ في فحص ملف Analytics:', error.message);
    return false;
  }
}

function checkUnifiedTranslationSystem() {
  console.log('\n🔄 فحص توحيد نظام الترجمة...\n');
  
  try {
    // Check if all admin pages use the same translation keys
    const adminPages = [
      './app/admin-panel-95/settings/page.js',
      './app/admin-panel-95/analytics/page.js',
      './app/admin-panel-95/users/page.js',
      './app/admin-panel-95/dashboard/page.js'
    ];
    
    let allPagesUseTranslation = true;
    
    for (const pagePath of adminPages) {
      if (fs.existsSync(pagePath)) {
        const content = fs.readFileSync(pagePath, 'utf8');
        const usesTranslation = content.includes('useTranslation') && content.includes("t('");
        
        const pageName = pagePath.split('/').pop().replace('.js', '');
        const status = usesTranslation ? '✅' : '❌';
        console.log(`${status} ${pageName}: ${usesTranslation ? 'يستخدم نظام الترجمة' : 'لا يستخدم نظام الترجمة'}`);
        
        if (!usesTranslation) {
          allPagesUseTranslation = false;
        }
      }
    }
    
    return allPagesUseTranslation;
  } catch (error) {
    console.log('❌ خطأ في فحص نظام الترجمة:', error.message);
    return false;
  }
}

function runComprehensiveCheck() {
  console.log('🔍 الفحص الشامل لجميع المشاك��\n');
  console.log('=' .repeat(50));
  
  const translationCheck = checkTranslationKeys();
  const cssCheck = checkCSSIssues();
  const reactCheck = checkReactErrors();
  const unifiedSystemCheck = checkUnifiedTranslationSystem();
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 النتيجة النهائية:');
  console.log('='.repeat(50));
  
  console.log(`🌐 مفاتيح الترجمة: ${translationCheck ? '✅ مكتملة' : '❌ مفقودة'}`);
  console.log(`🎨 مشاكل CSS: ${cssCheck ? '✅ محلولة' : '❌ موجودة'}`);
  console.log(`⚛️ أخطاء React: ${reactCheck ? '✅ محلولة' : '❌ موجودة'}`);
  console.log(`🔄 نظام الترجمة الموحد: ${unifiedSystemCheck ? '✅ موحد' : '❌ غير موحد'}`);
  
  const allPassed = translationCheck && cssCheck && reactCheck && unifiedSystemCheck;
  
  if (allPassed) {
    console.log('\n🎉 تهانينا! تم حل جميع المشاكل بنجاح');
    console.log('✅ مفاتيح الترجمة مكتملة ومتاحة');
    console.log('✅ مشاكل CSS تم إصلاحها');
    console.log('✅ أخطاء React تم حلها');
    console.log('✅ نظام الترجمة موحد عبر جميع الصفحات');
    
    console.log('\n🚀 الخطوات التال��ة:');
    console.log('1. اختبر صفحات الإدارة في المتصفح');
    console.log('2. تأكد من عرض جميع النصوص بالعربية');
    console.log('3. اختبر وظائف التحليلات والإعدادات');
    
  } else {
    console.log('\n⚠️  لا تزال هناك بعض المشاكل');
    
    if (!translationCheck) {
      console.log('🔧 أضف مفاتيح الترجمة المفقودة إلى ar.js');
    }
    if (!cssCheck) {
      console.log('🔧 أصلح مشاكل CSS في admin-styles.css');
    }
    if (!reactCheck) {
      console.log('🔧 أصلح أخطاء React في analytics/page.js');
    }
    if (!unifiedSystemCheck) {
      console.log('🔧 وحد نظام الترجمة عبر جميع صفحات الإدارة');
    }
  }
  
  console.log('\n📁 الملفات المُحدثة:');
  console.log('- /app/translations/ar.js (مفاتيح ترجمة جديدة)');
  console.log('- /app/admin-panel-95/admin-styles.css (إصلاح CSS)');
  console.log('- /app/admin-panel-95/analytics/page.js (إصلاح React)');
  
  console.log('\n🛠️  أدوات التحقق:');
  console.log('- node comprehensive-fix-check.js (هذا الفحص)');
  console.log('- node translation-validator.js (فحص الترجمات)');
  console.log('- node complete-system-check.js (فحص شامل للنظام)');
  
  const successRate = [translationCheck, cssCheck, reactCheck, unifiedSystemCheck]
    .filter(Boolean).length / 4 * 100;
  
  console.log(`\n📈 معدل النجاح: ${successRate.toFixed(1)}%`);
  
  return allPassed;
}

// Run the check
if (require.main === module) {
  const success = runComprehensiveCheck();
  process.exit(success ? 0 : 1);
}

module.exports = { runComprehensiveCheck };