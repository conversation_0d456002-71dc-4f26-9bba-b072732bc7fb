// @ts-check

/**
 * @type {import('next').NextConfig}
 */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  // تجاهل أخطاء الاتصال بقاعدة البيانات وRedis أثناء البناء
  webpack: (config, { isServer }) => {
    if (process.env.NODE_ENV === 'production') {
      // تجاهل أخطاء Redis في وضع الإنتاج
      config.ignoreWarnings = [
        { module: /node_modules\/@redis\/client\/dist\/lib\/client\/socket\.js/ },
        { module: /node_modules\/@prisma\/client/ },
        { message: /Dynamic server usage/ }
      ];
    }
    return config;
  },
  // تكوين لتجاهل أخطاء الاتصال بقاعدة البيانات وRedis أثناء البناء
  onDemandEntries: {
    // فترة انتظار أطول للصفحات المطلوبة عند الطلب
    maxInactiveAge: 60 * 60 * 1000,
    // عدد الصفحات التي يجب الاحتفاظ بها في الذاكرة
    pagesBufferLength: 5,
  },
  // تكوين لإنشاء نسخة مستقلة للنشر في Docker
  output: 'standalone',
  // زيادة وقت البناء
  staticPageGenerationTimeout: 180,
  // تكوين لتجاهل بناء نقاط النهاية API بشكل ثابت
  experimental: {
    // تمكين وضع الخادم فقط للمسارات الديناميكية
    serverComponentsExternalPackages: ['@prisma/client', 'redis'],
    // تجاهل جميع مسارات API أثناء البناء
    outputFileTracingExcludes: {
      '*': [
        './api/**/*',
      ]
    },
  },
};

export default nextConfig;