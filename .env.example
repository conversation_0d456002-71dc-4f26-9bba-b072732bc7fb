# متغيرات البيئة المثالية
# إعدادات Next.js
NEXT_TELEMETRY_DISABLED=1
NEXT_IGNORE_MISSING_REDIS=1
NEXT_IGNORE_MISSING_DB=1
NEXT_PUBLIC_SKIP_API_ROUTES=1
PORT=4000
HOSTNAME=0.0.0.0
NODE_ENV=production

# إعدادات قاعدة البيانات
DATABASE_URL=mysql://user:password@localhost:3306/dbname

# إعدادات Redis
REDIS_URL=redis://localhost:6379

# إعدادات MySQL (لـ docker-compose)
MYSQL_ROOT_PASSWORD=rootpassword
MYSQL_DATABASE=dbname
MYSQL_USER=user
MYSQL_PASSWORD=password

# مفاتيح الأمان
UPDATE_SECRET_KEY=your_update_secret_key
REFRESH_SECRET_KEY=your_refresh_secret_key
INIT_DB_SECRET_KEY=your_init_db_secret_key