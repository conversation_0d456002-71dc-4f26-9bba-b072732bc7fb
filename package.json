{"name": "wep", "version": "0.1.0", "private": true, "scripts": {"gen-db-url": "node prisma/generate-url.js", "dev": "next dev -p 3000", "build": "prisma generate && next build", "build:production": "NODE_ENV=production prisma generate && next build", "start": "next start -p 4000", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate deploy", "prisma:studio": "prisma studio", "db:push": "prisma db push", "db:seed": "node --experimental-json-modules prisma/seed.js", "db:reset": "prisma migrate reset", "db:deploy": "prisma migrate deploy", "db:init": "npm run prisma:generate && npm run prisma:migrate && npm run db:seed", "postinstall": "if [ -d prisma ] && [ -f prisma/schema.prisma ]; then prisma generate; else echo 'Skipping prisma generate as schema.prisma not found'; fi"}, "prisma": {"seed": "node --experimental-json-modules prisma/seed.js"}, "dependencies": {"@prisma/client": "^5.22.0", "bcryptjs": "^2.4.3", "chart.js": "^4.4.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "mysql2": "^3.14.1", "next": "^14.0.0", "prisma": "^5.22.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.5.2", "redis": "^4.7.1"}, "devDependencies": {}}