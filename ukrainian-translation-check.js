/**
 * Ukrainian Translation Check
 * Checks specifically for admin panel translation completeness in Ukrainian
 */

const fs = require('fs');

// Load Ukrainian translation file
function loadUkrainianTranslations() {
  try {
    // Import the Ukrainian translation file directly
    const ukTranslations = require('./app/translations/uk.js');
    return ukTranslations.default || ukTranslations;
  } catch (error) {
    console.error('Error loading Ukrainian translations:', error.message);
    return null;
  }
}

// Check if a nested key exists
function hasKey(obj, key) {
  const keys = key.split('.');
  let current = obj;
  for (const k of keys) {
    if (current && typeof current === 'object' && k in current) {
      current = current[k];
    } else {
      return false;
    }
  }
  return true;
}

// Main check function
function checkUkrainianAdminTranslations() {
  console.log('🔍 فحص ترجمات صفحة الإدارة الأوكرانية...\n');

  const translations = loadUkrainianTranslations();
  if (!translations) {
    console.log('❌ فشل في تحميل ملف الترجمة الأوكراني');
    return false;
  }

  // Critical admin translation keys
  const adminKeys = [
    'admin.dashboard.title',
    'admin.dashboard.welcome',
    'admin.dashboard.clicks',
    'admin.dashboard.totalClicks',
    'admin.dashboard.estimatedRevenue',
    'admin.dashboard.totalRevenue',
    'admin.dashboard.comparedToPrevious',
    'admin.dashboard.performanceOverview',
    'admin.dashboard.latestActivities',
    'admin.dashboard.mostVisitedProducts',
    'admin.dashboard.viewAll',
    'admin.dashboard.bags',
    'admin.users.title',
    'admin.users.addUser',
    'admin.users.editUser',
    'admin.users.deleteUser',
    'admin.users.name',
    'admin.users.email',
    'admin.users.status',
    'admin.users.actions',
    'admin.users.searchPlaceholder',
    'admin.users.confirmDelete',
    'admin.users.deleteConfirmMessage',
    'admin.users.deleteWarning',
    'admin.users.statuses.active',
    'admin.users.statuses.inactive',
    'admin.users.statuses.banned',
    'admin.analytics.title',
    'admin.analytics.visits',
    'admin.analytics.today',
    'admin.analytics.thisWeek',
    'admin.analytics.thisMonth',
    'admin.analytics.thisYear',
    'admin.analytics.totalUsers',
    'admin.analytics.activeUsers',
    'admin.analytics.newUsers',
    'admin.analytics.revenue',
    'admin.analytics.conversionRate',
    'admin.settings.title',
    'admin.settings.apiSettings',
    'admin.settings.aliexpressApiKeys',
    'admin.settings.saveSettings',
    'admin.settings.saving',
    'admin.settings.testConnection',
    'admin.settings.connectionTest',
    'admin.profile.title',
    'admin.profile.personalInfo',
    'admin.profile.changePassword',
    'admin.profile.saveChanges',
    'admin.profile.passwordChanged',
    'adminPanel.adminLogin.title',
    'adminPanel.adminLogin.subtitle',
    'adminPanel.adminLogin.loginButton',
    'adminPanel.adminLogin.token',
    'adminPanel.adminLogin.tokenPlaceholder',
    'adminPanel.adminLogin.tokenRequired',
    'adminPanel.sidebar.dashboard',
    'adminPanel.sidebar.settings',
    'adminPanel.sidebar.analytics',
    'adminPanel.sidebar.users',
    'adminPanel.sidebar.assistants',
    'adminPanel.sidebar.profile',
    'adminPanel.sidebar.home',
    'adminPanel.sidebar.logout',
    'adminProfile.title',
    'adminProfile.personalInfo',
    'adminProfile.changePassword'
  ];

  const presentKeys = [];
  const missingKeys = [];

  for (const key of adminKeys) {
    if (hasKey(translations, key)) {
      presentKeys.push(key);
    } else {
      missingKeys.push(key);
    }
  }

  // Check admin sections
  const sections = [
    { key: 'admin.dashboard', name: 'لوحة التحكم (admin.dashboard)' },
    { key: 'admin.users', name: 'إدارة المستخدمين (admin.users)' },
    { key: 'admin.analytics', name: 'التحليلات (admin.analytics)' },
    { key: 'admin.settings', name: 'الإعدادات (admin.settings)' },
    { key: 'admin.profile', name: 'الملف الشخصي (admin.profile)' },
    { key: 'adminPanel.adminLogin', name: 'تسجيل دخول الإدارة (adminPanel.adminLogin)' },
    { key: 'adminPanel.sidebar', name: 'الشريط الجانبي (adminPanel.sidebar)' },
    { key: 'adminProfile', name: 'ملف الإدارة الشخصي (adminProfile)' }
  ];

  console.log('🔍 فحص أقسام الإدارة:\n');

  for (const section of sections) {
    const sectionExists = hasKey(translations, section.key);
    const status = sectionExists ? '✅' : '❌';
    console.log(`${status} ${section.name}`);
  }

  console.log('\n📊 نتائج الفحص:');
  console.log(`✅ المفاتيح الموجودة: ${presentKeys.length}`);
  console.log(`❌ المفاتيح المفقودة: ${missingKeys.length}`);

  if (missingKeys.length > 0) {
    console.log('\n❌ المفاتيح المفقودة:');
    missingKeys.forEach(key => console.log(`   - ${key}`));
  }

  const completionRate = (presentKeys.length / adminKeys.length) * 100;
  console.log(`\n📈 نسبة اكتمال الترجمات: ${completionRate.toFixed(1)}%`);

  if (completionRate >= 95) {
    console.log('🎉 ممتاز! صفحة الإدارة الأوكرانية مترجمة بشكل شبه كامل');
  } else if (completionRate >= 80) {
    console.log('👍 جيد! معظم ترجمات صفحة الإدارة الأوكرانية موجودة');
  } else {
    console.log('⚠️ تحتاج إلى إكمال المزيد من الترجمات الأوكرانية');
  }

  return missingKeys.length === 0;
}

// Run the check if this file is executed directly
if (require.main === module) {
  const isComplete = checkUkrainianAdminTranslations();
  process.exit(isComplete ? 0 : 1);
}

module.exports = { checkUkrainianAdminTranslations };