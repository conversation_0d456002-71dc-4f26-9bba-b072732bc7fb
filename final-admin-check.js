#!/usr/bin/env node

/**
 * Final Admin Check
 * Comprehensive check for admin login functionality and CSS issues
 */

const fs = require('fs');

function checkCSSIssues() {
  console.log('🎨 فحص ملف CSS للمشاكل...\n');
  
  try {
    const cssPath = './app/admin-panel-95/admin-styles.css';
    const cssContent = fs.readFileSync(cssPath, 'utf8');
    
    // Check for empty rulesets
    const emptyRulesetPattern = /[^{}]*\{\s*\}/g;
    const emptyRulesets = cssContent.match(emptyRulesetPattern);
    
    if (emptyRulesets && emptyRulesets.length > 0) {
      console.log(`❌ تم العثور على ${emptyRulesets.length} قاعدة CSS فارغة:`);
      emptyRulesets.forEach((rule, index) => {
        console.log(`   ${index + 1}. ${rule.trim()}`);
      });
      return false;
    } else {
      console.log('✅ لا توجد قواعد CSS فارغة');
      return true;
    }
  } catch (error) {
    console.log('❌ خطأ في فحص ملف CSS:', error.message);
    return false;
  }
}

function checkAdminLoginPaths() {
  console.log('\n🔗 فحص مسارات تسجيل الدخول للإدارة...\n');
  
  const paths = [
    {
      path: './app/admin-panel-95/admin-login/page.js',
      name: 'صفحة تسجيل الدخول الصحيحة',
      url: '/admin-panel-95/admin-login'
    },
    {
      path: './app/admin-panel-95/login/page.js',
      name: 'صفحة إعادة التوجيه',
      url: '/admin-panel-95/login'
    }
  ];
  
  let allPathsExist = true;
  
  for (const pathInfo of paths) {
    const exists = fs.existsSync(pathInfo.path);
    const status = exists ? '✅' : '❌';
    console.log(`${status} ${pathInfo.name}: ${exists ? 'موجودة' : 'غير موجودة'}`);
    console.log(`   المسار: ${pathInfo.path}`);
    console.log(`   الرابط: ${pathInfo.url}\n`);
    
    if (!exists) {
      allPathsExist = false;
    }
  }
  
  return allPathsExist;
}

function checkTranslationKeys() {
  console.log('🌐 فحص مفاتيح الترجمة للإدارة...\n');
  
  try {
    const translationPath = './app/translations/ar.js';
    const content = fs.readFileSync(translationPath, 'utf8');
    
    const requiredKeys = [
      'adminPanel.adminLogin.title',
      'adminPanel.adminLogin.subtitle',
      'adminPanel.adminLogin.loginButton',
      'adminPanel.adminLogin.backToSite'
    ];
    
    let allKeysPresent = true;
    
    for (const key of requiredKeys) {
      const keyExists = content.includes(key);
      const status = keyExists ? '✅' : '❌';
      console.log(`${status} ${key}: ${keyExists ? 'موجود' : 'غير موجود'}`);
      
      if (!keyExists) {
        allKeysPresent = false;
      }
    }
    
    return allKeysPresent;
  } catch (error) {
    console.log('❌ خطأ في فحص ملف الترجمة:', error.message);
    return false;
  }
}

function runFinalAdminCheck() {
  console.log('🔍 الفحص النهائي لصفحة الإدارة\n');
  console.log('=' .repeat(50));
  
  const cssCheck = checkCSSIssues();
  const pathsCheck = checkAdminLoginPaths();
  const translationsCheck = checkTranslationKeys();
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 النتيجة النهائية:');
  console.log('='.repeat(50));
  
  console.log(`🎨 ملف CSS: ${cssCheck ? '✅ سليم' : '❌ يحتاج إصلاح'}`);
  console.log(`🔗 مسارات ا��صفحات: ${pathsCheck ? '✅ موجودة' : '❌ مفقودة'}`);
  console.log(`🌐 مفاتيح الترجمة: ${translationsCheck ? '✅ مكتملة' : '❌ مفقودة'}`);
  
  const allPassed = cssCheck && pathsCheck && translationsCheck;
  
  if (allPassed) {
    console.log('\n🎉 تهانينا! جميع الفحوصات نجحت');
    console.log('✅ ملف CSS سليم وخالي من المشاكل');
    console.log('✅ جميع مسارات تسجيل الدخول موجودة');
    console.log('✅ مفاتيح الترجمة مكتملة');
    
    console.log('\n🚀 روابط الوصول:');
    console.log('🔗 الرابط الصحيح: http://localhost:4000/admin-panel-95/admin-login');
    console.log('🔗 الرابط البديل (إعادة توجيه): http://localhost:4000/admin-panel-95/login');
    
    console.log('\n🔑 بيانات تجريبية:');
    console.log('📧 البريد الإلكتروني: <EMAIL>');
    console.log('🔒 كلمة المرور: admin123');
    
  } else {
    console.log('\n⚠️  يوجد بعض المشاكل التي تحتاج إلى إصلاح');
    
    if (!cssCheck) {
      console.log('🔧 أصلح قواعد CSS الفارغة في admin-styles.css');
    }
    if (!pathsCheck) {
      console.log('🔧 تأكد من وجود ملفات صفحات تسجيل الدخول');
    }
    if (!translationsCheck) {
      console.log('🔧 أضف مفاتيح الترجمة المفقودة');
    }
  }
  
  console.log('\n📝 ملاحظات مهمة:');
  console.log('• استخدم الرابط الصحيح: /admin-panel-95/admin-login');
  console.log('• الرابط /admin-panel-95/login/ سيعيد التوجيه تلقائياً');
  console.log('• تأكد من تشغيل الخادم على المنفذ 4000');
  
  return allPassed;
}

// Run the check
if (require.main === module) {
  const success = runFinalAdminCheck();
  process.exit(success ? 0 : 1);
}

module.exports = { runFinalAdminCheck };